import { NextResponse } from 'next/server';
import { verifyToken } from '../../../../../../../../lib/auth';
import { connectDB } from '../../../../../../../../lib/mongodb';
import Course from '../../../../../../../../models/Course';

// GET - جلب درس محدد
export async function GET(request, { params }) {
  try {
    await connectDB();
    
    const { id: courseId, unitId, lessonId } = params;
    
    const course = await Course.findById(courseId);
    if (!course) {
      return NextResponse.json(
        { message: 'الدورة غير موجودة' },
        { status: 404 }
      );
    }

    const unit = course.units.id(unitId);
    if (!unit) {
      return NextResponse.json(
        { message: 'الوحدة غير موجودة' },
        { status: 404 }
      );
    }

    const lesson = unit.lessons.id(lessonId);
    if (!lesson) {
      return NextResponse.json(
        { message: 'الدرس غير موجود' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      lesson,
      unitTitle: unit.title,
      courseTitle: course.title
    });

  } catch (error) {
    console.error('Error fetching lesson:', error);
    return NextResponse.json(
      { message: 'خطأ في جلب الدرس' },
      { status: 500 }
    );
  }
}

// PUT - تحديث درس
export async function PUT(request, { params }) {
  try {
    await connectDB();
    
    // التحقق من صحة التوكن والصلاحيات
    const authResult = await verifyToken(request);
    if (!authResult.success) {
      return NextResponse.json(
        { message: authResult.message },
        { status: 401 }
      );
    }

    const { user } = authResult;
    if (!['admin', 'super-admin'].includes(user.role)) {
      return NextResponse.json(
        { message: 'غير مصرح لك بهذا الإجراء' },
        { status: 403 }
      );
    }

    const { id: courseId, unitId, lessonId } = params;
    const { title, type, content, videoUrl, textContent, duration } = await request.json();

    if (!title) {
      return NextResponse.json(
        { message: 'عنوان الدرس مطلوب' },
        { status: 400 }
      );
    }

    const course = await Course.findById(courseId);
    if (!course) {
      return NextResponse.json(
        { message: 'الدورة غير موجودة' },
        { status: 404 }
      );
    }

    const unit = course.units.id(unitId);
    if (!unit) {
      return NextResponse.json(
        { message: 'الوحدة غير موجودة' },
        { status: 404 }
      );
    }

    const lesson = unit.lessons.id(lessonId);
    if (!lesson) {
      return NextResponse.json(
        { message: 'الدرس غير موجود' },
        { status: 404 }
      );
    }

    // تحديث الدرس
    lesson.title = title;
    lesson.type = type || lesson.type;
    lesson.content = content || '';
    lesson.videoUrl = videoUrl || '';
    lesson.textContent = textContent || '';
    lesson.duration = duration || '';

    course.updatedBy = user._id;
    await course.save();

    return NextResponse.json({
      success: true,
      message: 'تم تحديث الدرس بنجاح',
      lesson
    });

  } catch (error) {
    console.error('Error updating lesson:', error);
    return NextResponse.json(
      { message: 'خطأ في تحديث الدرس' },
      { status: 500 }
    );
  }
}

// DELETE - حذف درس
export async function DELETE(request, { params }) {
  try {
    await connectDB();
    
    // التحقق من صحة التوكن والصلاحيات
    const authResult = await verifyToken(request);
    if (!authResult.success) {
      return NextResponse.json(
        { message: authResult.message },
        { status: 401 }
      );
    }

    const { user } = authResult;
    if (!['admin', 'super-admin'].includes(user.role)) {
      return NextResponse.json(
        { message: 'غير مصرح لك بهذا الإجراء' },
        { status: 403 }
      );
    }

    const { id: courseId, unitId, lessonId } = params;

    const course = await Course.findById(courseId);
    if (!course) {
      return NextResponse.json(
        { message: 'الدورة غير موجودة' },
        { status: 404 }
      );
    }

    const unit = course.units.id(unitId);
    if (!unit) {
      return NextResponse.json(
        { message: 'الوحدة غير موجودة' },
        { status: 404 }
      );
    }

    // حذف الدرس
    unit.lessons.id(lessonId).remove();
    
    // إعادة ترقيم الدروس
    unit.lessons.forEach((lesson, index) => {
      lesson.order = index;
    });

    course.updatedBy = user._id;
    await course.save();

    return NextResponse.json({
      success: true,
      message: 'تم حذف الدرس بنجاح'
    });

  } catch (error) {
    console.error('Error deleting lesson:', error);
    return NextResponse.json(
      { message: 'خطأ في حذف الدرس' },
      { status: 500 }
    );
  }
}
