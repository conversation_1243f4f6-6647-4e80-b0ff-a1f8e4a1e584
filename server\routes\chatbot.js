import express from 'express';
import axios from 'axios';
import { verifyToken } from '../middleware/verifyToken.js';

const router = express.Router();

router.post('/', verifyToken, async (req, res) => {
  try {
    const { message } = req.body;

    if (!message) {
      return res.status(400).json({ message: 'الرسالة مطلوبة' });
    }

    if (!process.env.OPENAI_API_KEY) {
      return res.status(500).json({ message: 'مفتاح OpenAI API غير مُكوَّن' });
    }

    const response = await axios.post(
      'https://api.openai.com/v1/chat/completions',
      {
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'أنت مساعد تعليمي مفيد لمنصة تعلم عبر الإنترنت. ساعد الطلاب في أسئلتهم المتعلقة بالدورات، وقدم نصائح للدراسة، واعرض إرشادات حول المواضيع التقنية. اجعل الردود مختصرة وتعليمية. يرجى الرد باللغة العربية.'
          },
          {
            role: 'user',
            content: message
          }
        ],
        max_tokens: 150,
        temperature: 0.7
      },
      {
        headers: {
          'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
          'Content-Type': 'application/json'
        }
      }
    );

    const botMessage = response.data.choices[0].message.content;
    res.json({ response: botMessage });

  } catch (error) {
    console.error('OpenAI API Error:', error.response?.data || error.message);
    
    if (error.response?.status === 401) {
      return res.status(500).json({ message: 'مفتاح OpenAI API غير صحيح' });
    }
    
    res.status(500).json({ 
      message: 'خدمة الدردشة غير متاحة. يرجى المحاولة مرة أخرى لاحقاً.' 
    });
  }
});

export default router;