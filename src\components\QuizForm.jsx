import React from 'react';

const QuizForm = ({ quiz, onChange }) => {
  const safeQuiz = quiz || { questions: [] };

  const handleQuestionChange = (idx, updated) => {
    const newQuestions = safeQuiz.questions.map((q, i) => (i === idx ? updated : q));
    onChange({ ...safeQuiz, questions: newQuestions });
  };
  const handleAddQuestion = (e) => {
    e.preventDefault();
    e.stopPropagation();
    onChange({ ...safeQuiz, questions: [...safeQuiz.questions, { question: '', options: ['', '', '', ''], correctAnswer: '' }] });
  };
  const handleRemoveQuestion = (idx) => (e) => {
    e.preventDefault();
    e.stopPropagation();
    onChange({ ...safeQuiz, questions: safeQuiz.questions.filter((_, i) => i !== idx) });
  };
  return (
    <div className="border rounded p-2 mt-2">
      <div className="d-flex justify-content-between align-items-center mb-2">
        <span>اختبار نهاية الوحدة</span>
        <button type="button" className="btn btn-outline-primary btn-sm" onClick={handleAddQuestion}>
          إضافة سؤال
        </button>
      </div>
      {safeQuiz.questions.map((q, idx) => (
        <div key={idx} className="border rounded p-2 mb-2">
          <div className="d-flex justify-content-between align-items-center mb-1">
            <span>سؤال {idx + 1}</span>
            <button type="button" className="btn btn-outline-danger btn-sm" onClick={handleRemoveQuestion(idx)}>
              حذف
            </button>
          </div>
          <div className="mb-1">
            <label className="form-label">نص السؤال</label>
            <input
              type="text"
              className="form-control"
              value={q.question}
              onChange={e => handleQuestionChange(idx, { ...q, question: e.target.value })}
              required
            />
          </div>
          <div className="mb-1">
            <label className="form-label">الخيارات</label>
            {q.options.map((opt, oidx) => (
              <input
                key={oidx}
                type="text"
                className="form-control mb-1"
                value={opt}
                onChange={e => {
                  const newOpts = [...q.options];
                  newOpts[oidx] = e.target.value;
                  handleQuestionChange(idx, { ...q, options: newOpts });
                }}
                placeholder={`الخيار ${oidx + 1}`}
                required
              />
            ))}
          </div>
          <div className="mb-1">
            <label className="form-label">الإجابة الصحيحة</label>
            <input
              type="text"
              className="form-control"
              value={q.correctAnswer}
              onChange={e => handleQuestionChange(idx, { ...q, correctAnswer: e.target.value })}
              placeholder="أدخل نص الإجابة الصحيحة"
              required
            />
          </div>
        </div>
      ))}
    </div>
  );
};

export default QuizForm; 