// Environment Configuration
export const ENV_CONFIG = {
  // تحديد البيئة الحالية
  isDevelopment: import.meta.env.DEV || import.meta.env.MODE === 'development',
  isProduction: import.meta.env.PROD || import.meta.env.MODE === 'production',
  
  // API URLs
  API_URLS: {
    development: 'http://localhost:5000',
    production: 'https://academy-project-backend.onrender.com'
  },
  
  // الحصول على URL الصحيح حسب البيئة
  getApiUrl() {
    if (this.isDevelopment) {
      return this.API_URLS.development;
    }
    return this.API_URLS.production;
  },
  
  // معلومات إضافية للتشخيص
  debug: {
    mode: import.meta.env.MODE,
    dev: import.meta.env.DEV,
    prod: import.meta.env.PROD,
    viteApiUrl: import.meta.env.VITE_API_URL
  }
};

// تصدير URL الحالي
export const CURRENT_API_URL = ENV_CONFIG.getApiUrl();

// تصدير معلومات البيئة للتشخيص
export const DEBUG_INFO = ENV_CONFIG.debug;

export default ENV_CONFIG;
