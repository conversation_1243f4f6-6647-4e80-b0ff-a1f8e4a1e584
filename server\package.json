{"name": "educational-platform-server", "version": "1.0.0", "description": "Backend for educational platform", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "multer": "^1.4.5-lts.1", "axios": "^1.6.2"}}