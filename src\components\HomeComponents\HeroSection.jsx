import React from 'react';
import { Container, <PERSON>, <PERSON>, Button, Badge } from 'react-bootstrap';
import { <PERSON> } from 'react-router-dom';
import { 
  Play, Users, BookOpen, Trophy, Globe, Zap, Target, 
  Code, Brain, Rocket, Sparkles, ArrowRight 
} from 'lucide-react';

const HeroSection = ({ isDark, token, animatedStats }) => {
  return (
    <div className="hero-section text-white position-relative">
      {/* Floating Shapes */}
      <div className="floating-shapes">
        <div className="floating-shape">
          <Code size={60} />
        </div>
        <div className="floating-shape">
          <Brain size={80} />
        </div>
        <div className="floating-shape">
          <Rocket size={70} />
        </div>
        <div className="floating-shape">
          <Target size={50} />
        </div>
      </div>

      <Container className="h-100 d-flex align-items-center">
        <Row className="align-items-center w-100">
          <Col lg={6} className="animate-slideInLeft">
            <div className="mb-4">
              <Badge bg="warning" className="mb-3 px-3 py-2 animate-sparkle">
                <Sparkles size={16} className="me-2" />
                منصة التعلم الرائدة
              </Badge>
            </div>
            
            <h1 className="display-3 fw-bold mb-4 animate-fadeInUp">
              اتقن مهارات 
              <span className="text-warning animate-sparkle"> المستقبل </span>
              مع خبراء الصناعة
            </h1>
            
            <p className="lead mb-4 animate-fadeInUp" style={{ animationDelay: '0.2s' }}>
              انضم إلى <strong className="text-warning">{animatedStats.students.toLocaleString()}+</strong> طالب 
              يتعلمون مهارات التكنولوجيا المتطورة من 
              <strong className="text-warning"> {animatedStats.instructors}+ </strong>
              محترف في الصناعة
            </p>

            <div className="d-flex gap-3 mb-4 animate-fadeInUp" style={{ animationDelay: '0.4s' }}>
              <Button 
                as={Link} 
                to="/courses" 
                variant="warning" 
                size="lg"
                className="px-4 py-3 fw-bold animate-pulse"
              >
                <Play size={20} className="me-2" />
                ابدأ التعلم الآن
              </Button>
              
              {!token && (
                <Button 
                  as={Link} 
                  to="/register" 
                  variant="outline-light" 
                  size="lg"
                  className="px-4 py-3"
                >
                  <Users size={20} className="me-2" />
                  انضم مجاناً
                </Button>
              )}
            </div>

            {/* Statistics */}
            <Row className="mt-5 animate-fadeInUp" style={{ animationDelay: '0.6s' }}>
              <Col md={4}>
                <div className="stat-card rounded-3 p-3 text-center mb-3">
                  <h3 className="fw-bold text-warning mb-1">{animatedStats.students.toLocaleString()}+</h3>
                  <small>طالب نشط</small>
                </div>
              </Col>
              <Col md={4}>
                <div className="stat-card rounded-3 p-3 text-center mb-3">
                  <h3 className="fw-bold text-warning mb-1">{animatedStats.courses}+</h3>
                  <small>دورة متخصصة</small>
                </div>
              </Col>
              <Col md={4}>
                <div className="stat-card rounded-3 p-3 text-center mb-3">
                  <h3 className="fw-bold text-warning mb-1">{animatedStats.instructors}+</h3>
                  <small>خبير ومدرب</small>
                </div>
              </Col>
            </Row>
          </Col>

          <Col lg={6} className="text-center animate-slideInRight">
            <div className="position-relative">
              <div className="bg-white bg-opacity-10 rounded-4 p-5 animate-float">
                <div className="mb-4">
                  <BookOpen size={100} className="text-warning animate-pulse" />
                </div>
                <h3 className="fw-bold mb-3">رحلة التعلم تبدأ هنا</h3>
                <p className="mb-4">
                  محتوى متميز • أدوات تفاعلية • مجتمع داعم
                </p>
                
                <div className="d-flex justify-content-center gap-3 mb-4">
                  <div className="text-center">
                    <Trophy size={24} className="text-warning mb-2" />
                    <small>شهادات معتمدة</small>
                  </div>
                  <div className="text-center">
                    <Globe size={24} className="text-info mb-2" />
                    <small>تعلم من أي مكان</small>
                  </div>
                  <div className="text-center">
                    <Zap size={24} className="text-success mb-2" />
                    <small>تعلم سريع</small>
                  </div>
                </div>

                <Button 
                  as={Link} 
                  to="/courses" 
                  variant="outline-light" 
                  className="px-4 py-2"
                >
                  استكشف الدورات
                  <ArrowRight size={16} className="ms-2" />
                </Button>
              </div>
            </div>
          </Col>
        </Row>
      </Container>
    </div>
  );
};

export default HeroSection;
