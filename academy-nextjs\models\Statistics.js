import mongoose from 'mongoose';

const statisticsSchema = new mongoose.Schema({
  // إحصائيات عامة للنظام
  totalUsers: { type: Number, default: 0 },
  totalStudents: { type: Number, default: 0 },
  totalAdmins: { type: Number, default: 0 },
  totalCourses: { type: Number, default: 0 },
  activeCourses: { type: Number, default: 0 },
  totalEnrollments: { type: Number, default: 0 },
  completedEnrollments: { type: Number, default: 0 },
  
  // إحصائيات شهرية
  monthlyStats: {
    newUsers: { type: Number, default: 0 },
    newEnrollments: { type: Number, default: 0 },
    completedCourses: { type: Number, default: 0 },
    totalRevenue: { type: Number, default: 0 }
  },
  
  // إحصائيات أسبوعية
  weeklyStats: {
    activeUsers: { type: Number, default: 0 },
    newEnrollments: { type: Number, default: 0 },
    studyHours: { type: Number, default: 0 }
  },
  
  // إحصائيات يومية
  dailyStats: {
    activeUsers: { type: Number, default: 0 },
    newUsers: { type: Number, default: 0 },
    completedLessons: { type: Number, default: 0 }
  },
  
  // تاريخ آخر تحديث
  lastUpdated: { type: Date, default: Date.now },
  
  // فترة الإحصائية
  period: {
    type: String,
    enum: ['daily', 'weekly', 'monthly', 'yearly'],
    default: 'daily'
  },
  
  // تاريخ الإحصائية
  date: { type: Date, default: Date.now }
}, {
  timestamps: true
});

// فهارس
statisticsSchema.index({ date: -1, period: 1 });
statisticsSchema.index({ lastUpdated: -1 });

// دالة لحساب الإحصائيات
statisticsSchema.statics.calculateStats = async function(period = 'daily') {
  const User = mongoose.model('User');
  const Course = mongoose.model('Course');
  const Enrollment = mongoose.model('Enrollment');
  
  try {
    // حساب الإحصائيات العامة
    const totalUsers = await User.countDocuments();
    const totalStudents = await User.countDocuments({ role: 'student' });
    const totalAdmins = await User.countDocuments({ role: { $in: ['admin', 'super-admin'] } });
    const totalCourses = await Course.countDocuments();
    const activeCourses = await Course.countDocuments({ isActive: true });
    const totalEnrollments = await Enrollment.countDocuments();
    const completedEnrollments = await Enrollment.countDocuments({ isCompleted: true });
    
    // حساب الإحصائيات حسب الفترة
    const now = new Date();
    let startDate;
    
    switch (period) {
      case 'daily':
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        break;
      case 'weekly':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'monthly':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'yearly':
        startDate = new Date(now.getFullYear(), 0, 1);
        break;
      default:
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    }
    
    // إحصائيات الفترة
    const newUsers = await User.countDocuments({ 
      createdAt: { $gte: startDate } 
    });
    
    const newEnrollments = await Enrollment.countDocuments({ 
      enrolledAt: { $gte: startDate } 
    });
    
    const completedCourses = await Enrollment.countDocuments({ 
      completedAt: { $gte: startDate } 
    });
    
    const activeUsers = await User.countDocuments({ 
      lastActive: { $gte: startDate } 
    });
    
    // إنشاء أو تحديث الإحصائيات
    const stats = await this.findOneAndUpdate(
      { 
        period,
        date: { 
          $gte: startDate,
          $lt: new Date(startDate.getTime() + 24 * 60 * 60 * 1000)
        }
      },
      {
        totalUsers,
        totalStudents,
        totalAdmins,
        totalCourses,
        activeCourses,
        totalEnrollments,
        completedEnrollments,
        [`${period}Stats`]: {
          newUsers,
          newEnrollments,
          completedCourses,
          activeUsers
        },
        lastUpdated: new Date(),
        date: startDate
      },
      { 
        upsert: true, 
        new: true 
      }
    );
    
    return stats;
  } catch (error) {
    console.error('Error calculating statistics:', error);
    throw error;
  }
};

// دالة للحصول على أحدث الإحصائيات
statisticsSchema.statics.getLatestStats = async function(period = 'daily') {
  return this.findOne({ period }).sort({ date: -1 });
};

export default mongoose.models.Statistics || mongoose.model('Statistics', statisticsSchema);
