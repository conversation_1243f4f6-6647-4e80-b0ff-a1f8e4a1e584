import React, { useState } from 'react';
import { Button, Dropdown, Modal } from 'react-bootstrap';
import { 
  Edit, 
  Trash2, 
  Eye, 
  EyeOff, 
  Power, 
  MoreVertical,
  AlertTriangle 
} from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import { coursesAPI } from '../services/api';

const CourseActions = ({ course, onUpdate }) => {
  const { isSuperAdmin } = useAuth();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showToggleModal, setShowToggleModal] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleToggleStatus = async () => {
    setLoading(true);
    try {
      await coursesAPI.update(course._id, {
        ...course,
        isActive: !course.isActive
      });
      setShowToggleModal(false);
      onUpdate && onUpdate();
    } catch (error) {
      console.error('Error toggling course status:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    setLoading(true);
    try {
      await coursesAPI.delete(course._id);
      setShowDeleteModal(false);
      onUpdate && onUpdate();
    } catch (error) {
      console.error('Error deleting course:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <div className="d-flex gap-2">
        {/* View Course - Available to all admins */}
        <Button
          variant="outline-primary"
          size="sm"
          href={`/courses/${course._id}`}
          target="_blank"
          title="عرض الدورة"
        >
          <Eye size={16} />
        </Button>
        
        {/* Edit Course - Available to all admins */}
        <Button
          variant="outline-warning"
          size="sm"
          href={`/admin/edit-course/${course._id}`}
          title="تعديل الدورة"
        >
          <Edit size={16} />
        </Button>
        
        {/* More Actions Dropdown */}
        <Dropdown>
          <Dropdown.Toggle 
            variant="outline-secondary" 
            size="sm" 
            id={`dropdown-${course._id}`}
            className="d-flex align-items-center"
          >
            <MoreVertical size={16} />
          </Dropdown.Toggle>

          <Dropdown.Menu>
            {/* Toggle Active Status - Available to all admins */}
            <Dropdown.Item 
              onClick={() => setShowToggleModal(true)}
              className="d-flex align-items-center gap-2"
            >
              {course.isActive ? (
                <>
                  <EyeOff size={16} className="text-warning" />
                  إخفاء الدورة
                </>
              ) : (
                <>
                  <Power size={16} className="text-success" />
                  إظهار الدورة
                </>
              )}
            </Dropdown.Item>
            
            {/* Delete Course - Super Admin Only */}
            {isSuperAdmin && (
              <>
                <Dropdown.Divider />
                <Dropdown.Item 
                  onClick={() => setShowDeleteModal(true)}
                  className="d-flex align-items-center gap-2 text-danger"
                >
                  <Trash2 size={16} />
                  حذف نهائياً
                </Dropdown.Item>
              </>
            )}
          </Dropdown.Menu>
        </Dropdown>
      </div>

      {/* Toggle Status Modal */}
      <Modal show={showToggleModal} onHide={() => setShowToggleModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>
            {course.isActive ? 'إخفاء الدورة' : 'إظهار الدورة'}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="d-flex align-items-start gap-3">
            <div className="text-warning">
              <AlertTriangle size={24} />
            </div>
            <div>
              <p className="mb-2">
                هل تريد {course.isActive ? 'إخفاء' : 'إظهار'} الدورة "{course.title}"؟
              </p>
              <small className="text-muted">
                {course.isActive 
                  ? 'سيتم إخفاء الدورة من الطلاب ولكن ستبقى البيانات محفوظة. يمكنك إظهارها مرة أخرى في أي وقت.'
                  : 'سيتم إظهار الدورة للطلاب مرة أخرى وستصبح متاحة للتسجيل.'
                }
              </small>
            </div>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button 
            variant="secondary" 
            onClick={() => setShowToggleModal(false)}
            disabled={loading}
          >
            إلغاء
          </Button>
          <Button 
            variant={course.isActive ? "warning" : "success"} 
            onClick={handleToggleStatus}
            disabled={loading}
          >
            {loading ? 'جاري التحديث...' : (course.isActive ? 'إخفاء الدورة' : 'إظهار الدورة')}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Delete Confirmation Modal - Super Admin Only */}
      {isSuperAdmin && (
        <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)}>
          <Modal.Header closeButton>
            <Modal.Title className="text-danger">حذف الدورة نهائياً</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <div className="alert alert-danger">
              <div className="d-flex align-items-start gap-3">
                <div className="text-danger">
                  <AlertTriangle size={24} />
                </div>
                <div>
                  <strong>تحذير: هذا الإجراء لا يمكن التراجع عنه!</strong>
                  <p className="mb-0 mt-2">
                    سيتم حذف الدورة "{course.title}" وجميع البيانات المرتبطة بها نهائياً من النظام.
                  </p>
                </div>
              </div>
            </div>
            <p className="mb-0">
              هل أنت متأكد من أنك تريد المتابعة؟
            </p>
          </Modal.Body>
          <Modal.Footer>
            <Button 
              variant="secondary" 
              onClick={() => setShowDeleteModal(false)}
              disabled={loading}
            >
              إلغاء
            </Button>
            <Button 
              variant="danger" 
              onClick={handleDelete}
              disabled={loading}
            >
              {loading ? 'جاري الحذف...' : 'حذف نهائياً'}
            </Button>
          </Modal.Footer>
        </Modal>
      )}
    </>
  );
};

export default CourseActions;
