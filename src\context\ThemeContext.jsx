import React, { createContext, useState, useContext, useEffect } from 'react';

// إنشاء السياق
const ThemeContext = createContext();

// هوك مخصص للوصول للسياق
export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

// المزوّد
export const ThemeProvider = ({ children }) => {
  // حالة الثيم: افتراضيًا يكون الوضع الفاتح
  const [isDark, setIsDark] = useState(() => {
  const savedTheme = localStorage.getItem('theme');
  if (savedTheme) return savedTheme === 'dark';
  return window.matchMedia('(prefers-color-scheme: dark)').matches;
});


  // تحديث الـ DOM والـ localStorage عند تغيّر الثيم
  useEffect(() => {
    const theme = isDark ? 'dark' : 'light';
    localStorage.setItem('theme', theme);
    document.body.setAttribute('data-bs-theme', theme);
    document.body.className = theme === 'dark' ? 'bg-dark text-light' : 'bg-light text-dark';
    document.documentElement.style.transition = 'background-color 0.3s, color 0.3s';
  }, [isDark]);

  const toggleTheme = () => setIsDark(prev => !prev);

  const value = {
    isDark,
    toggleTheme,
    theme: isDark ? 'dark' : 'light',
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};
