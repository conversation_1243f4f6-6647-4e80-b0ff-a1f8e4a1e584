# تشخيص مشكلة نموذج إضافة الدورة

## المشاكل المحتملة والحلول

### 1. الحقول المطلوبة
✅ **تم الحل**: إضا<PERSON><PERSON> حقل `instructor` المطلوب في:
- `src/pages/SuperAdmin/SuperAdminAddCourse.jsx`
- البيانات الافتراضية
- النموذج
- التحقق من صحة البيانات

### 2. مسار الاستيراد
✅ **تم الحل**: تصحيح مسار `useTheme` من:
- `../../contexts/ThemeContext` ❌
- إلى `../../context/ThemeContext` ✅

### 3. التشخيص المضاف
✅ **تم إضافة**:
- رسائل console.log في handleSubmit
- تحسين رسائل الخطأ
- التحقق من البيانات قبل الإرسال

### 4. خطوات الاختبار

#### للـ Super Admin:
1. انتقل إلى `/super-admin/add-course`
2. املأ الحقول المطلوبة:
   - العنوان ✅
   - الوصف ✅
   - اسم المدرب ✅ (جديد)
3. اضغط "حفظ الدورة"
4. تحقق من وحدة التحكم للرسائل التشخيصية

#### للـ Admin:
1. انتقل إلى `/admin/add-course`
2. املأ الحقول المطلوبة
3. اضغط "إنشاء دورة"
4. تحقق من وحدة التحكم

### 5. رسائل التشخيص المتوقعة

```javascript
// عند النقر على الزر:
"Form submitted!" // أو "Admin form submitted!"

// البيانات:
"Course data: {title: '...', description: '...', instructor: '...'}"

// قبل الإرسال:
"Sending request..."

// بعد النجاح:
"Response: {...}"
```

### 6. إذا لم تظهر الرسائل
المشكلة قد تكون:
- خطأ JavaScript يمنع تنفيذ الكود
- مشكلة في event handler
- مشكلة في النموذج نفسه

### 7. خطوات إضافية للتشخيص
1. افتح Developer Tools (F12)
2. انتقل إلى Console
3. ابحث عن أي أخطاء JavaScript
4. جرب النقر على الزر ولاحظ الرسائل
