import React from 'react';
import { Card, Row, Col, Badge, ListGroup } from 'react-bootstrap';
import { useAuth } from '../../context/AuthContext';
import { 
  Shield, 
  BookOpen, 
  Users, 
  Plus, 
  Edit, 
  Eye, 
  EyeOff, 
  Trash2, 
  Settings,
  UserCog,
  CheckCircle,
  XCircle
} from 'lucide-react';

const PermissionsOverview = () => {
  const { user, isSuperAdmin , isAdmin } = useAuth();

  const permissions = [
    {
      category: 'إدارة الدورات',
      icon: BookOpen,
      items: [
        { name: 'عرض قائمة الدورات', admin: true, superAdmin: true, icon: Eye },
        { name: 'إضافة دورة جديدة', admin: true, superAdmin: true, icon: Plus },
        { name: 'تعديل بيانات الدورة', admin: true, superAdmin: true, icon: Edit },
        { name: 'إخفاء/إظهار الدورة', admin: true, superAdmin: true, icon: EyeOff },
        { name: 'حذف الدورة نهائياً', admin: false, superAdmin: true, icon: Trash2 },
      ]
    },
    {
      category: 'إدارة المستخدمين',
      icon: Users,
      items: [
        { name: 'عرض قائمة المستخدمين', admin: false, superAdmin: true, icon: Eye },
        { name: 'تغيير أدوار المستخدمين', admin: false, superAdmin: true, icon: UserCog },
        { name: 'حذف المستخدمين', admin: false, superAdmin: true, icon: Trash2 },
      ]
    },
    {
      category: 'إعدادات النظام',
      icon: Settings,
      items: [
        { name: 'الوصول لإعدادات النظام', admin: false, superAdmin: true, icon: Settings },
        { name: 'إدارة صلاحيات المديرين', admin: false, superAdmin: true, icon: Shield },
      ]
    }
  ];

  const PermissionIcon = ({ hasPermission }) => (
    hasPermission ? (
      <CheckCircle size={16} className="text-success" />
    ) : (
      <XCircle size={16} className="text-danger" />
    )
  );

  return (
    <div>
      <div className="mb-4">
        <h2 className="fw-bold">نظرة عامة على الصلاحيات</h2>
        <div className="d-flex align-items-center gap-2">
          <Badge bg={isSuperAdmin ? 'danger' : 'primary'} className="d-flex align-items-center gap-1">
            <Shield size={14} />
            {isSuperAdmin ? 'مدير عام' : 'مدير'}
          </Badge>
          <span className="text-muted">الصلاحيات المتاحة لدورك الحالي</span>
        </div>
      </div>

      <Row className="g-4">
        {permissions.map((category, index) => (
          <Col key={index} lg={4} md={6}>
            <Card className="border-0 shadow-sm h-100">
              <Card.Header className="bg-light border-0">
                <div className="d-flex align-items-center gap-2">
                  <category.icon size={20} className="text-primary" />
                  <h6 className="fw-bold mb-0">{category.category}</h6>
                </div>
              </Card.Header>
              <Card.Body className="p-0">
                <ListGroup variant="flush">
                  {category.items.map((item, itemIndex) => {
                    const hasPermission = isSuperAdmin ? item.superAdmin : item.admin;
                    
                    return (
                      <ListGroup.Item 
                        key={itemIndex}
                        className={`d-flex align-items-center justify-content-between ${
                          hasPermission ? 'bg-light-success' : 'bg-light-danger'
                        }`}
                      >
                        <div className="d-flex align-items-center gap-2">
                          <item.icon size={16} className="text-muted" />
                          <span className={hasPermission ? 'text-dark' : 'text-muted'}>
                            {item.name}
                          </span>
                        </div>
                        <PermissionIcon hasPermission={hasPermission} />
                      </ListGroup.Item>
                    );
                  })}
                </ListGroup>
              </Card.Body>
            </Card>
          </Col>
        ))}
      </Row>

      <Row className="mt-4">
        <Col>
          <Card className="border-0 shadow-sm">
            <Card.Header className="bg-primary text-white">
              <h6 className="fw-bold mb-0">معلومات الحساب</h6>
            </Card.Header>
            <Card.Body>
              <Row>
                <Col md={6}>
                  <div className="mb-3">
                    <strong>الاسم:</strong> {user?.name}
                  </div>
                  <div className="mb-3">
                    <strong>البريد الإلكتروني:</strong> {user?.email}
                  </div>
                </Col>
                <Col md={6}>
                  <div className="mb-3">
                    <strong>الدور:</strong> 
                    <Badge bg={isSuperAdmin ? 'danger' : 'primary'} className="ms-2">
                      {isSuperAdmin ? 'مدير عام' : 'مدير'}
                    </Badge>
                  </div>
                  <div className="mb-3">
                    <strong>تاريخ التسجيل:</strong> {user?.createdAt ? new Date(user.createdAt).toLocaleDateString('ar-SA') : 'غير متاح'}
                  </div>
                </Col>
              </Row>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      <Row className="mt-4">
        <Col>
          <Card className="border-0 shadow-sm">
            <Card.Header className="bg-info text-white">
              <h6 className="fw-bold mb-0">ملاحظات مهمة</h6>
            </Card.Header>
            <Card.Body>
              <ul className="mb-0">
                <li className="mb-2">
                  <strong>المدير العام</strong> لديه جميع الصلاحيات بما في ذلك حذف الدورات والمستخدمين نهائياً
                </li>
                <li className="mb-2">
                  <strong>المدير العادي</strong> يمكنه إدارة الدورات (إضافة، تعديل، إخفاء) ولكن لا يمكنه حذفها نهائياً
                </li>
                <li className="mb-2">
                  إدارة المستخدمين متاحة للمديرين العامين فقط لضمان أمان النظام
                </li>
                <li>
                  جميع العمليات يتم تسجيلها في سجل النظام لأغراض المراجعة والأمان
                </li>
              </ul>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default PermissionsOverview;
