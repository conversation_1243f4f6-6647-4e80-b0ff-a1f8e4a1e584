import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, Form, <PERSON><PERSON>, Alert, Spinner, Row, Col, Badge } from 'react-bootstrap';
import { Save, ArrowLeft, Upload, X, Eye, EyeOff } from 'lucide-react';
import { coursesAPI } from '../../services/api';
import UnitForm from '../../components/UnitForm';

const EditCourse = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    instructor: '',
    duration: '',
    level: '',
    tags: '',
    isActive: true
  });
  
  const [units, setUnits] = useState([]);
  const [videoFile, setVideoFile] = useState(null);
  const [imageFile, setImageFile] = useState(null);
  const [removeVideo, setRemoveVideo] = useState(false);
  const [removeImage, setRemoveImage] = useState(false);
  const [currentVideo, setCurrentVideo] = useState('');
  const [currentImage, setCurrentImage] = useState('');

  // تحميل بيانات الدورة
  useEffect(() => {
    const fetchCourse = async () => {
      try {
        setLoading(true);
        const response = await coursesAPI.getById(id);
        const course = response.data;
        
        setFormData({
          title: course.title || '',
          description: course.description || '',
          instructor: course.instructor || '',
          duration: course.duration || '',
          level: course.level || '',
          tags: course.tags ? course.tags.join(', ') : '',
          isActive: course.isActive
        });
        
        setUnits(course.units || []);
        setCurrentVideo(course.video || '');
        setCurrentImage(course.image || '');
        
      } catch (err) {
        setError('فشل في تحميل بيانات الدورة');
        console.error('Error fetching course:', err);
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchCourse();
    }
  }, [id]);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleVideoChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setVideoFile(file);
      setRemoveVideo(false);
    }
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setImageFile(file);
      setRemoveImage(false);
    }
  };

  const handleRemoveCurrentVideo = () => {
    setRemoveVideo(true);
    setVideoFile(null);
    setCurrentVideo('');
  };

  const handleRemoveCurrentImage = () => {
    setRemoveImage(true);
    setImageFile(null);
    setCurrentImage('');
  };

  const handleAddUnit = () => {
    setUnits([...units, {
      title: '',
      lessons: [],
      quiz: { questions: [] }
    }]);
  };

  const handleUnitChange = (index, updatedUnit) => {
    const newUnits = units.map((unit, i) => i === index ? updatedUnit : unit);
    setUnits(newUnits);
  };

  const handleRemoveUnit = (index) => {
    setUnits(units.filter((_, i) => i !== index));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setSuccess('');
    setSaving(true);

    // التحقق من البيانات المطلوبة
    if (!formData.title || !formData.description || !formData.instructor || !formData.duration || !formData.level) {
      setError('يرجى تعبئة جميع الحقول المطلوبة.');
      setSaving(false);
      return;
    }

    try {
      const tagsArray = formData.tags 
        ? formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0)
        : [];

      // تجهيز FormData
      const formPayload = new FormData();
      formPayload.append('title', formData.title);
      formPayload.append('description', formData.description);
      formPayload.append('instructor', formData.instructor);
      formPayload.append('duration', formData.duration);
      formPayload.append('level', formData.level);
      formPayload.append('tags', tagsArray.join(','));
      formPayload.append('isActive', formData.isActive);

      if (videoFile) {
        formPayload.append('video', videoFile);
      }
      if (removeVideo) {
        formPayload.append('removeVideo', 'true');
      }

      if (imageFile) {
        formPayload.append('image', imageFile);
      }
      if (removeImage) {
        formPayload.append('removeImage', 'true');
      }

      // تنظيف بيانات الوحدات
      const cleanUnits = units.map(unit => ({
        title: unit.title || '',
        lessons: (unit.lessons || []).map(lesson => ({
          title: lesson.title || '',
          type: lesson.type || 'video',
          videoUrl: lesson.videoUrl || '',
          textContent: lesson.textContent || ''
        })),
        quiz: {
          questions: (unit.quiz?.questions || []).map(q => ({
            question: q.question || '',
            options: q.options || ['', '', '', ''],
            correctAnswer: q.correctAnswer || ''
          }))
        }
      }));

      formPayload.append('units', JSON.stringify(cleanUnits));

      const response = await coursesAPI.update(id, formPayload);
      
      setSuccess('تم تحديث الدورة بنجاح!');
      setTimeout(() => {
        navigate('/admin/courses');
      }, 2000);

    } catch (err) {
      console.error('Error updating course:', err);
      setError(err.response?.data?.message || err.message || 'فشل في تحديث الدورة');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
        <Spinner animation="border" role="status">
          <span className="visually-hidden">جاري التحميل...</span>
        </Spinner>
      </div>
    );
  }

  return (
    <div className="container-fluid py-4">
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h2>تعديل الدورة</h2>
        <Button 
          variant="outline-secondary" 
          onClick={() => navigate('/admin/courses')}
          className="d-flex align-items-center"
        >
          <ArrowLeft size={16} className="me-2" />
          العودة للدورات
        </Button>
      </div>

      <Card>
        <Card.Body>
          {error && <Alert variant="danger">{error}</Alert>}
          {success && <Alert variant="success">{success}</Alert>}
          
          <Form onSubmit={handleSubmit}>
            {/* البيانات الأساسية */}
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>عنوان الدورة *</Form.Label>
                  <Form.Control
                    type="text"
                    name="title"
                    value={formData.title}
                    onChange={handleInputChange}
                    placeholder="أدخل عنوان الدورة"
                    required
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>المدرب *</Form.Label>
                  <Form.Control
                    type="text"
                    name="instructor"
                    value={formData.instructor}
                    onChange={handleInputChange}
                    placeholder="اسم المدرب"
                    required
                  />
                </Form.Group>
              </Col>
            </Row>

            <Form.Group className="mb-3">
              <Form.Label>وصف الدورة *</Form.Label>
              <Form.Control
                as="textarea"
                rows={4}
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="وصف مفصل للدورة"
                required
              />
            </Form.Group>

            <Row>
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label>المدة *</Form.Label>
                  <Form.Control
                    type="text"
                    name="duration"
                    value={formData.duration}
                    onChange={handleInputChange}
                    placeholder="مثال: 4 أسابيع"
                    required
                  />
                </Form.Group>
              </Col>
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label>المستوى *</Form.Label>
                  <Form.Select
                    name="level"
                    value={formData.level}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="">اختر المستوى</option>
                    <option value="مبتدئ">مبتدئ</option>
                    <option value="متوسط">متوسط</option>
                    <option value="متقدم">متقدم</option>
                  </Form.Select>
                </Form.Group>
              </Col>
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label>حالة الدورة</Form.Label>
                  <div className="d-flex align-items-center">
                    <Form.Check
                      type="switch"
                      id="isActive"
                      name="isActive"
                      checked={formData.isActive}
                      onChange={handleInputChange}
                      label=""
                    />
                    <Badge bg={formData.isActive ? 'success' : 'secondary'} className="ms-2">
                      {formData.isActive ? 'مفعلة' : 'غير مفعلة'}
                    </Badge>
                  </div>
                </Form.Group>
              </Col>
            </Row>

            <Form.Group className="mb-3">
              <Form.Label>الوسوم (مفصولة بفواصل)</Form.Label>
              <Form.Control
                type="text"
                name="tags"
                value={formData.tags}
                onChange={handleInputChange}
                placeholder="مثال: برمجة, تطوير ويب, جافاسكريبت"
              />
            </Form.Group>

            {/* إدارة الملفات */}
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>فيديو تعريفي</Form.Label>
                  {currentVideo && !removeVideo && (
                    <div className="mb-2 p-2 bg-light rounded">
                      <div className="d-flex justify-content-between align-items-center">
                        <span className="text-muted">فيديو حالي موجود</span>
                        <Button 
                          variant="outline-danger" 
                          size="sm"
                          onClick={handleRemoveCurrentVideo}
                        >
                          <X size={14} />
                        </Button>
                      </div>
                    </div>
                  )}
                  <Form.Control
                    type="file"
                    accept="video/*"
                    onChange={handleVideoChange}
                  />
                  <Form.Text className="text-muted">
                    اختياري - يمكن رفع فيديو تعريفي للدورة
                  </Form.Text>
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>صورة الدورة</Form.Label>
                  {currentImage && !removeImage && (
                    <div className="mb-2 p-2 bg-light rounded">
                      <div className="d-flex justify-content-between align-items-center">
                        <span className="text-muted">صورة حالية موجودة</span>
                        <Button 
                          variant="outline-danger" 
                          size="sm"
                          onClick={handleRemoveCurrentImage}
                        >
                          <X size={14} />
                        </Button>
                      </div>
                    </div>
                  )}
                  <Form.Control
                    type="file"
                    accept="image/*"
                    onChange={handleImageChange}
                  />
                  <Form.Text className="text-muted">
                    اختياري - صورة غلاف للدورة
                  </Form.Text>
                </Form.Group>
              </Col>
            </Row>

            {/* إدارة الوحدات */}
            <div className="mb-4">
              <div className="d-flex justify-content-between align-items-center mb-3">
                <h5>وحدات الدورة</h5>
                <Button variant="outline-primary" onClick={handleAddUnit}>
                  إضافة وحدة
                </Button>
              </div>
              
              {units.length === 0 && (
                <Alert variant="info">
                  لم يتم إضافة أي وحدة بعد. اضغط على "إضافة وحدة" لبدء إنشاء محتوى الدورة.
                </Alert>
              )}
              
              {units.map((unit, index) => (
                <UnitForm
                  key={index}
                  unit={unit}
                  unitIndex={index}
                  onChange={(updatedUnit) => handleUnitChange(index, updatedUnit)}
                  onRemove={() => handleRemoveUnit(index)}
                />
              ))}
            </div>

            {/* أزرار الحفظ */}
            <div className="d-flex gap-3">
              <Button 
                type="submit" 
                variant="primary" 
                disabled={saving}
                className="d-flex align-items-center"
              >
                {saving ? (
                  <>
                    <div className="spinner-border spinner-border-sm me-2" role="status">
                      <span className="visually-hidden">جاري الحفظ...</span>
                    </div>
                    جاري التحديث...
                  </>
                ) : (
                  <>
                    <Save size={16} className="me-2" />
                    حفظ التغييرات
                  </>
                )}
              </Button>
              <Button 
                type="button" 
                variant="outline-secondary"
                onClick={() => navigate('/admin/courses')}
              >
                إلغاء
              </Button>
            </div>
          </Form>
        </Card.Body>
      </Card>
    </div>
  );
};

export default EditCourse;
