'use client';

import { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Badge, Tab, Tabs, ProgressBar } from 'react-bootstrap';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import { 
  Trophy, 
  Medal, 
  Award, 
  Star, 
  TrendingUp, 
  BookOpen, 
  Clock,
  Target,
  Crown,
  Zap
} from 'lucide-react';

export default function Leaderboard() {
  const { user, api } = useAuth();
  const { isDark } = useTheme();
  
  const [activeTab, setActiveTab] = useState('points');
  const [topStudents, setTopStudents] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchLeaderboard();
  }, [activeTab]);

  const fetchLeaderboard = async () => {
    try {
      setLoading(true);

      // جلب بيانات لوحة الشرف من API
      const response = await api.post('/statistics', {
        type: 'leaderboard',
        filters: { limit: 50 }
      });

      const { topStudents } = response.data.data;

      // تحويل البيانات للتنسيق المطلوب
      const formattedStudents = topStudents.map((student, index) => ({
        _id: student._id,
        name: student.name,
        totalPoints: student.totalPoints || 0,
        completedCourses: student.completedCourses || 0,
        achievements: student.achievements || ['طالب نشط'],
        avatar: null,
        currentStreak: Math.floor(Math.random() * 30) + 1, // محاكاة مؤقتة
        totalHours: student.totalStudyHours || 0,
        rank: index + 1
      }));

      setTopStudents(formattedStudents);
    } catch (error) {
      console.error('Error fetching leaderboard:', error);
    } finally {
      setLoading(false);
    }
  };

  const getRankIcon = (rank) => {
    switch (rank) {
      case 1:
        return <Crown size={24} className="text-warning" />;
      case 2:
        return <Medal size={24} className="text-secondary" />;
      case 3:
        return <Award size={24} className="text-warning" />;
      default:
        return <span className="fw-bold text-primary">#{rank}</span>;
    }
  };

  const getRankBadge = (rank) => {
    switch (rank) {
      case 1:
        return <Badge bg="warning" className="me-2">🥇 المركز الأول</Badge>;
      case 2:
        return <Badge bg="secondary" className="me-2">🥈 المركز الثاني</Badge>;
      case 3:
        return <Badge bg="warning" className="me-2">🥉 المركز الثالث</Badge>;
      default:
        return <Badge bg="primary" className="me-2">المركز {rank}</Badge>;
    }
  };

  const sortStudents = (students) => {
    switch (activeTab) {
      case 'points':
        return students.sort((a, b) => b.totalPoints - a.totalPoints);
      case 'courses':
        return students.sort((a, b) => b.completedCourses - a.completedCourses);
      case 'streak':
        return students.sort((a, b) => b.currentStreak - a.currentStreak);
      case 'hours':
        return students.sort((a, b) => b.totalHours - a.totalHours);
      default:
        return students;
    }
  };

  const getTabValue = (student) => {
    switch (activeTab) {
      case 'points':
        return `${student.totalPoints} نقطة`;
      case 'courses':
        return `${student.completedCourses} دورة`;
      case 'streak':
        return `${student.currentStreak} يوم`;
      case 'hours':
        return `${student.totalHours} ساعة`;
      default:
        return '';
    }
  };

  const getTabIcon = () => {
    switch (activeTab) {
      case 'points':
        return <Star size={16} />;
      case 'courses':
        return <BookOpen size={16} />;
      case 'streak':
        return <Zap size={16} />;
      case 'hours':
        return <Clock size={16} />;
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center min-vh-100">
        <div className="spinner-border text-primary" />
      </div>
    );
  }

  const sortedStudents = sortStudents([...topStudents]);

  return (
    <div className={`min-vh-100 ${isDark ? 'bg-dark text-light' : 'bg-light'}`}>
      <Container className="py-5">
        {/* Header */}
        <Row className="mb-5">
          <Col className="text-center">
            <div className="mb-4">
              <Trophy size={64} className="text-warning" />
            </div>
            <h1 className="display-5 fw-bold mb-3">لوحة الشرف</h1>
            <p className="lead text-muted">
              أفضل الطلاب المتميزين في أكاديمية التعلم
            </p>
          </Col>
        </Row>

        {/* Top 3 Students */}
        <Row className="mb-5">
          <Col>
            <Card className={`border-0 shadow-sm ${isDark ? 'bg-dark text-light' : ''}`}>
              <Card.Body className="p-4">
                <h4 className="text-center mb-4">🏆 أفضل 3 طلاب</h4>
                <Row className="text-center">
                  {sortedStudents.slice(0, 3).map((student, index) => (
                    <Col md={4} key={student._id} className="mb-3">
                      <div className={`p-4 rounded ${index === 0 ? 'bg-warning bg-opacity-10' : index === 1 ? 'bg-secondary bg-opacity-10' : 'bg-info bg-opacity-10'}`}>
                        <div className="mb-3">
                          {getRankIcon(index + 1)}
                        </div>
                        <div className="bg-primary rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center" 
                             style={{ width: '60px', height: '60px' }}>
                          <span className="text-white fw-bold">
                            {student.name.split(' ').map(n => n[0]).join('')}
                          </span>
                        </div>
                        <h5 className="fw-bold">{student.name}</h5>
                        <p className="text-muted mb-2">{student.totalPoints} نقطة</p>
                        <div className="d-flex justify-content-center gap-1 flex-wrap">
                          {student.achievements.slice(0, 2).map((achievement, i) => (
                            <Badge key={i} bg="primary" className="small">
                              {achievement}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </Col>
                  ))}
                </Row>
              </Card.Body>
            </Card>
          </Col>
        </Row>

        {/* Leaderboard Tabs */}
        <Row>
          <Col>
            <Card className={`border-0 shadow-sm ${isDark ? 'bg-dark text-light' : ''}`}>
              <Card.Body>
                <Tabs
                  activeKey={activeTab}
                  onSelect={setActiveTab}
                  className="mb-4"
                >
                  <Tab eventKey="points" title={<><Star size={16} className="me-1" />النقاط</>}>
                  </Tab>
                  <Tab eventKey="courses" title={<><BookOpen size={16} className="me-1" />الدورات</>}>
                  </Tab>
                  <Tab eventKey="streak" title={<><Zap size={16} className="me-1" />التتالي</>}>
                  </Tab>
                  <Tab eventKey="hours" title={<><Clock size={16} className="me-1" />الساعات</>}>
                  </Tab>
                </Tabs>

                {/* Leaderboard List */}
                <div className="table-responsive">
                  {sortedStudents.map((student, index) => (
                    <Card key={student._id} className={`mb-3 border-0 ${isDark ? 'bg-secondary' : 'bg-light'}`}>
                      <Card.Body className="p-3">
                        <Row className="align-items-center">
                          <Col xs={1} className="text-center">
                            {getRankIcon(index + 1)}
                          </Col>
                          <Col xs={2}>
                            <div className="d-flex align-items-center">
                              <div className="bg-primary rounded-circle me-3 d-flex align-items-center justify-content-center" 
                                   style={{ width: '40px', height: '40px' }}>
                                <span className="text-white small fw-bold">
                                  {student.name.split(' ').map(n => n[0]).join('')}
                                </span>
                              </div>
                            </div>
                          </Col>
                          <Col xs={3}>
                            <h6 className="mb-1 fw-bold">{student.name}</h6>
                            {getRankBadge(index + 1)}
                          </Col>
                          <Col xs={2} className="text-center">
                            <div className="d-flex align-items-center justify-content-center">
                              {getTabIcon()}
                              <span className="ms-1 fw-bold">{getTabValue(student)}</span>
                            </div>
                          </Col>
                          <Col xs={2} className="text-center">
                            <small className="text-muted">
                              {student.completedCourses} دورة مكتملة
                            </small>
                          </Col>
                          <Col xs={2}>
                            <div className="d-flex gap-1 flex-wrap">
                              {student.achievements.slice(0, 2).map((achievement, i) => (
                                <Badge key={i} bg="outline-primary" className="small">
                                  {achievement}
                                </Badge>
                              ))}
                            </div>
                          </Col>
                        </Row>
                      </Card.Body>
                    </Card>
                  ))}
                </div>

                {/* User's Position */}
                {user && (
                  <Card className={`mt-4 border-primary ${isDark ? 'bg-dark text-light' : ''}`}>
                    <Card.Body className="p-3">
                      <Row className="align-items-center">
                        <Col>
                          <div className="d-flex align-items-center">
                            <Target size={20} className="text-primary me-2" />
                            <span className="fw-bold">موقعك الحالي: </span>
                            <Badge bg="primary" className="ms-2">المركز #15</Badge>
                            <span className="ms-2 text-muted">
                              {user.totalPoints || 850} نقطة
                            </span>
                          </div>
                        </Col>
                      </Row>
                    </Card.Body>
                  </Card>
                )}
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>
    </div>
  );
}
