import React from 'react';
import { Container, Row, Col, Card, Badge } from 'react-bootstrap';
import { 
  BookO<PERSON>, Brain, TrendingUp, Globe, Zap, Users, Trophy, 
  Star, CheckCircle 
} from 'lucide-react';

const FeaturesSection = ({ isDark }) => {
  return (
    <Container className={`section-spacing ${isDark ? 'text-light' : ''}`}>
      <Row className="text-center mb-5 animate-fadeInUp">
        <Col>
          <Badge bg="primary" className="mb-3 px-3 py-2">
            <Star size={16} className="me-2" />
            مميزات استثنائية
          </Badge>
          <h2 className="display-5 fw-bold mb-3">
            لماذا تختار 
            <span className="text-primary"> منصتنا؟</span>
          </h2>
          <p className="lead text-muted">
            كل ما تحتاجه لتطوير مسيرتك المهنية ومهاراتك في مكان واحد
          </p>
        </Col>
      </Row>
      
      <Row className="g-4">
        <Col md={4} className="animate-fadeInUp" style={{ animationDelay: '0.1s' }}>
          <Card className="feature-card h-100 text-center shadow-lg">
            <Card.Body className="p-5">
              <div className="mb-4">
                <div className="d-inline-flex align-items-center justify-content-center rounded-circle bg-primary bg-opacity-10 p-3 mb-3">
                  <BookOpen size={48} className="text-primary animate-pulse" />
                </div>
              </div>
              <Card.Title className="h4 fw-bold mb-3">محتوى يقوده خبراء</Card.Title>
              <Card.Text className="text-muted mb-4">
                تعلم من المحترفين في الصناعة الذين يتمتعون بخبرة عملية
                وخبرة مثبتة في أحدث التقنيات
              </Card.Text>
              <div className="d-flex justify-content-center gap-2">
                <CheckCircle size={16} className="text-success" />
                <small className="text-muted">محتوى محدث باستمرار</small>
              </div>
            </Card.Body>
          </Card>
        </Col>

        <Col md={4} className="animate-fadeInUp" style={{ animationDelay: '0.2s' }}>
          <Card className="feature-card h-100 text-center shadow-lg">
            <Card.Body className="p-5">
              <div className="mb-4">
                <div className="d-inline-flex align-items-center justify-content-center rounded-circle bg-success bg-opacity-10 p-3 mb-3">
                  <Brain size={48} className="text-success animate-pulse" />
                </div>
              </div>
              <Card.Title className="h4 fw-bold mb-3">ذكاء اصطناعي متقدم</Card.Title>
              <Card.Text className="text-muted mb-4">
                تفاعل مع روبوت الدردشة المدعوم بالذكاء الاصطناعي للحصول على 
                مساعدة فورية ودعم تعلم مخصص على مدار الساعة
              </Card.Text>
              <div className="d-flex justify-content-center gap-2">
                <CheckCircle size={16} className="text-success" />
                <small className="text-muted">دعم 24/7</small>
              </div>
            </Card.Body>
          </Card>
        </Col>

        <Col md={4} className="animate-fadeInUp" style={{ animationDelay: '0.3s' }}>
          <Card className="feature-card h-100 text-center shadow-lg">
            <Card.Body className="p-5">
              <div className="mb-4">
                <div className="d-inline-flex align-items-center justify-content-center rounded-circle bg-warning bg-opacity-10 p-3 mb-3">
                  <TrendingUp size={48} className="text-warning animate-pulse" />
                </div>
              </div>
              <Card.Title className="h4 fw-bold mb-3">نمو مهني مضمون</Card.Title>
              <Card.Text className="text-muted mb-4">
                اكتسب المهارات التي تحتاجها لتطوير مسيرتك المهنية مع
                دورات ذات صلة مباشرة بسوق العمل
              </Card.Text>
              <div className="d-flex justify-content-center gap-2">
                <CheckCircle size={16} className="text-success" />
                <small className="text-muted">شهادات معتمدة</small>
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Additional Features Row */}
      <Row className="g-4 mt-4">
        <Col md={3} className="animate-fadeInUp" style={{ animationDelay: '0.4s' }}>
          <div className="text-center p-4">
            <div className="d-inline-flex align-items-center justify-content-center rounded-circle bg-info bg-opacity-10 p-3 mb-3">
              <Globe size={32} className="text-info" />
            </div>
            <h6 className="fw-bold">تعلم من أي مكان</h6>
            <small className="text-muted">وصول كامل عبر جميع الأجهزة</small>
          </div>
        </Col>

        <Col md={3} className="animate-fadeInUp" style={{ animationDelay: '0.5s' }}>
          <div className="text-center p-4">
            <div className="d-inline-flex align-items-center justify-content-center rounded-circle bg-danger bg-opacity-10 p-3 mb-3">
              <Zap size={32} className="text-danger" />
            </div>
            <h6 className="fw-bold">تعلم سريع وفعال</h6>
            <small className="text-muted">منهجية تعلم مبتكرة</small>
          </div>
        </Col>

        <Col md={3} className="animate-fadeInUp" style={{ animationDelay: '0.6s' }}>
          <div className="text-center p-4">
            <div className="d-inline-flex align-items-center justify-content-center rounded-circle bg-purple bg-opacity-10 p-3 mb-3">
              <Users size={32} className="text-purple" />
            </div>
            <h6 className="fw-bold">مجتمع داعم</h6>
            <small className="text-muted">تفاعل مع آلاف المتعلمين</small>
          </div>
        </Col>

        <Col md={3} className="animate-fadeInUp" style={{ animationDelay: '0.7s' }}>
          <div className="text-center p-4">
            <div className="d-inline-flex align-items-center justify-content-center rounded-circle bg-success bg-opacity-10 p-3 mb-3">
              <Trophy size={32} className="text-success" />
            </div>
            <h6 className="fw-bold">إنجازات وجوائز</h6>
            <small className="text-muted">نظام تحفيزي متقدم</small>
          </div>
        </Col>
      </Row>
    </Container>
  );
};

export default FeaturesSection;
