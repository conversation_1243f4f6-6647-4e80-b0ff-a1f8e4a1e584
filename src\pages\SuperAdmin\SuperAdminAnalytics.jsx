import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>, Card, Badge, Table, Al<PERSON>, <PERSON><PERSON>, But<PERSON> } from 'react-bootstrap';
import { useTheme } from '../../context/ThemeContext';
import { supedAdminAPI } from '../../services/api';
import { 
  TrendingUp, 
  TrendingDown,
  Users, 
  BookOpen, 
  UserCheck, 
  Activity,
  BarChart3,
  PieChart,
  Calendar,
  Download,
  RefreshCw,
  Target,
  Award,
  Clock
} from 'lucide-react';

const SuperAdminAnalytics = () => {
  const { isDark } = useTheme();
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchAnalytics();
  }, []);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      const response = await supedAdminAPI.getAdvancedAnalytics();
      setAnalytics(response.data);
    } catch (err) {
      setError('فشل في تحميل التحليلات');
      console.error('Error fetching analytics:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchAnalytics();
    setRefreshing(false);
  };

  if (loading) {
    return (
      <div className="text-center py-5">
        <Spinner animation="border" variant="primary" />
        <p className="mt-3">جاري تحميل التحليلات المتقدمة...</p>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="danger">
        <Activity size={20} className="me-2" />
        {error}
      </Alert>
    );
  }

  // Mock data for demonstration
  const mockAnalytics = {
    overview: {
      totalUsers: 1250,
      activeUsers: 890,
      totalCourses: 45,
      completedCourses: 234,
      totalRevenue: 125000,
      monthlyGrowth: 15.5
    },
    userEngagement: {
      dailyActiveUsers: [120, 135, 142, 158, 165, 172, 180],
      weeklyActiveUsers: [450, 480, 520, 560, 590, 620, 650],
      averageSessionTime: '25 دقيقة',
      bounceRate: '12%'
    },
    coursePerformance: [
      { name: 'تطوير الويب', enrollments: 320, completions: 245, rating: 4.8 },
      { name: 'تصميم الجرافيك', enrollments: 280, completions: 210, rating: 4.6 },
      { name: 'التسويق الرقمي', enrollments: 250, completions: 180, rating: 4.7 },
      { name: 'إدارة المشاريع', enrollments: 200, completions: 150, rating: 4.5 },
      { name: 'الذكاء الاصطناعي', enrollments: 180, completions: 120, rating: 4.9 }
    ],
    revenueAnalysis: {
      monthlyRevenue: [8500, 9200, 10100, 11500, 12800, 14200, 15600],
      topPayingCourses: [
        { name: 'الذكاء الاصطناعي', revenue: 45000 },
        { name: 'تطوير الويب', revenue: 38000 },
        { name: 'التسويق الرقمي', revenue: 32000 }
      ]
    }
  };

  const data = analytics || mockAnalytics;

  return (
    <div>
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h4 className="mb-1">التحليلات المتقدمة</h4>
          <p className="text-muted mb-0">تحليل شامل لأداء المنصة والمستخدمين</p>
        </div>
        <div className="d-flex gap-2">
          <Button 
            variant="outline-primary" 
            onClick={handleRefresh}
            disabled={refreshing}
            className="d-flex align-items-center"
          >
            <RefreshCw size={16} className={`me-2 ${refreshing ? 'spin' : ''}`} />
            تحديث
          </Button>
          <Button 
            variant="outline-success"
            className="d-flex align-items-center"
          >
            <Download size={16} className="me-2" />
            تصدير التقرير
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <Row className="mb-4">
        <Col md={6} lg={3} className="mb-3">
          <Card className={`h-100 ${isDark ? 'bg-dark text-light' : 'bg-white'} border-0 shadow-sm`}>
            <Card.Body>
              <div className="d-flex justify-content-between align-items-start">
                <div>
                  <h6 className="text-muted mb-2">إجمالي المستخدمين</h6>
                  <h3 className="mb-1">{data.overview.totalUsers.toLocaleString()}</h3>
                  <small className="text-success">
                    <TrendingUp size={14} className="me-1" />
                    +{data.overview.monthlyGrowth}% هذا الشهر
                  </small>
                </div>
                <div className="p-3 rounded-circle bg-primary bg-opacity-10">
                  <Users size={24} className="text-primary" />
                </div>
              </div>
            </Card.Body>
          </Card>
        </Col>

        <Col md={6} lg={3} className="mb-3">
          <Card className={`h-100 ${isDark ? 'bg-dark text-light' : 'bg-white'} border-0 shadow-sm`}>
            <Card.Body>
              <div className="d-flex justify-content-between align-items-start">
                <div>
                  <h6 className="text-muted mb-2">المستخدمين النشطين</h6>
                  <h3 className="mb-1">{data.overview.activeUsers.toLocaleString()}</h3>
                  <small className="text-info">
                    <Activity size={14} className="me-1" />
                    {((data.overview.activeUsers / data.overview.totalUsers) * 100).toFixed(1)}% من الإجمالي
                  </small>
                </div>
                <div className="p-3 rounded-circle bg-success bg-opacity-10">
                  <UserCheck size={24} className="text-success" />
                </div>
              </div>
            </Card.Body>
          </Card>
        </Col>

        <Col md={6} lg={3} className="mb-3">
          <Card className={`h-100 ${isDark ? 'bg-dark text-light' : 'bg-white'} border-0 shadow-sm`}>
            <Card.Body>
              <div className="d-flex justify-content-between align-items-start">
                <div>
                  <h6 className="text-muted mb-2">إجمالي الدورات</h6>
                  <h3 className="mb-1">{data.overview.totalCourses}</h3>
                  <small className="text-warning">
                    <BookOpen size={14} className="me-1" />
                    {data.overview.completedCourses} مكتملة
                  </small>
                </div>
                <div className="p-3 rounded-circle bg-info bg-opacity-10">
                  <BookOpen size={24} className="text-info" />
                </div>
              </div>
            </Card.Body>
          </Card>
        </Col>

        <Col md={6} lg={3} className="mb-3">
          <Card className={`h-100 ${isDark ? 'bg-dark text-light' : 'bg-white'} border-0 shadow-sm`}>
            <Card.Body>
              <div className="d-flex justify-content-between align-items-start">
                <div>
                  <h6 className="text-muted mb-2">إجمالي الإيرادات</h6>
                  <h3 className="mb-1">{data.overview.totalRevenue.toLocaleString()} ريال</h3>
                  <small className="text-success">
                    <TrendingUp size={14} className="me-1" />
                    نمو مستمر
                  </small>
                </div>
                <div className="p-3 rounded-circle bg-warning bg-opacity-10">
                  <Target size={24} className="text-warning" />
                </div>
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Charts Row */}
      <Row className="mb-4">
        {/* User Engagement */}
        <Col lg={8}>
          <Card className={`${isDark ? 'bg-dark text-light' : 'bg-white'} border-0 shadow-sm`}>
            <Card.Header className="bg-transparent border-0">
              <h6 className="mb-0 d-flex align-items-center">
                <BarChart3 size={18} className="me-2" />
                تفاعل المستخدمين
              </h6>
            </Card.Header>
            <Card.Body>
              <div className="row g-3 mb-4">
                <div className="col-md-3">
                  <div className="text-center">
                    <h5 className="mb-1">{data.userEngagement.averageSessionTime}</h5>
                    <small className="text-muted">متوسط وقت الجلسة</small>
                  </div>
                </div>
                <div className="col-md-3">
                  <div className="text-center">
                    <h5 className="mb-1">{data.userEngagement.bounceRate}</h5>
                    <small className="text-muted">معدل الارتداد</small>
                  </div>
                </div>
                <div className="col-md-3">
                  <div className="text-center">
                    <h5 className="mb-1">{data.userEngagement.dailyActiveUsers[6]}</h5>
                    <small className="text-muted">نشط اليوم</small>
                  </div>
                </div>
                <div className="col-md-3">
                  <div className="text-center">
                    <h5 className="mb-1">{data.userEngagement.weeklyActiveUsers[6]}</h5>
                    <small className="text-muted">نشط هذا الأسبوع</small>
                  </div>
                </div>
              </div>
              
              {/* Simple chart representation */}
              <div className="bg-light rounded p-3" style={{ height: '200px' }}>
                <div className="d-flex align-items-end justify-content-between h-100">
                  {data.userEngagement.dailyActiveUsers.map((value, index) => (
                    <div key={index} className="d-flex flex-column align-items-center">
                      <div 
                        className="bg-primary rounded-top"
                        style={{ 
                          width: '20px', 
                          height: `${(value / Math.max(...data.userEngagement.dailyActiveUsers)) * 150}px`,
                          marginBottom: '10px'
                        }}
                      ></div>
                      <small className="text-muted">
                        {['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'][index]}
                      </small>
                    </div>
                  ))}
                </div>
              </div>
            </Card.Body>
          </Card>
        </Col>

        {/* Top Metrics */}
        <Col lg={4}>
          <Card className={`${isDark ? 'bg-dark text-light' : 'bg-white'} border-0 shadow-sm`}>
            <Card.Header className="bg-transparent border-0">
              <h6 className="mb-0 d-flex align-items-center">
                <Award size={18} className="me-2" />
                أهم المقاييس
              </h6>
            </Card.Header>
            <Card.Body>
              <div className="d-flex justify-content-between align-items-center mb-3 p-3 bg-light rounded">
                <div>
                  <div className="fw-bold">معدل إكمال الدورات</div>
                  <small className="text-muted">من إجمالي التسجيلات</small>
                </div>
                <div className="text-end">
                  <h5 className="mb-0 text-success">78%</h5>
                  <small className="text-success">
                    <TrendingUp size={12} className="me-1" />
                    +5%
                  </small>
                </div>
              </div>

              <div className="d-flex justify-content-between align-items-center mb-3 p-3 bg-light rounded">
                <div>
                  <div className="fw-bold">متوسط التقييم</div>
                  <small className="text-muted">جميع الدورات</small>
                </div>
                <div className="text-end">
                  <h5 className="mb-0 text-warning">4.7</h5>
                  <small className="text-muted">من 5</small>
                </div>
              </div>

              <div className="d-flex justify-content-between align-items-center mb-3 p-3 bg-light rounded">
                <div>
                  <div className="fw-bold">معدل الاحتفاظ</div>
                  <small className="text-muted">المستخدمين النشطين</small>
                </div>
                <div className="text-end">
                  <h5 className="mb-0 text-info">85%</h5>
                  <small className="text-info">
                    <TrendingUp size={12} className="me-1" />
                    +2%
                  </small>
                </div>
              </div>

              <div className="d-flex justify-content-between align-items-center p-3 bg-light rounded">
                <div>
                  <div className="fw-bold">الإيرادات الشهرية</div>
                  <small className="text-muted">هذا الشهر</small>
                </div>
                <div className="text-end">
                  <h5 className="mb-0 text-primary">15,600 ريال</h5>
                  <small className="text-success">
                    <TrendingUp size={12} className="me-1" />
                    +12%
                  </small>
                </div>
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Course Performance */}
      <Row className="mb-4">
        <Col lg={8}>
          <Card className={`${isDark ? 'bg-dark text-light' : 'bg-white'} border-0 shadow-sm`}>
            <Card.Header className="bg-transparent border-0">
              <h6 className="mb-0 d-flex align-items-center">
                <BookOpen size={18} className="me-2" />
                أداء الدورات
              </h6>
            </Card.Header>
            <Card.Body className="p-0">
              <Table responsive className={`mb-0 ${isDark ? 'table-dark' : ''}`}>
                <thead className={isDark ? 'table-secondary' : 'table-light'}>
                  <tr>
                    <th>الدورة</th>
                    <th>التسجيلات</th>
                    <th>المكتملة</th>
                    <th>معدل الإكمال</th>
                    <th>التقييم</th>
                  </tr>
                </thead>
                <tbody>
                  {data.coursePerformance.map((course, index) => (
                    <tr key={index}>
                      <td>
                        <div className="fw-bold">{course.name}</div>
                      </td>
                      <td>
                        <Badge bg="primary">{course.enrollments}</Badge>
                      </td>
                      <td>
                        <Badge bg="success">{course.completions}</Badge>
                      </td>
                      <td>
                        <div className="d-flex align-items-center">
                          <div className="progress me-2" style={{ width: '60px', height: '8px' }}>
                            <div 
                              className="progress-bar bg-success" 
                              style={{ width: `${(course.completions / course.enrollments) * 100}%` }}
                            ></div>
                          </div>
                          <small>{Math.round((course.completions / course.enrollments) * 100)}%</small>
                        </div>
                      </td>
                      <td>
                        <div className="d-flex align-items-center">
                          <Award size={14} className="text-warning me-1" />
                          <span>{course.rating}</span>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </Card.Body>
          </Card>
        </Col>

        <Col lg={4}>
          <Card className={`${isDark ? 'bg-dark text-light' : 'bg-white'} border-0 shadow-sm`}>
            <Card.Header className="bg-transparent border-0">
              <h6 className="mb-0 d-flex align-items-center">
                <PieChart size={18} className="me-2" />
                أعلى الدورات إيراداً
              </h6>
            </Card.Header>
            <Card.Body>
              {data.revenueAnalysis.topPayingCourses.map((course, index) => (
                <div key={index} className="d-flex justify-content-between align-items-center mb-3 p-3 bg-light rounded">
                  <div>
                    <div className="fw-bold">{course.name}</div>
                    <small className="text-muted">المركز {index + 1}</small>
                  </div>
                  <div className="text-end">
                    <div className="fw-bold text-success">{course.revenue.toLocaleString()} ريال</div>
                  </div>
                </div>
              ))}
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Recent Activity */}
      <Card className={`${isDark ? 'bg-dark text-light' : 'bg-white'} border-0 shadow-sm`}>
        <Card.Header className="bg-transparent border-0">
          <h6 className="mb-0 d-flex align-items-center">
            <Clock size={18} className="me-2" />
            النشاط الأخير
          </h6>
        </Card.Header>
        <Card.Body>
          <div className="row">
            <div className="col-md-6">
              <h6 className="text-muted mb-3">التسجيلات الأخيرة</h6>
              <div className="list-group list-group-flush">
                <div className="list-group-item bg-transparent border-0 px-0">
                  <div className="d-flex justify-content-between">
                    <span>أحمد محمد سجل في "تطوير الويب"</span>
                    <small className="text-muted">منذ 5 دقائق</small>
                  </div>
                </div>
                <div className="list-group-item bg-transparent border-0 px-0">
                  <div className="d-flex justify-content-between">
                    <span>فاطمة أحمد أكملت "التسويق الرقمي"</span>
                    <small className="text-muted">منذ 15 دقيقة</small>
                  </div>
                </div>
                <div className="list-group-item bg-transparent border-0 px-0">
                  <div className="d-flex justify-content-between">
                    <span>محمد علي سجل في "الذكاء الاصطناعي"</span>
                    <small className="text-muted">منذ 30 دقيقة</small>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-md-6">
              <h6 className="text-muted mb-3">الإنجازات الأخيرة</h6>
              <div className="list-group list-group-flush">
                <div className="list-group-item bg-transparent border-0 px-0">
                  <div className="d-flex justify-content-between">
                    <span>سارة أحمد حصلت على شهادة</span>
                    <small className="text-muted">منذ 10 دقائق</small>
                  </div>
                </div>
                <div className="list-group-item bg-transparent border-0 px-0">
                  <div className="d-flex justify-content-between">
                    <span>خالد محمد أكمل 5 دورات</span>
                    <small className="text-muted">منذ 25 دقيقة</small>
                  </div>
                </div>
                <div className="list-group-item bg-transparent border-0 px-0">
                  <div className="d-flex justify-content-between">
                    <span>نور الدين حقق مستوى جديد</span>
                    <small className="text-muted">منذ 45 دقيقة</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Card.Body>
      </Card>
    </div>
  );
};

export default SuperAdminAnalytics;
