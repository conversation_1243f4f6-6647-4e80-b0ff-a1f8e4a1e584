import express from 'express';
import User from '../models/User.js';
import Course from '../models/Course.js';
import UserProgress from '../models/UserProgress.js';
import { verifyToken } from '../middleware/verifyToken.js';
import { superAdminOnly, preventSelfModification, logSensitiveAction } from '../middleware/superAdminOnly.js';

const router = express.Router();

// Get all users (للـ Super Admin فقط)
router.get('/users', verifyToken, superAdminOnly, async (req, res) => {
  try {
    const users = await User.find()
      .select('-password')
      .sort({ createdAt: -1 });
    
    res.json(users);
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Update user role (super-admin only)
router.put('/users/:id/role', verifyToken, superAdminOnly, preventSelfModification ,async (req, res) => {
  try {
    const { role } = req.body;
    const userId = req.params.id;
    
    // Validate role
    if (!['student', 'admin', 'super-admin'].includes(role)) {
      return res.status(400).json({ 
        message: 'Invalid role',
        validRoles: ['student', 'admin', 'super-admin']
      });
    }

    // Prevent super-admin from changing their own role
    if (userId === req.user.id && req.user.role === 'super-admin') {
      return res.status(400).json({
        message: 'Cannot change your own role as super-admin'
      });
    }

    // Find and update user
    const user = await User.findByIdAndUpdate(
      userId,
      { role, updatedAt: new Date() },
      { new: true, runValidators: true }
    ).select('-password');

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Log the role change
    console.log(`Super admin ${req.user.email} changed role of user ${user.email} to ${role}`);

    res.json({
      message: 'User role updated successfully',
      user: user
    });
  } catch (error) {
    console.error('Error updating user role:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Delete user (super-admin only)
router.delete('/users/:id', verifyToken, superAdminOnly, preventSelfModification,  async (req, res) => {
  try {
    const userId = req.params.id;
    
    // Prevent super-admin from deleting their own account
    if (userId === req.user.id) {
      return res.status(400).json({
        message: 'Cannot delete your own account'
      });
    }

    // Find user first to get details for logging
    const userToDelete = await User.findById(userId).select('-password');
    
    if (!userToDelete) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Delete the user
    await User.findByIdAndDelete(userId);

    // Log the deletion
    console.log(`Super admin ${req.user.email} deleted user ${userToDelete.email} (${userToDelete.role})`);

    res.json({ 
      message: 'User deleted successfully',
      deletedUser: {
        id: userToDelete._id,
        name: userToDelete.name,
        email: userToDelete.email,
        role: userToDelete.role
      }
    });
  } catch (error) {
    console.error('Error deleting user:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Get detailed system statistics (super-admin only)
router.get('/statistics', verifyToken, superAdminOnly, async (req, res) => {
  try {
    // User statistics
    const totalUsers = await User.countDocuments();
    const totalStudents = await User.countDocuments({ role: 'student' });
    const totalAdmins = await User.countDocuments({ role: 'admin' });
    const totalSuperAdmins = await User.countDocuments({ role: 'super-admin' });
    
    // Course statistics
    const totalCourses = await Course.countDocuments();
    const activeCourses = await Course.countDocuments({ isActive: true });
    const inactiveCourses = await Course.countDocuments({ isActive: false });
    
    // Activity statistics
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const activeToday = await User.countDocuments({ 
      lastActive: { $gte: yesterday } 
    });

    // Recent registrations (last 7 days)
    const weekAgo = new Date();
    weekAgo.setDate(weekAgo.getDate() - 7);
    const recentRegistrations = await User.countDocuments({
      createdAt: { $gte: weekAgo }
    });

    // Recent courses (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const recentCourses = await Course.countDocuments({
      createdAt: { $gte: thirtyDaysAgo }
    });

    res.json({
      users: {
        total: totalUsers,
        students: totalStudents,
        admins: totalAdmins,
        superAdmins: totalSuperAdmins,
        activeToday: activeToday,
        recentRegistrations: recentRegistrations
      },
      courses: {
        total: totalCourses,
        active: activeCourses,
        inactive: inactiveCourses,
        recentlyAdded: recentCourses
      },
      system: {
        timestamp: new Date(),
        uptime: process.uptime()
      }
    });
  } catch (error) {
    console.error('Error fetching statistics:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Get user activity logs (super-admin only)
router.get('/users/:id/activity', verifyToken, superAdminOnly, async (req, res) => {
  try {
    const user = await User.findById(req.params.id)
      .select('name email role createdAt lastActive')
      .lean();
    
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // In a real application, you would have an activity log collection
    // For now, we'll return basic user information
    res.json({
      user: user,
      activity: {
        lastLogin: user.lastActive,
        accountCreated: user.createdAt,
        // Add more activity data as needed
      }
    });
  } catch (error) {
    console.error('Error fetching user activity:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Bulk operations for users (super-admin only)
router.post('/users/bulk-action', verifyToken, superAdminOnly, async (req, res) => {
  try {
    const { action, userIds } = req.body;
    
    if (!action || !userIds || !Array.isArray(userIds)) {
      return res.status(400).json({ 
        message: 'Invalid request. Action and userIds array required.' 
      });
    }

    let result;
    
    switch (action) {
      case 'delete':
        // Prevent deleting own account
        const filteredIds = userIds.filter(id => id !== req.user.id);
        result = await User.deleteMany({ _id: { $in: filteredIds } });
        break;
        
      case 'activate':
        result = await User.updateMany(
          { _id: { $in: userIds } },
          { isActive: true, updatedAt: new Date() }
        );
        break;
        
      case 'deactivate':
        // Prevent deactivating own account
        const filteredIdsForDeactivate = userIds.filter(id => id !== req.user.id);
        result = await User.updateMany(
          { _id: { $in: filteredIdsForDeactivate } },
          { isActive: false, updatedAt: new Date() }
        );
        break;
        
      default:
        return res.status(400).json({ message: 'Invalid action' });
    }

    res.json({
      message: `Bulk ${action} completed successfully`,
      affectedCount: result.modifiedCount || result.deletedCount,
      result: result
    });
  } catch (error) {
    console.error('Error performing bulk action:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Get advanced analytics (super-admin only)
router.get('/analytics', verifyToken, superAdminOnly, async (req, res) => {
  try {
    // Mock advanced analytics data
    const analytics = {
      userGrowth: {
        thisMonth: 150,
        lastMonth: 120,
        growth: 25
      },
      coursePerformance: {
        topCourses: [
          { name: 'React للمبتدئين', enrollments: 250, rating: 4.8 },
          { name: 'JavaScript المتقدم', enrollments: 180, rating: 4.6 },
          { name: 'Node.js', enrollments: 120, rating: 4.7 }
        ]
      },
      systemHealth: {
        cpuUsage: 45,
        memoryUsage: 62,
        diskUsage: 38,
        uptime: process.uptime()
      },
      revenue: {
        thisMonth: 15000,
        lastMonth: 12000,
        growth: 25
      }
    };

    res.json(analytics);
  } catch (error) {
    console.error('Error fetching analytics:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Get system stats (super-admin only)
router.get('/system/stats', verifyToken, superAdminOnly, async (req, res) => {
  try {
    const stats = {
      server: {
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        cpu: process.cpuUsage(),
        version: process.version
      },
      database: {
        connected: true,
        collections: ['users', 'courses', 'userprogresses']
      },
      system: {
        status: 'healthy',
        lastBackup: new Date(Date.now() - 86400000), // 24 hours ago
        maintenanceMode: false
      }
    };

    res.json(stats);
  } catch (error) {
    console.error('Error fetching system stats:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Get system settings (super-admin only)
router.get('/system/settings', verifyToken, superAdminOnly, async (req, res) => {
  try {
    // Mock system settings
    const settings = {
      siteName: 'أكاديمية التعلم',
      siteDescription: 'منصة تعليمية متقدمة',
      allowRegistration: true,
      requireEmailVerification: true,
      maintenanceMode: false,
      maxFileSize: 50,
      allowedFileTypes: 'jpg,jpeg,png,pdf,mp4,mp3',
      sessionTimeout: 30
    };

    res.json(settings);
  } catch (error) {
    console.error('Error fetching system settings:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Update system settings (super-admin only)
router.put('/system/settings', verifyToken, superAdminOnly, async (req, res) => {
  try {
    const settings = req.body;

    // Here you would normally save to database
    // For now, just return success

    res.json({
      message: 'تم حفظ الإعدادات بنجاح',
      settings
    });
  } catch (error) {
    console.error('Error updating system settings:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

export default router;
