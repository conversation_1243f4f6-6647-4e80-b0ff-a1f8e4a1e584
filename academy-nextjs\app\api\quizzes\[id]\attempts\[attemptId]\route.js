import { NextResponse } from 'next/server';
import { withAuth } from '../../../../../../lib/auth';
import connectDB from '../../../../../../lib/mongodb';
import QuizAttempt from '../../../../../../models/QuizAttempt';
import Quiz from '../../../../../../models/Quiz';
import Question from '../../../../../../models/Question';

// GET - جلب محاولة محددة
export const GET = withAuth(async (request, { params }) => {
  try {
    await connectDB();
    
    const { attemptId } = params;
    
    const attempt = await QuizAttempt.findById(attemptId)
      .populate('quiz')
      .populate('student', 'name email')
      .populate('answers.question')
      .lean();
    
    if (!attempt) {
      return NextResponse.json(
        { message: 'المحاولة غير موجودة' },
        { status: 404 }
      );
    }

    // التحقق من الصلاحية
    const isOwner = attempt.student._id.toString() === request.user._id.toString();
    const isAdmin = ['admin', 'super-admin'].includes(request.user.role);
    
    if (!isOwner && !isAdmin) {
      return NextResponse.json(
        { message: 'ليس لديك صلاحية لعرض هذه المحاولة' },
        { status: 403 }
      );
    }

    return NextResponse.json({
      attempt
    }, { status: 200 });

  } catch (error) {
    console.error('Quiz attempt fetch error:', error);
    return NextResponse.json(
      { message: 'خطأ في جلب المحاولة' },
      { status: 500 }
    );
  }
});

// PUT - حفظ إجابة أو تحديث المحاولة
export const PUT = withAuth(async (request, { params }) => {
  try {
    await connectDB();
    
    const { attemptId } = params;
    const data = await request.json();
    const { action, questionId, answer, submit = false } = data;
    
    const attempt = await QuizAttempt.findById(attemptId)
      .populate('quiz');
    
    if (!attempt) {
      return NextResponse.json(
        { message: 'المحاولة غير موجودة' },
        { status: 404 }
      );
    }

    // التحقق من الصلاحية
    if (attempt.student.toString() !== request.user._id.toString()) {
      return NextResponse.json(
        { message: 'ليس لديك صلاحية لتحديث هذه المحاولة' },
        { status: 403 }
      );
    }

    // التحقق من حالة المحاولة
    if (attempt.status !== 'in_progress') {
      return NextResponse.json(
        { message: 'لا يمكن تحديث محاولة منتهية' },
        { status: 400 }
      );
    }

    // التحقق من انتهاء الوقت
    if (attempt.isExpired(attempt.quiz)) {
      attempt.status = 'expired';
      await attempt.save();
      return NextResponse.json(
        { message: 'انتهى وقت الاختبار' },
        { status: 400 }
      );
    }

    if (action === 'save_answer' && questionId && answer !== undefined) {
      // حفظ إجابة سؤال
      await attempt.saveAnswer(questionId, answer);
      
      // جلب السؤال لحساب النقاط
      const question = await Question.findById(questionId);
      if (question) {
        const answerIndex = attempt.answers.findIndex(
          a => a.question.toString() === questionId
        );
        
        if (answerIndex !== -1) {
          const isCorrect = question.checkAnswer(answer);
          const points = question.calculateScore(answer);
          
          attempt.answers[answerIndex].isCorrect = isCorrect;
          attempt.answers[answerIndex].points = points;
          
          await attempt.save();
        }
      }
      
      return NextResponse.json({
        message: 'تم حفظ الإجابة',
        saved: true
      }, { status: 200 });
    }

    if (submit) {
      // إنهاء الاختبار
      await attempt.submit();
      
      // حساب النتيجة النهائية
      let totalScore = 0;
      let totalPoints = 0;
      
      // جلب جميع الأسئلة لحساب النقاط
      const questions = await Question.find({ 
        quiz: attempt.quiz._id 
      }).lean();
      
      for (const question of questions) {
        totalPoints += question.points || 1;
        
        const userAnswer = attempt.answers.find(
          a => a.question.toString() === question._id.toString()
        );
        
        if (userAnswer) {
          const isCorrect = question.checkAnswer ? 
            question.checkAnswer(userAnswer.answer) : false;
          const points = isCorrect ? (question.points || 1) : 0;
          
          totalScore += points;
          
          // تحديث الإجابة
          userAnswer.isCorrect = isCorrect;
          userAnswer.points = points;
        }
      }
      
      attempt.score = totalScore;
      attempt.totalPoints = totalPoints;
      attempt.percentage = totalPoints > 0 ? 
        Math.round((totalScore / totalPoints) * 100) : 0;
      attempt.passed = attempt.percentage >= attempt.quiz.settings.passingScore;
      attempt.status = 'submitted';
      
      await attempt.save();
      
      // تحديث إحصائيات الاختبار
      await Quiz.findByIdAndUpdate(attempt.quiz._id, {
        $inc: { 'stats.totalAttempts': 1 }
      });

      return NextResponse.json({
        message: 'تم إنهاء الاختبار بنجاح',
        result: {
          score: attempt.score,
          totalPoints: attempt.totalPoints,
          percentage: attempt.percentage,
          passed: attempt.passed,
          timeSpent: attempt.timeSpent
        }
      }, { status: 200 });
    }

    return NextResponse.json(
      { message: 'إجراء غير صحيح' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Quiz attempt update error:', error);
    return NextResponse.json(
      { message: 'خطأ في تحديث المحاولة' },
      { status: 500 }
    );
  }
});
