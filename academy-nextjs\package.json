{"name": "academy-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"next": "14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "mongoose": "^8.0.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "bootstrap": "^5.3.2", "react-bootstrap": "^2.9.1", "lucide-react": "^0.344.0", "axios": "^1.6.2", "formik": "^2.4.6", "yup": "^1.6.1", "react-toastify": "^11.0.5", "next-auth": "^4.24.5"}, "devDependencies": {"@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-config-next": "14.0.4", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "typescript": "^5.3.3"}}