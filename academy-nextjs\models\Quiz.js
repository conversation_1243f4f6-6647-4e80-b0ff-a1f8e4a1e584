import mongoose from 'mongoose';

const quizSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'عنوان الاختبار مطلوب'],
    trim: true
  },
  description: {
    type: String,
    required: [true, 'وصف الاختبار مطلوب']
  },
  course: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Course',
    required: true
  },
  lesson: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Lesson'
  },
  unit: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Unit'
  },
  type: {
    type: String,
    enum: ['practice', 'graded', 'final'],
    default: 'practice'
  },
  // إعدادات الاختبار
  settings: {
    timeLimit: {
      type: Number, // بالدقائق
      default: 0 // 0 = بدون حد زمني
    },
    attempts: {
      type: Number,
      default: 1 // عدد المحاولات المسموحة
    },
    passingScore: {
      type: Number,
      default: 70 // النسبة المئوية للنجاح
    },
    showResults: {
      type: String,
      enum: ['immediately', 'after_submission', 'after_deadline', 'never'],
      default: 'immediately'
    },
    showCorrectAnswers: {
      type: Boolean,
      default: true
    },
    randomizeQuestions: {
      type: Boolean,
      default: false
    },
    randomizeAnswers: {
      type: Boolean,
      default: false
    }
  },
  // الأسئلة
  questions: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Question'
  }],
  // التوقيت
  availableFrom: Date,
  availableUntil: Date,
  
  // الحالة
  isActive: {
    type: Boolean,
    default: true
  },
  isPublished: {
    type: Boolean,
    default: false
  },
  
  // إحصائيات
  stats: {
    totalAttempts: { type: Number, default: 0 },
    averageScore: { type: Number, default: 0 },
    passRate: { type: Number, default: 0 },
    averageTime: { type: Number, default: 0 }
  },
  
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// فهارس
quizSchema.index({ course: 1, isActive: 1 });
quizSchema.index({ lesson: 1 });
quizSchema.index({ type: 1, isPublished: 1 });

// دالة لحساب الدرجة الإجمالية
quizSchema.methods.calculateTotalPoints = async function() {
  await this.populate('questions');
  return this.questions.reduce((total, question) => total + (question.points || 1), 0);
};

// دالة للتحقق من إمكانية أداء الاختبار
quizSchema.methods.canTakeQuiz = function(user, enrollment) {
  // التحقق من النشر
  if (!this.isPublished || !this.isActive) return false;
  
  // التحقق من التوقيت
  const now = new Date();
  if (this.availableFrom && now < this.availableFrom) return false;
  if (this.availableUntil && now > this.availableUntil) return false;
  
  // التحقق من التسجيل في الدورة
  if (!enrollment || !enrollment.isActive) return false;
  
  return true;
};

export default mongoose.models.Quiz || mongoose.model('Quiz', quizSchema);
