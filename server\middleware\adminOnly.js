// Middleware للتحقق من أدوار متعددة
export const allowRoles = (...roles) => (req, res, next) => {
  if (req.user && roles.includes(req.user.role)) {
    return next();
  }
  res.status(403).json({ message: 'Access denied. Insufficient permissions.' });
};

// للإبقاء على التوافق مع الكود القديم
export const adminOnly = (req, res, next) => {
  if (req.user && req.user.role === 'admin') {
    next();
  } else {
    res.status(403).json({ message: 'Access denied. Admin rights required.' });
  }
};