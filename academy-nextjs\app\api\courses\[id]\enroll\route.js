import { NextResponse } from 'next/server';
import { verifyToken } from '../../../../../lib/auth';
import { connectDB } from '../../../../../lib/mongodb';
import Course from '../../../../../models/Course';
import Enrollment from '../../../../../models/Enrollment';

// POST - التسجيل في الدورة
export async function POST(request, { params }) {
  try {
    await connectDB();
    
    // التحقق من صحة التوكن
    const authResult = await verifyToken(request);
    if (!authResult.success) {
      return NextResponse.json(
        { message: authResult.message },
        { status: 401 }
      );
    }

    const { user } = authResult;
    const { id: courseId } = params;

    // التحقق من وجود الدورة
    const course = await Course.findById(courseId);
    if (!course) {
      return NextResponse.json(
        { message: 'الدورة غير موجودة' },
        { status: 404 }
      );
    }

    if (!course.isActive) {
      return NextResponse.json(
        { message: 'الدورة غير متاحة حالياً' },
        { status: 400 }
      );
    }

    // التحقق من التسجيل المسبق
    const existingEnrollment = await Enrollment.findOne({
      student: user._id,
      course: courseId
    });

    if (existingEnrollment) {
      return NextResponse.json(
        { message: 'أنت مسجل في هذه الدورة بالفعل' },
        { status: 400 }
      );
    }

    // إنشاء تسجيل جديد
    const enrollment = new Enrollment({
      student: user._id,
      course: courseId,
      unitsProgress: course.units.map((unit, unitIndex) => ({
        unitId: unit._id,
        unitIndex,
        isUnlocked: unitIndex === 0, // فقط الوحدة الأولى مفتوحة
        lessons: unit.lessons.map((lesson, lessonIndex) => ({
          lessonId: lesson._id,
          lessonIndex,
          completed: false
        })),
        quizCompleted: false,
        quizAttempts: 0,
        unitCompleted: false
      }))
    });

    await enrollment.save();

    // تحديث عدد المسجلين في الدورة
    course.enrollmentCount = (course.enrollmentCount || 0) + 1;
    await course.save();

    return NextResponse.json({
      success: true,
      message: 'تم التسجيل في الدورة بنجاح',
      enrollment: {
        _id: enrollment._id,
        progress: enrollment.progress,
        currentUnit: enrollment.currentUnit
      }
    });

  } catch (error) {
    console.error('Error enrolling in course:', error);
    return NextResponse.json(
      { message: 'خطأ في التسجيل في الدورة' },
      { status: 500 }
    );
  }
}

// DELETE - إلغاء التسجيل في الدورة
export async function DELETE(request, { params }) {
  try {
    await connectDB();
    
    // التحقق من صحة التوكن
    const authResult = await verifyToken(request);
    if (!authResult.success) {
      return NextResponse.json(
        { message: authResult.message },
        { status: 401 }
      );
    }

    const { user } = authResult;
    const { id: courseId } = params;

    // العثور على التسجيل
    const enrollment = await Enrollment.findOne({
      student: user._id,
      course: courseId
    });

    if (!enrollment) {
      return NextResponse.json(
        { message: 'أنت غير مسجل في هذه الدورة' },
        { status: 400 }
      );
    }

    // حذف التسجيل
    await Enrollment.findByIdAndDelete(enrollment._id);

    // تحديث عدد المسجلين في الدورة
    const course = await Course.findById(courseId);
    if (course) {
      course.enrollmentCount = Math.max((course.enrollmentCount || 1) - 1, 0);
      await course.save();
    }

    return NextResponse.json({
      success: true,
      message: 'تم إلغاء التسجيل في الدورة'
    });

  } catch (error) {
    console.error('Error unenrolling from course:', error);
    return NextResponse.json(
      { message: 'خطأ في إلغاء التسجيل' },
      { status: 500 }
    );
  }
}
