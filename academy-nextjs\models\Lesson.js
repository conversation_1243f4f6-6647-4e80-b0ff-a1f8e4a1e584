import mongoose from 'mongoose';

const lessonSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'عنوان الدرس مطلوب'],
    trim: true
  },
  description: {
    type: String,
    required: [true, 'وصف الدرس مطلوب']
  },
  course: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Course',
    required: true
  },
  unit: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Unit',
    required: true
  },
  order: {
    type: Number,
    required: true,
    default: 0
  },
  type: {
    type: String,
    enum: ['video', 'text', 'quiz', 'assignment', 'file'],
    required: true,
    default: 'text'
  },
  content: {
    // محتوى الدرس حسب النوع
    text: String, // للدروس النصية
    videoUrl: String, // رابط الفيديو
    videoProvider: {
      type: String,
      enum: ['youtube', 'vimeo', 'direct'],
      default: 'youtube'
    },
    videoDuration: Number, // مدة الفيديو بالثواني
    fileUrl: String, // رابط الملف
    fileType: String, // نوع الملف
    fileSize: Number, // حجم الملف
    embedCode: String // كود التضمين
  },
  resources: [{
    title: String,
    url: String,
    type: {
      type: String,
      enum: ['pdf', 'doc', 'link', 'image', 'video']
    }
  }],
  duration: {
    type: Number, // المدة المقدرة بالدقائق
    default: 0
  },
  isPreview: {
    type: Boolean,
    default: false // هل يمكن مشاهدة الدرس كمعاينة
  },
  isActive: {
    type: Boolean,
    default: true
  },
  // إعدادات التقدم
  completionCriteria: {
    type: String,
    enum: ['view', 'time', 'quiz', 'manual'],
    default: 'view'
  },
  minViewTime: {
    type: Number, // الحد الأدنى لوقت المشاهدة (بالثواني)
    default: 0
  },
  // الاختبار المرتبط بالدرس
  quiz: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Quiz'
  },
  // إحصائيات
  stats: {
    views: { type: Number, default: 0 },
    completions: { type: Number, default: 0 },
    averageTime: { type: Number, default: 0 },
    likes: { type: Number, default: 0 },
    dislikes: { type: Number, default: 0 }
  },
  // الملاحظات والتعليقات
  notes: [{
    user: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    content: String,
    timestamp: Number, // وقت الملاحظة في الفيديو
    createdAt: { type: Date, default: Date.now }
  }],
  // البيانات الوصفية
  metadata: {
    tags: [String],
    difficulty: {
      type: String,
      enum: ['easy', 'medium', 'hard'],
      default: 'medium'
    },
    language: {
      type: String,
      default: 'ar'
    }
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// فهارس للأداء
lessonSchema.index({ course: 1, unit: 1, order: 1 });
lessonSchema.index({ type: 1, isActive: 1 });
lessonSchema.index({ createdAt: -1 });
lessonSchema.index({ 'stats.views': -1 });

// دالة لتحديث إحصائيات المشاهدة
lessonSchema.methods.incrementView = function() {
  this.stats.views += 1;
  return this.save();
};

// دالة لإكمال الدرس
lessonSchema.methods.markCompleted = function() {
  this.stats.completions += 1;
  return this.save();
};

// دالة للحصول على الدرس التالي
lessonSchema.methods.getNextLesson = async function() {
  return this.constructor.findOne({
    course: this.course,
    unit: this.unit,
    order: { $gt: this.order },
    isActive: true
  }).sort({ order: 1 });
};

// دالة للحصول على الدرس السابق
lessonSchema.methods.getPreviousLesson = async function() {
  return this.constructor.findOne({
    course: this.course,
    unit: this.unit,
    order: { $lt: this.order },
    isActive: true
  }).sort({ order: -1 });
};

// التحقق من إمكانية الوصول للدرس
lessonSchema.methods.canAccess = function(user, enrollment) {
  // إذا كان الدرس معاينة
  if (this.isPreview) return true;
  
  // إذا كان المستخدم مدير
  if (['admin', 'super-admin'].includes(user.role)) return true;
  
  // إذا كان المستخدم مسجل في الدورة
  if (enrollment && enrollment.isActive) return true;
  
  return false;
};

export default mongoose.models.Lesson || mongoose.model('Lesson', lessonSchema);
