'use client';

import { Spinner } from 'react-bootstrap';
import { useTheme } from '../contexts/ThemeContext';

export default function LoadingSpinner({ 
  size = 'md', 
  variant = 'primary', 
  text = 'جاري التحميل...', 
  fullScreen = false,
  className = ''
}) {
  const { isDark } = useTheme();

  const getSpinnerSize = () => {
    switch (size) {
      case 'sm':
        return { width: '1rem', height: '1rem' };
      case 'lg':
        return { width: '3rem', height: '3rem' };
      case 'xl':
        return { width: '4rem', height: '4rem' };
      default:
        return { width: '2rem', height: '2rem' };
    }
  };

  const spinnerElement = (
    <div className={`text-center ${className}`}>
      <Spinner 
        animation="border" 
        variant={variant}
        style={getSpinnerSize()}
      />
      {text && (
        <p className={`mt-3 mb-0 ${isDark ? 'text-light' : 'text-muted'}`}>
          {text}
        </p>
      )}
    </div>
  );

  if (fullScreen) {
    return (
      <div className={`d-flex justify-content-center align-items-center min-vh-100 ${isDark ? 'bg-dark' : 'bg-light'}`}>
        {spinnerElement}
      </div>
    );
  }

  return spinnerElement;
}

// مكونات loading متخصصة
export function PageLoader({ text = 'جاري تحميل الصفحة...' }) {
  return <LoadingSpinner fullScreen={true} size="lg" text={text} />;
}

export function ButtonLoader({ size = 'sm', className = 'me-2' }) {
  return (
    <Spinner 
      animation="border" 
      size={size}
      className={className}
    />
  );
}

export function CardLoader({ height = '200px' }) {
  const { isDark } = useTheme();
  
  return (
    <div 
      className={`d-flex justify-content-center align-items-center ${isDark ? 'bg-dark' : 'bg-light'} rounded`}
      style={{ height }}
    >
      <LoadingSpinner size="md" text="" />
    </div>
  );
}

export function TableLoader({ rows = 5, columns = 4 }) {
  const { isDark } = useTheme();
  
  return (
    <div className="table-responsive">
      <table className={`table ${isDark ? 'table-dark' : ''}`}>
        <thead>
          <tr>
            {[...Array(columns)].map((_, i) => (
              <th key={i}>
                <div 
                  className={`placeholder ${isDark ? 'placeholder-light' : 'placeholder-glow'}`}
                  style={{ width: '80%', height: '20px' }}
                />
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {[...Array(rows)].map((_, rowIndex) => (
            <tr key={rowIndex}>
              {[...Array(columns)].map((_, colIndex) => (
                <td key={colIndex}>
                  <div 
                    className={`placeholder ${isDark ? 'placeholder-light' : 'placeholder-glow'}`}
                    style={{ width: '90%', height: '16px' }}
                  />
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
