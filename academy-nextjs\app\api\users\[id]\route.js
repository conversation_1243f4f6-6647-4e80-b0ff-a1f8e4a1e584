import { NextResponse } from 'next/server';
import { withAuth } from '../../../../lib/auth';
import connectDB from '../../../../lib/mongodb';
import User from '../../../../models/User';
import Enrollment from '../../../../models/Enrollment';

// GET - جلب مستخدم محدد
export const GET = withAuth(async (request, { params }) => {
  try {
    await connectDB();
    
    const { id } = params;
    
    const user = await User.findById(id).select('-password').lean();
    
    if (!user) {
      return NextResponse.json(
        { message: 'المستخدم غير موجود' },
        { status: 404 }
      );
    }

    // إضافة إحصائيات المستخدم إذا كان طالباً
    if (user.role === 'student') {
      const enrollments = await Enrollment.find({ student: id })
        .populate('course', 'title instructor level')
        .lean();
      
      const completedCourses = enrollments.filter(e => e.isCompleted);
      const inProgressCourses = enrollments.filter(e => !e.isCompleted);
      
      const totalPoints = completedCourses.reduce((sum, e) => sum + (e.finalScore || 0), 0);
      const totalStudyTime = enrollments.reduce((sum, e) => sum + (e.studyTime?.totalMinutes || 0), 0);
      
      user.detailedStats = {
        enrollments,
        completedCourses: completedCourses.length,
        inProgressCourses: inProgressCourses.length,
        totalPoints,
        averageScore: completedCourses.length > 0 ? totalPoints / completedCourses.length : 0,
        totalStudyHours: Math.round(totalStudyTime / 60),
        recentActivity: enrollments
          .sort((a, b) => new Date(b.lastAccessedAt) - new Date(a.lastAccessedAt))
          .slice(0, 5)
      };
    }

    return NextResponse.json({
      user
    }, { status: 200 });

  } catch (error) {
    console.error('User fetch error:', error);
    return NextResponse.json(
      { message: 'خطأ في جلب المستخدم' },
      { status: 500 }
    );
  }
}, { roles: ['admin', 'super-admin'] });

// PUT - تحديث مستخدم
export const PUT = withAuth(async (request, { params }) => {
  try {
    await connectDB();
    
    const { id } = params;
    const data = await request.json();
    
    const user = await User.findById(id);
    if (!user) {
      return NextResponse.json(
        { message: 'المستخدم غير موجود' },
        { status: 404 }
      );
    }

    // الحقول المسموح بتحديثها
    const allowedFields = ['name', 'role', 'isActive'];
    
    allowedFields.forEach(field => {
      if (data[field] !== undefined) {
        user[field] = data[field];
      }
    });

    await user.save();

    return NextResponse.json({
      message: 'تم تحديث المستخدم بنجاح',
      user: user.toJSON()
    }, { status: 200 });

  } catch (error) {
    console.error('User update error:', error);
    return NextResponse.json(
      { message: 'خطأ في تحديث المستخدم' },
      { status: 500 }
    );
  }
}, { roles: ['admin', 'super-admin'] });

// DELETE - حذف مستخدم
export const DELETE = withAuth(async (request, { params }) => {
  try {
    await connectDB();
    
    const { id } = params;
    
    const user = await User.findById(id);
    if (!user) {
      return NextResponse.json(
        { message: 'المستخدم غير موجود' },
        { status: 404 }
      );
    }

    // حذف جميع التسجيلات المرتبطة بالمستخدم
    await Enrollment.deleteMany({ student: id });
    
    // حذف المستخدم
    await User.findByIdAndDelete(id);

    return NextResponse.json({
      message: 'تم حذف المستخدم بنجاح'
    }, { status: 200 });

  } catch (error) {
    console.error('User deletion error:', error);
    return NextResponse.json(
      { message: 'خطأ في حذف المستخدم' },
      { status: 500 }
    );
  }
}, { roles: ['super-admin'] }); // فقط المدير العام يمكنه حذف المستخدمين
