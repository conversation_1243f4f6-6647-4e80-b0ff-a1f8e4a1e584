'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useAuth } from '../../../../../../../contexts/AuthContext';
import api from '../../../../../../../lib/api';
import { toast } from 'react-toastify';
import {
  ArrowLeft,
  ArrowRight,
  CheckCircle,
  Play,
  FileText,
  Clock,
  BookOpen
} from 'lucide-react';

export default function LessonPage() {
  const { user } = useAuth();
  const router = useRouter();
  const params = useParams();
  const { id: courseId, unitId, lessonId } = params;

  const [course, setCourse] = useState(null);
  const [unit, setUnit] = useState(null);
  const [lesson, setLesson] = useState(null);
  const [progress, setProgress] = useState(null);
  const [loading, setLoading] = useState(true);
  const [completing, setCompleting] = useState(false);
  const [startTime, setStartTime] = useState(Date.now());

  useEffect(() => {
    fetchData();
    setStartTime(Date.now());
  }, [courseId, unitId, lessonId]);

  const fetchData = async () => {
    try {
      setLoading(true);
      
      // جلب الدورة
      const courseResponse = await api.get(`/courses/${courseId}`);
      const courseData = courseResponse.data.course;
      setCourse(courseData);
      
      // العثور على الوحدة والدرس
      const foundUnit = courseData.units.find(u => u._id === unitId);
      setUnit(foundUnit);
      
      if (foundUnit) {
        const foundLesson = foundUnit.lessons.find(l => l._id === lessonId);
        setLesson(foundLesson);
      }
      
      // جلب التقدم
      const progressResponse = await api.get(`/courses/${courseId}/progress`);
      setProgress(progressResponse.data);
      
    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('خطأ في جلب البيانات');
    } finally {
      setLoading(false);
    }
  };

  const completeLesson = async () => {
    try {
      setCompleting(true);
      
      const timeSpent = Math.round((Date.now() - startTime) / 1000); // بالثواني
      const unitIndex = course.units.findIndex(u => u._id === unitId);
      const lessonIndex = unit.lessons.findIndex(l => l._id === lessonId);
      
      await api.post(`/courses/${courseId}/progress`, {
        unitIndex,
        lessonIndex,
        timeSpent
      });
      
      toast.success('تم إكمال الدرس بنجاح!');
      
      // الانتقال للدرس التالي أو الاختبار
      const nextLesson = unit.lessons[lessonIndex + 1];
      if (nextLesson) {
        router.push(`/courses/${courseId}/units/${unitId}/lessons/${nextLesson._id}`);
      } else {
        // إذا انتهت الدروس، الانتقال للاختبار
        if (unit.quiz && unit.quiz.questions.length > 0) {
          router.push(`/courses/${courseId}/units/${unitId}/quiz`);
        } else {
          // العودة لصفحة الدورة
          router.push(`/courses/${courseId}`);
        }
      }
      
    } catch (error) {
      console.error('Error completing lesson:', error);
      toast.error('خطأ في إكمال الدرس');
    } finally {
      setCompleting(false);
    }
  };

  const goToPreviousLesson = () => {
    const unitIndex = course.units.findIndex(u => u._id === unitId);
    const lessonIndex = unit.lessons.findIndex(l => l._id === lessonId);
    
    if (lessonIndex > 0) {
      const prevLesson = unit.lessons[lessonIndex - 1];
      router.push(`/courses/${courseId}/units/${unitId}/lessons/${prevLesson._id}`);
    } else if (unitIndex > 0) {
      // الانتقال للوحدة السابقة
      const prevUnit = course.units[unitIndex - 1];
      if (prevUnit.lessons.length > 0) {
        const lastLesson = prevUnit.lessons[prevUnit.lessons.length - 1];
        router.push(`/courses/${courseId}/units/${prevUnit._id}/lessons/${lastLesson._id}`);
      }
    }
  };

  const goToNextLesson = () => {
    const unitIndex = course.units.findIndex(u => u._id === unitId);
    const lessonIndex = unit.lessons.findIndex(l => l._id === lessonId);
    
    const nextLesson = unit.lessons[lessonIndex + 1];
    if (nextLesson) {
      router.push(`/courses/${courseId}/units/${unitId}/lessons/${nextLesson._id}`);
    } else {
      // إذا انتهت الدروس، الانتقال للاختبار
      if (unit.quiz && unit.quiz.questions.length > 0) {
        router.push(`/courses/${courseId}/units/${unitId}/quiz`);
      }
    }
  };

  if (loading) {
    return (
      <div className="container py-4">
        <div className="text-center">
          <div className="spinner-border" role="status">
            <span className="visually-hidden">جاري التحميل...</span>
          </div>
        </div>
      </div>
    );
  }

  if (!course || !unit || !lesson) {
    return (
      <div className="container py-4">
        <div className="alert alert-danger">
          الدرس غير موجود
        </div>
      </div>
    );
  }

  const unitIndex = course.units.findIndex(u => u._id === unitId);
  const lessonIndex = unit.lessons.findIndex(l => l._id === lessonId);
  const unitProgress = progress?.unitsProgress?.[unitIndex];
  const lessonProgress = unitProgress?.lessons?.[lessonIndex];
  const isCompleted = lessonProgress?.completed;

  return (
    <div className="container-fluid py-4">
      {/* Header */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <button
                className="btn btn-outline-secondary btn-sm mb-2"
                onClick={() => router.push(`/courses/${courseId}`)}
              >
                <ArrowLeft className="w-4 h-4 me-1" />
                العودة للدورة
              </button>
              <h2 className="mb-1">{lesson.title}</h2>
              <p className="text-muted mb-0">
                {course.title} • الوحدة {unitIndex + 1}: {unit.title}
              </p>
            </div>
            <div className="d-flex align-items-center gap-3">
              {lesson.duration && (
                <div className="d-flex align-items-center text-muted">
                  <Clock className="w-4 h-4 me-1" />
                  {lesson.duration}
                </div>
              )}
              {isCompleted && (
                <div className="d-flex align-items-center text-success">
                  <CheckCircle className="w-4 h-4 me-1" />
                  مكتمل
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Lesson Content */}
      <div className="row">
        <div className="col-lg-8">
          <div className="card">
            <div className="card-body">
              {/* Video Lesson */}
              {lesson.type === 'video' && lesson.videoUrl && (
                <div className="mb-4">
                  <video
                    src={lesson.videoUrl}
                    controls
                    className="w-100"
                    style={{ maxHeight: '400px' }}
                  >
                    متصفحك لا يدعم تشغيل الفيديو
                  </video>
                </div>
              )}

              {/* Text Content */}
              {lesson.textContent && (
                <div className="mb-4">
                  <div 
                    className="lesson-content"
                    dangerouslySetInnerHTML={{ __html: lesson.textContent }}
                  />
                </div>
              )}

              {/* Additional Content */}
              {lesson.content && (
                <div className="mb-4">
                  <div className="lesson-content">
                    {lesson.content}
                  </div>
                </div>
              )}

              {/* Navigation */}
              <div className="d-flex justify-content-between align-items-center mt-4">
                <button
                  className="btn btn-outline-secondary"
                  onClick={goToPreviousLesson}
                  disabled={unitIndex === 0 && lessonIndex === 0}
                >
                  <ArrowLeft className="w-4 h-4 me-1" />
                  الدرس السابق
                </button>

                <div className="d-flex gap-2">
                  {!isCompleted && (
                    <button
                      className="btn btn-success"
                      onClick={completeLesson}
                      disabled={completing}
                    >
                      {completing ? (
                        <>
                          <div className="spinner-border spinner-border-sm me-2" role="status"></div>
                          جاري الإكمال...
                        </>
                      ) : (
                        <>
                          <CheckCircle className="w-4 h-4 me-1" />
                          إكمال الدرس
                        </>
                      )}
                    </button>
                  )}

                  <button
                    className="btn btn-primary"
                    onClick={goToNextLesson}
                    disabled={lessonIndex === unit.lessons.length - 1 && (!unit.quiz || unit.quiz.questions.length === 0)}
                  >
                    {lessonIndex === unit.lessons.length - 1 ? (
                      unit.quiz && unit.quiz.questions.length > 0 ? (
                        <>
                          الاختبار
                          <ArrowRight className="w-4 h-4 ms-1" />
                        </>
                      ) : (
                        'انتهت الوحدة'
                      )
                    ) : (
                      <>
                        الدرس التالي
                        <ArrowRight className="w-4 h-4 ms-1" />
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="col-lg-4">
          {/* Unit Progress */}
          <div className="card mb-4">
            <div className="card-header">
              <h6 className="mb-0">تقدم الوحدة</h6>
            </div>
            <div className="card-body">
              <div className="list-group list-group-flush">
                {unit.lessons.map((unitLesson, index) => {
                  const lessonProg = unitProgress?.lessons?.[index];
                  const isCurrent = unitLesson._id === lessonId;
                  const isLessonCompleted = lessonProg?.completed;
                  
                  return (
                    <div
                      key={unitLesson._id}
                      className={`list-group-item d-flex align-items-center ${isCurrent ? 'bg-light' : ''}`}
                    >
                      <div className="me-2">
                        {isLessonCompleted ? (
                          <CheckCircle className="w-4 h-4 text-success" />
                        ) : isCurrent ? (
                          <Play className="w-4 h-4 text-primary" />
                        ) : (
                          <div className="w-4 h-4 border rounded-circle"></div>
                        )}
                      </div>
                      <div className="flex-grow-1">
                        <div className={`${isCurrent ? 'fw-bold' : ''}`}>
                          {unitLesson.title}
                        </div>
                        <small className="text-muted">
                          {unitLesson.type === 'video' ? 'فيديو' : unitLesson.type === 'reading' ? 'قراءة' : 'تمرين'}
                        </small>
                      </div>
                    </div>
                  );
                })}
                
                {/* Quiz */}
                {unit.quiz && unit.quiz.questions.length > 0 && (
                  <div className="list-group-item d-flex align-items-center">
                    <div className="me-2">
                      {unitProgress?.quizCompleted ? (
                        <CheckCircle className="w-4 h-4 text-success" />
                      ) : (
                        <div className="w-4 h-4 border rounded-circle"></div>
                      )}
                    </div>
                    <div className="flex-grow-1">
                      <div className="fw-medium">اختبار الوحدة</div>
                      <small className="text-muted">
                        {unit.quiz.questions.length} سؤال
                        {unitProgress?.quizScore && ` • النتيجة: ${unitProgress.quizScore}%`}
                      </small>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Course Info */}
          <div className="card">
            <div className="card-header">
              <h6 className="mb-0">معلومات الدورة</h6>
            </div>
            <div className="card-body">
              <div className="d-flex align-items-center mb-2">
                <BookOpen className="w-4 h-4 me-2 text-primary" />
                <span>{course.title}</span>
              </div>
              <div className="progress mb-2" style={{ height: '8px' }}>
                <div
                  className="progress-bar"
                  role="progressbar"
                  style={{ width: `${progress?.progress || 0}%` }}
                ></div>
              </div>
              <small className="text-muted">
                {progress?.progress || 0}% مكتمل
              </small>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
