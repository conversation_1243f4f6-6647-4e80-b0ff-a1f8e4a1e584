'use client';

import { useState, useEffect, useCallback } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useAuth } from '../../../../contexts/AuthContext';
import ProtectedRoute from '../../../../components/ProtectedRoute';
import api from '../../../../lib/api';
import { toast } from 'react-toastify';
import { 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  Flag,
  ArrowLeft,
  ArrowRight,
  Send,
  HelpCircle,
  Timer
} from 'lucide-react';

export default function TakeQuiz() {
  const params = useParams();
  const router = useRouter();
  const { user } = useAuth();
  
  const [quiz, setQuiz] = useState(null);
  const [questions, setQuestions] = useState([]);
  const [attempt, setAttempt] = useState(null);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState({});
  const [flaggedQuestions, setFlaggedQuestions] = useState(new Set());
  const [timeRemaining, setTimeRemaining] = useState(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [autoSaving, setAutoSaving] = useState(false);

  // بدء الاختبار
  const startQuiz = useCallback(async () => {
    try {
      setLoading(true);
      
      const response = await api.post(`/quizzes/${params.id}/attempts`);
      const { attempt: attemptData, questions: questionsData, quiz: quizData } = response.data;
      
      setAttempt(attemptData);
      setQuestions(questionsData);
      setQuiz(quizData);
      
      // إعداد المؤقت
      if (quizData.settings.timeLimit > 0) {
        setTimeRemaining(quizData.settings.timeLimit * 60); // تحويل إلى ثواني
      }
      
    } catch (error) {
      console.error('Error starting quiz:', error);
      const errorMessage = error.response?.data?.message || 'خطأ في بدء الاختبار';
      toast.error(errorMessage);
      router.push(`/quizzes/${params.id}`);
    } finally {
      setLoading(false);
    }
  }, [params.id, router]);

  useEffect(() => {
    startQuiz();
  }, [startQuiz]);

  // المؤقت
  useEffect(() => {
    if (timeRemaining === null || timeRemaining <= 0) return;

    const timer = setInterval(() => {
      setTimeRemaining(prev => {
        if (prev <= 1) {
          handleSubmitQuiz(true); // إرسال تلقائي عند انتهاء الوقت
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [timeRemaining]);

  // حفظ تلقائي للإجابات
  useEffect(() => {
    if (!attempt || !questions[currentQuestion]) return;

    const questionId = questions[currentQuestion]._id;
    const answer = answers[questionId];
    
    if (answer !== undefined) {
      saveAnswer(questionId, answer);
    }
  }, [answers, currentQuestion, attempt, questions]);

  const saveAnswer = async (questionId, answer) => {
    if (!attempt) return;

    try {
      setAutoSaving(true);
      
      await api.put(`/quizzes/${params.id}/attempts/${attempt._id}`, {
        action: 'save_answer',
        questionId,
        answer
      });
      
    } catch (error) {
      console.error('Error saving answer:', error);
    } finally {
      setAutoSaving(false);
    }
  };

  const handleAnswerChange = (questionId, answer) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }));
  };

  const toggleFlag = (questionIndex) => {
    setFlaggedQuestions(prev => {
      const newSet = new Set(prev);
      if (newSet.has(questionIndex)) {
        newSet.delete(questionIndex);
      } else {
        newSet.add(questionIndex);
      }
      return newSet;
    });
  };

  const handleSubmitQuiz = async (isAutoSubmit = false) => {
    if (!attempt) return;

    if (!isAutoSubmit && !confirm('هل أنت متأكد من إرسال الاختبار؟ لن تتمكن من تعديل إجاباتك بعد الإرسال.')) {
      return;
    }

    try {
      setSubmitting(true);
      
      const response = await api.put(`/quizzes/${params.id}/attempts/${attempt._id}`, {
        submit: true
      });
      
      toast.success('تم إرسال الاختبار بنجاح!');
      router.push(`/quizzes/${params.id}/results/${attempt._id}`);
      
    } catch (error) {
      console.error('Error submitting quiz:', error);
      const errorMessage = error.response?.data?.message || 'خطأ في إرسال الاختبار';
      toast.error(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const renderQuestion = (question) => {
    const questionId = question._id;
    const userAnswer = answers[questionId];

    switch (question.type) {
      case 'multiple_choice':
        return (
          <div className="question-options">
            {question.options.map((option, index) => (
              <div key={option._id} className="form-check mb-2">
                <input
                  className="form-check-input"
                  type="radio"
                  name={`question_${questionId}`}
                  id={`option_${option._id}`}
                  value={option._id}
                  checked={userAnswer === option._id}
                  onChange={(e) => handleAnswerChange(questionId, e.target.value)}
                />
                <label className="form-check-label" htmlFor={`option_${option._id}`}>
                  {option.text}
                </label>
              </div>
            ))}
          </div>
        );

      case 'true_false':
        return (
          <div className="question-options">
            <div className="form-check mb-2">
              <input
                className="form-check-input"
                type="radio"
                name={`question_${questionId}`}
                id={`true_${questionId}`}
                value="true"
                checked={userAnswer === 'true'}
                onChange={(e) => handleAnswerChange(questionId, e.target.value)}
              />
              <label className="form-check-label" htmlFor={`true_${questionId}`}>
                صحيح
              </label>
            </div>
            <div className="form-check mb-2">
              <input
                className="form-check-input"
                type="radio"
                name={`question_${questionId}`}
                id={`false_${questionId}`}
                value="false"
                checked={userAnswer === 'false'}
                onChange={(e) => handleAnswerChange(questionId, e.target.value)}
              />
              <label className="form-check-label" htmlFor={`false_${questionId}`}>
                خطأ
              </label>
            </div>
          </div>
        );

      case 'short_answer':
      case 'fill_blank':
        return (
          <div className="question-input">
            <textarea
              className="form-control"
              rows="3"
              placeholder="اكتب إجابتك هنا..."
              value={userAnswer || ''}
              onChange={(e) => handleAnswerChange(questionId, e.target.value)}
            />
          </div>
        );

      case 'essay':
        return (
          <div className="question-input">
            <textarea
              className="form-control"
              rows="6"
              placeholder="اكتب إجابتك المفصلة هنا..."
              value={userAnswer || ''}
              onChange={(e) => handleAnswerChange(questionId, e.target.value)}
            />
          </div>
        );

      default:
        return <div>نوع سؤال غير مدعوم</div>;
    }
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">جاري التحميل...</span>
        </div>
      </div>
    );
  }

  if (!quiz || !questions.length) {
    return (
      <div className="container py-5">
        <div className="text-center">
          <h3>خطأ في تحميل الاختبار</h3>
          <button className="btn btn-primary" onClick={() => router.back()}>
            العودة
          </button>
        </div>
      </div>
    );
  }

  const currentQ = questions[currentQuestion];
  const progress = ((currentQuestion + 1) / questions.length) * 100;
  const answeredCount = Object.keys(answers).length;

  return (
    <ProtectedRoute>
      <div className="container-fluid py-4">
        {/* Header */}
        <div className="row mb-4">
          <div className="col-12">
            <div className="d-flex justify-content-between align-items-center">
              <div>
                <h2 className="mb-1">{quiz.title}</h2>
                <p className="text-muted mb-0">
                  السؤال {currentQuestion + 1} من {questions.length}
                </p>
              </div>
              <div className="d-flex align-items-center gap-3">
                {autoSaving && (
                  <div className="d-flex align-items-center text-muted">
                    <div className="spinner-border spinner-border-sm me-2" role="status"></div>
                    <small>جاري الحفظ...</small>
                  </div>
                )}
                {timeRemaining !== null && (
                  <div className={`d-flex align-items-center ${timeRemaining < 300 ? 'text-danger' : 'text-primary'}`}>
                    <Timer className="w-4 h-4 me-1" />
                    <span className="fw-bold">{formatTime(timeRemaining)}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="row">
          {/* Main Content */}
          <div className="col-lg-8">
            {/* Progress */}
            <div className="card mb-4">
              <div className="card-body">
                <div className="d-flex justify-content-between align-items-center mb-2">
                  <span>التقدم</span>
                  <span>{Math.round(progress)}%</span>
                </div>
                <div className="progress">
                  <div
                    className="progress-bar"
                    style={{ width: `${progress}%` }}
                  ></div>
                </div>
                <div className="mt-2 text-muted small">
                  تم الإجابة على {answeredCount} من {questions.length} سؤال
                </div>
              </div>
            </div>

            {/* Question */}
            <div className="card mb-4">
              <div className="card-header">
                <div className="d-flex justify-content-between align-items-center">
                  <div className="d-flex align-items-center">
                    <HelpCircle className="w-5 h-5 me-2" />
                    <span className="fw-bold">السؤال {currentQuestion + 1}</span>
                    <span className="badge bg-secondary ms-2">
                      {currentQ.points} نقطة
                    </span>
                  </div>
                  <button
                    className={`btn btn-sm ${flaggedQuestions.has(currentQuestion) ? 'btn-warning' : 'btn-outline-warning'}`}
                    onClick={() => toggleFlag(currentQuestion)}
                  >
                    <Flag className="w-3 h-3 me-1" />
                    {flaggedQuestions.has(currentQuestion) ? 'مُعلم' : 'تعليم'}
                  </button>
                </div>
              </div>
              <div className="card-body">
                <div className="question-text mb-4">
                  <p className="fs-5">{currentQ.question}</p>
                  {currentQ.media && (
                    <div className="question-media mb-3">
                      {currentQ.media.type === 'image' && (
                        <img
                          src={currentQ.media.url}
                          alt={currentQ.media.caption}
                          className="img-fluid rounded"
                        />
                      )}
                    </div>
                  )}
                </div>
                
                {renderQuestion(currentQ)}

                {currentQ.hints && currentQ.hints.length > 0 && (
                  <div className="mt-3">
                    <details>
                      <summary className="text-primary" style={{ cursor: 'pointer' }}>
                        عرض التلميح
                      </summary>
                      <div className="mt-2 p-3 bg-light rounded">
                        {currentQ.hints.map((hint, index) => (
                          <p key={index} className="mb-1 small text-muted">
                            💡 {hint}
                          </p>
                        ))}
                      </div>
                    </details>
                  </div>
                )}
              </div>
            </div>

            {/* Navigation */}
            <div className="d-flex justify-content-between">
              <button
                className="btn btn-outline-primary"
                onClick={() => setCurrentQuestion(prev => Math.max(0, prev - 1))}
                disabled={currentQuestion === 0}
              >
                <ArrowRight className="w-4 h-4 me-1" />
                السابق
              </button>
              
              <div className="d-flex gap-2">
                {currentQuestion === questions.length - 1 ? (
                  <button
                    className="btn btn-success"
                    onClick={() => handleSubmitQuiz()}
                    disabled={submitting}
                  >
                    {submitting ? (
                      <>
                        <div className="spinner-border spinner-border-sm me-2" role="status"></div>
                        جاري الإرسال...
                      </>
                    ) : (
                      <>
                        <Send className="w-4 h-4 me-1" />
                        إرسال الاختبار
                      </>
                    )}
                  </button>
                ) : (
                  <button
                    className="btn btn-primary"
                    onClick={() => setCurrentQuestion(prev => Math.min(questions.length - 1, prev + 1))}
                  >
                    التالي
                    <ArrowLeft className="w-4 h-4 ms-1" />
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="col-lg-4">
            <div className="card sticky-top" style={{ top: '20px' }}>
              <div className="card-header">
                <h5 className="mb-0">نظرة عامة</h5>
              </div>
              <div className="card-body">
                <div className="mb-3">
                  <div className="d-flex justify-content-between mb-2">
                    <span>الأسئلة المجابة:</span>
                    <span>{answeredCount}/{questions.length}</span>
                  </div>
                  <div className="d-flex justify-content-between mb-2">
                    <span>الأسئلة المُعلمة:</span>
                    <span>{flaggedQuestions.size}</span>
                  </div>
                  {timeRemaining !== null && (
                    <div className="d-flex justify-content-between mb-2">
                      <span>الوقت المتبقي:</span>
                      <span className={timeRemaining < 300 ? 'text-danger fw-bold' : ''}>
                        {formatTime(timeRemaining)}
                      </span>
                    </div>
                  )}
                </div>

                {/* Questions Grid */}
                <div className="questions-grid">
                  <h6 className="mb-2">الأسئلة</h6>
                  <div className="d-flex flex-wrap gap-1">
                    {questions.map((_, index) => (
                      <button
                        key={index}
                        className={`btn btn-sm ${
                          index === currentQuestion
                            ? 'btn-primary'
                            : answers[questions[index]._id]
                            ? 'btn-success'
                            : 'btn-outline-secondary'
                        } ${flaggedQuestions.has(index) ? 'border-warning border-2' : ''}`}
                        onClick={() => setCurrentQuestion(index)}
                        style={{ minWidth: '35px' }}
                      >
                        {index + 1}
                        {flaggedQuestions.has(index) && (
                          <Flag className="w-2 h-2 ms-1" />
                        )}
                      </button>
                    ))}
                  </div>
                </div>

                <div className="mt-3 d-grid">
                  <button
                    className="btn btn-warning"
                    onClick={() => handleSubmitQuiz()}
                    disabled={submitting}
                  >
                    <Send className="w-4 h-4 me-1" />
                    إرسال الاختبار
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
