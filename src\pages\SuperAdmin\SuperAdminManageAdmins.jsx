import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Badge, 
  Modal, 
  Form, 
  Alert, 
  Spinner,
  InputGroup,
  Dropdown
} from 'react-bootstrap';
import { useTheme } from '../../context/ThemeContext';
import { supedAdminAPI } from '../../services/api';
import { 
  Users, 
  UserCog, 
  Search, 
  Filter,
  UserPlus,
  Shield,
  AlertTriangle,
  CheckCircle,
  Crown,
  Settings
} from 'lucide-react';

const SuperAdminManageAdmins = () => {
  const { isDark } = useTheme();
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showPromoteModal, setShowPromoteModal] = useState(false);
  const [showDemoteModal, setShowDemoteModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState('admin');

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await supedAdminAPI.getUsers();
      setUsers(response.data);
    } catch (err) {
      setError('فشل في تحميل المستخدمين');
      console.error('Error fetching users:', err);
    } finally {
      setLoading(false);
    }
  };

  const handlePromoteToAdmin = async () => {
    try {
      await supedAdminAPI.updateUserRole(selectedUser._id, 'admin');
      setUsers(users.map(user => 
        user._id === selectedUser._id 
          ? { ...user, role: 'admin' }
          : user
      ));
      setShowPromoteModal(false);
      setSelectedUser(null);
    } catch (err) {
      setError('فشل في ترقية المستخدم');
      console.error('Error promoting user:', err);
    }
  };

  const handleDemoteAdmin = async () => {
    try {
      await supedAdminAPI.updateUserRole(selectedUser._id, 'student');
      setUsers(users.map(user => 
        user._id === selectedUser._id 
          ? { ...user, role: 'student' }
          : user
      ));
      setShowDemoteModal(false);
      setSelectedUser(null);
    } catch (err) {
      setError('فشل في تخفيض رتبة المدير');
      console.error('Error demoting admin:', err);
    }
  };

  const getRoleBadge = (role) => {
    const roleConfig = {
      'super-admin': { variant: 'danger', icon: Crown, label: 'مدير عام' },
      'admin': { variant: 'warning', icon: Shield, label: 'مدير' },
      'student': { variant: 'primary', icon: Users, label: 'طالب' }
    };

    const config = roleConfig[role] || roleConfig.student;
    const IconComponent = config.icon;

    return (
      <Badge bg={config.variant} className="d-flex align-items-center">
        <IconComponent size={12} className="me-1" />
        {config.label}
      </Badge>
    );
  };

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesRole = roleFilter === 'all' || user.role === roleFilter;
    return matchesSearch && matchesRole;
  });

  const adminUsers = filteredUsers.filter(user => user.role === 'admin');
  const superAdminUsers = filteredUsers.filter(user => user.role === 'super-admin');
  const studentUsers = filteredUsers.filter(user => user.role === 'student');

  if (loading) {
    return (
      <div className="text-center py-5">
        <Spinner animation="border" variant="primary" />
        <p className="mt-3">جاري تحميل المستخدمين...</p>
      </div>
    );
  }

  return (
    <div>
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h4 className="mb-1">إدارة المديرين</h4>
          <p className="text-muted mb-0">إدارة صلاحيات المديرين والمديرين العامين</p>
        </div>
        <div className="d-flex gap-2">
          <Badge bg="danger" className="d-flex align-items-center">
            <Crown size={14} className="me-1" />
            {superAdminUsers.length} مدير عام
          </Badge>
          <Badge bg="warning" className="d-flex align-items-center">
            <Shield size={14} className="me-1" />
            {adminUsers.length} مدير
          </Badge>
        </div>
      </div>

      {error && (
        <Alert variant="danger" className="mb-4">
          <AlertTriangle size={18} className="me-2" />
          {error}
        </Alert>
      )}

      {/* Filters */}
      <Card className={`${isDark ? 'bg-dark text-light' : 'bg-white'} border-0 shadow-sm mb-4`}>
        <Card.Body>
          <div className="row g-3">
            <div className="col-md-6">
              <InputGroup>
                <InputGroup.Text>
                  <Search size={16} />
                </InputGroup.Text>
                <Form.Control
                  type="text"
                  placeholder="البحث بالاسم أو البريد الإلكتروني..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </InputGroup>
            </div>
            <div className="col-md-3">
              <Dropdown>
                <Dropdown.Toggle variant="outline-secondary" className="w-100 d-flex align-items-center justify-content-between">
                  <span className="d-flex align-items-center">
                    <Filter size={16} className="me-2" />
                    {roleFilter === 'all' ? 'جميع الأدوار' : 
                     roleFilter === 'super-admin' ? 'مدير عام' :
                     roleFilter === 'admin' ? 'مدير' : 'طالب'}
                  </span>
                </Dropdown.Toggle>
                <Dropdown.Menu>
                  <Dropdown.Item onClick={() => setRoleFilter('all')}>جميع الأدوار</Dropdown.Item>
                  <Dropdown.Item onClick={() => setRoleFilter('super-admin')}>مدير عام</Dropdown.Item>
                  <Dropdown.Item onClick={() => setRoleFilter('admin')}>مدير</Dropdown.Item>
                  <Dropdown.Item onClick={() => setRoleFilter('student')}>طالب</Dropdown.Item>
                </Dropdown.Menu>
              </Dropdown>
            </div>
            <div className="col-md-3">
              <div className="text-muted small">
                إجمالي: {filteredUsers.length} مستخدم
              </div>
            </div>
          </div>
        </Card.Body>
      </Card>

      {/* Super Admins Section */}
      <Card className={`${isDark ? 'bg-dark text-light' : 'bg-white'} border-0 shadow-sm mb-4`}>
        <Card.Header className="bg-transparent border-0 d-flex justify-content-between align-items-center">
          <h6 className="mb-0 d-flex align-items-center">
            <Crown size={18} className="me-2 text-danger" />
            المديرين العامين ({superAdminUsers.length})
          </h6>
        </Card.Header>
        <Card.Body className="p-0">
          {superAdminUsers.length > 0 ? (
            <Table responsive className={`mb-0 ${isDark ? 'table-dark' : ''}`}>
              <thead className={isDark ? 'table-secondary' : 'table-light'}>
                <tr>
                  <th>المستخدم</th>
                  <th>البريد الإلكتروني</th>
                  <th>تاريخ التسجيل</th>
                  <th>آخر نشاط</th>
                </tr>
              </thead>
              <tbody>
                {superAdminUsers.map((user) => (
                  <tr key={user._id}>
                    <td>
                      <div className="d-flex align-items-center">
                        <div className="bg-danger rounded-circle d-flex align-items-center justify-content-center me-3" 
                             style={{ width: '40px', height: '40px' }}>
                          <Crown size={20} className="text-white" />
                        </div>
                        <div>
                          <div className="fw-bold">{user.name}</div>
                          <small className="text-muted">ID: {user._id.slice(-6)}</small>
                        </div>
                      </div>
                    </td>
                    <td>{user.email}</td>
                    <td>
                      <small>
                        {new Date(user.createdAt).toLocaleDateString('ar-SA')}
                      </small>
                    </td>
                    <td>
                      <small className="text-muted">
                        {user.lastActive ? 
                          new Date(user.lastActive).toLocaleDateString('ar-SA') : 
                          'لم يسجل دخول'
                        }
                      </small>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          ) : (
            <div className="text-center py-4">
              <Crown size={48} className="text-muted mb-3" />
              <p className="text-muted">لا يوجد مديرين عامين</p>
            </div>
          )}
        </Card.Body>
      </Card>

      {/* Admins Section */}
      <Card className={`${isDark ? 'bg-dark text-light' : 'bg-white'} border-0 shadow-sm mb-4`}>
        <Card.Header className="bg-transparent border-0 d-flex justify-content-between align-items-center">
          <h6 className="mb-0 d-flex align-items-center">
            <Shield size={18} className="me-2 text-warning" />
            المديرين ({adminUsers.length})
          </h6>
        </Card.Header>
        <Card.Body className="p-0">
          {adminUsers.length > 0 ? (
            <Table responsive className={`mb-0 ${isDark ? 'table-dark' : ''}`}>
              <thead className={isDark ? 'table-secondary' : 'table-light'}>
                <tr>
                  <th>المستخدم</th>
                  <th>البريد الإلكتروني</th>
                  <th>تاريخ التسجيل</th>
                  <th>آخر نشاط</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {adminUsers.map((user) => (
                  <tr key={user._id}>
                    <td>
                      <div className="d-flex align-items-center">
                        <div className="bg-warning rounded-circle d-flex align-items-center justify-content-center me-3" 
                             style={{ width: '40px', height: '40px' }}>
                          <Shield size={20} className="text-white" />
                        </div>
                        <div>
                          <div className="fw-bold">{user.name}</div>
                          <small className="text-muted">ID: {user._id.slice(-6)}</small>
                        </div>
                      </div>
                    </td>
                    <td>{user.email}</td>
                    <td>
                      <small>
                        {new Date(user.createdAt).toLocaleDateString('ar-SA')}
                      </small>
                    </td>
                    <td>
                      <small className="text-muted">
                        {user.lastActive ? 
                          new Date(user.lastActive).toLocaleDateString('ar-SA') : 
                          'لم يسجل دخول'
                        }
                      </small>
                    </td>
                    <td>
                      <Button
                        variant="outline-danger"
                        size="sm"
                        onClick={() => {
                          setSelectedUser(user);
                          setShowDemoteModal(true);
                        }}
                      >
                        تخفيض الرتبة
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          ) : (
            <div className="text-center py-4">
              <Shield size={48} className="text-muted mb-3" />
              <p className="text-muted">لا يوجد مديرين</p>
            </div>
          )}
        </Card.Body>
      </Card>

      {/* Students Section - Potential Admins */}
      <Card className={`${isDark ? 'bg-dark text-light' : 'bg-white'} border-0 shadow-sm`}>
        <Card.Header className="bg-transparent border-0 d-flex justify-content-between align-items-center">
          <h6 className="mb-0 d-flex align-items-center">
            <Users size={18} className="me-2 text-primary" />
            الطلاب - مرشحين للترقية ({studentUsers.length})
          </h6>
        </Card.Header>
        <Card.Body className="p-0">
          {studentUsers.length > 0 ? (
            <Table responsive className={`mb-0 ${isDark ? 'table-dark' : ''}`}>
              <thead className={isDark ? 'table-secondary' : 'table-light'}>
                <tr>
                  <th>المستخدم</th>
                  <th>البريد الإلكتروني</th>
                  <th>تاريخ التسجيل</th>
                  <th>آخر نشاط</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {studentUsers.slice(0, 10).map((user) => (
                  <tr key={user._id}>
                    <td>
                      <div className="d-flex align-items-center">
                        <div className="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" 
                             style={{ width: '40px', height: '40px' }}>
                          <span className="text-white fw-bold">
                            {user.name.charAt(0).toUpperCase()}
                          </span>
                        </div>
                        <div>
                          <div className="fw-bold">{user.name}</div>
                          <small className="text-muted">ID: {user._id.slice(-6)}</small>
                        </div>
                      </div>
                    </td>
                    <td>{user.email}</td>
                    <td>
                      <small>
                        {new Date(user.createdAt).toLocaleDateString('ar-SA')}
                      </small>
                    </td>
                    <td>
                      <small className="text-muted">
                        {user.lastActive ? 
                          new Date(user.lastActive).toLocaleDateString('ar-SA') : 
                          'لم يسجل دخول'
                        }
                      </small>
                    </td>
                    <td>
                      <Button
                        variant="outline-success"
                        size="sm"
                        onClick={() => {
                          setSelectedUser(user);
                          setShowPromoteModal(true);
                        }}
                      >
                        ترقية لمدير
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          ) : (
            <div className="text-center py-4">
              <Users size={48} className="text-muted mb-3" />
              <p className="text-muted">لا يوجد طلاب</p>
            </div>
          )}
        </Card.Body>
      </Card>

      {/* Promote Modal */}
      <Modal show={showPromoteModal} onHide={() => setShowPromoteModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title className="text-success">ترقية إلى مدير</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="text-center">
            <Shield size={48} className="text-success mb-3" />
            <p>هل أنت متأكد من ترقية: <strong>{selectedUser?.name}</strong> إلى مدير؟</p>
            <p className="text-muted">سيحصل على صلاحيات إدارة الدورات والمستخدمين.</p>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowPromoteModal(false)}>
            إلغاء
          </Button>
          <Button variant="success" onClick={handlePromoteToAdmin}>
            ترقية إلى مدير
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Demote Modal */}
      <Modal show={showDemoteModal} onHide={() => setShowDemoteModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title className="text-danger">تخفيض الرتبة</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="text-center">
            <AlertTriangle size={48} className="text-danger mb-3" />
            <p>هل أنت متأكد من تخفيض رتبة: <strong>{selectedUser?.name}</strong> إلى طالب؟</p>
            <p className="text-muted">سيفقد جميع صلاحيات الإدارة.</p>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDemoteModal(false)}>
            إلغاء
          </Button>
          <Button variant="danger" onClick={handleDemoteAdmin}>
            تخفيض الرتبة
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default SuperAdminManageAdmins;
