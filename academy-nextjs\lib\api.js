import axios from 'axios';

// إنشاء instance من axios مع الإعدادات الافتراضية
const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// إضافة interceptor للطلبات لإضافة token إذا كان متوفراً
api.interceptors.request.use(
  (config) => {
    // الحصول على token من localStorage إذا كان متوفراً
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// إضافة interceptor للاستجابات للتعامل مع الأخطاء
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // التعامل مع خطأ 401 (غير مصرح)
    if (error.response?.status === 401) {
      // إزالة token من localStorage
      if (typeof window !== 'undefined') {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        // إعادة توجيه إلى صفحة تسجيل الدخول
        window.location.href = '/login';
      }
    }
    
    // التعامل مع خطأ 403 (ممنوع)
    if (error.response?.status === 403) {
      if (typeof window !== 'undefined') {
        window.location.href = '/unauthorized';
      }
    }
    
    return Promise.reject(error);
  }
);

export default api;
