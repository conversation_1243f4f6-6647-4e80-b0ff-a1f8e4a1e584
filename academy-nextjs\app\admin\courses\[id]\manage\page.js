'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useAuth } from '../../../../../contexts/AuthContext';
import ProtectedRoute from '../../../../../components/ProtectedRoute';
import api from '../../../../../lib/api';
import { toast } from 'react-toastify';
import {
  BookOpen,
  Plus,
  Edit,
  Trash2,
  Play,
  FileText,
  Video,
  Settings,
  Users,
  BarChart3,
  ArrowLeft,
  Save,
  Eye,
  Star
} from 'lucide-react';

export default function ManageCourse() {
  const { user } = useAuth();
  const router = useRouter();
  const params = useParams();
  const courseId = params.id;

  const [course, setCourse] = useState(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('content');
  const [editingCourse, setEditingCourse] = useState(false);
  const [courseForm, setCourseForm] = useState({});

  useEffect(() => {
    fetchCourse();
  }, [courseId]);

  const fetchCourse = async () => {
    try {
      setLoading(true);
      const response = await api.get(`/courses/${courseId}`);
      setCourse(response.data.course);
      setCourseForm(response.data.course);
    } catch (error) {
      console.error('Error fetching course:', error);
      toast.error('خطأ في جلب بيانات الدورة');
    } finally {
      setLoading(false);
    }
  };

  const handleCourseUpdate = async (e) => {
    e.preventDefault();
    
    try {
      await api.put(`/courses/${courseId}`, courseForm);
      toast.success('تم تحديث الدورة بنجاح');
      setCourse(courseForm);
      setEditingCourse(false);
    } catch (error) {
      console.error('Error updating course:', error);
      toast.error('خطأ في تحديث الدورة');
    }
  };

  const addUnit = async () => {
    const title = prompt('عنوان الوحدة الجديدة:');
    if (!title) return;

    try {
      const response = await api.post(`/courses/${courseId}/units`, {
        title,
        description: ''
      });
      
      toast.success('تم إضافة الوحدة بنجاح');
      fetchCourse(); // إعادة جلب البيانات
    } catch (error) {
      console.error('Error adding unit:', error);
      toast.error('خطأ في إضافة الوحدة');
    }
  };

  if (loading) {
    return (
      <ProtectedRoute allowedRoles={['admin', 'super-admin']}>
        <div className="container-fluid py-4">
          <div className="text-center">
            <div className="spinner-border" role="status">
              <span className="visually-hidden">جاري التحميل...</span>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  if (!course) {
    return (
      <ProtectedRoute allowedRoles={['admin', 'super-admin']}>
        <div className="container-fluid py-4">
          <div className="alert alert-danger">
            الدورة غير موجودة
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute allowedRoles={['admin', 'super-admin']}>
      <div className="container-fluid py-4">
        {/* Header */}
        <div className="row mb-4">
          <div className="col-12">
            <div className="d-flex justify-content-between align-items-center">
              <div>
                <button
                  className="btn btn-outline-secondary btn-sm mb-2"
                  onClick={() => router.push('/admin/courses')}
                >
                  <ArrowLeft className="w-4 h-4 me-1" />
                  العودة للدورات
                </button>
                <h2 className="mb-1">{course.title}</h2>
                <p className="text-muted mb-0">
                  إدارة محتوى الدورة والوحدات والدروس
                </p>
              </div>
              <div className="d-flex gap-2">
                <button
                  className="btn btn-outline-primary"
                  onClick={() => setEditingCourse(true)}
                >
                  <Edit className="w-4 h-4 me-1" />
                  تعديل الدورة
                </button>
                <button
                  className="btn btn-success"
                  onClick={() => router.push(`/courses/${courseId}`)}
                >
                  <Eye className="w-4 h-4 me-1" />
                  معاينة
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="row mb-4">
          <div className="col-12">
            <ul className="nav nav-tabs">
              <li className="nav-item">
                <button
                  className={`nav-link ${activeTab === 'content' ? 'active' : ''}`}
                  onClick={() => setActiveTab('content')}
                >
                  <BookOpen className="w-4 h-4 me-1" />
                  المحتوى
                </button>
              </li>
              <li className="nav-item">
                <button
                  className={`nav-link ${activeTab === 'settings' ? 'active' : ''}`}
                  onClick={() => setActiveTab('settings')}
                >
                  <Settings className="w-4 h-4 me-1" />
                  الإعدادات
                </button>
              </li>
              <li className="nav-item">
                <button
                  className={`nav-link ${activeTab === 'students' ? 'active' : ''}`}
                  onClick={() => setActiveTab('students')}
                >
                  <Users className="w-4 h-4 me-1" />
                  الطلاب
                </button>
              </li>
              <li className="nav-item">
                <button
                  className={`nav-link ${activeTab === 'analytics' ? 'active' : ''}`}
                  onClick={() => setActiveTab('analytics')}
                >
                  <BarChart3 className="w-4 h-4 me-1" />
                  الإحصائيات
                </button>
              </li>
            </ul>
          </div>
        </div>

        {/* Content */}
        {activeTab === 'content' && (
          <div className="row">
            <div className="col-12">
              <div className="card">
                <div className="card-header d-flex justify-content-between align-items-center">
                  <h5 className="mb-0">وحدات الدورة</h5>
                  <button
                    className="btn btn-primary"
                    onClick={addUnit}
                  >
                    <Plus className="w-4 h-4 me-1" />
                    إضافة وحدة
                  </button>
                </div>
                <div className="card-body">
                  {course.units && course.units.length > 0 ? (
                    <div className="list-group">
                      {course.units.map((unit, index) => (
                        <div key={unit._id} className="list-group-item">
                          <div className="d-flex justify-content-between align-items-center">
                            <div>
                              <h6 className="mb-1">
                                الوحدة {index + 1}: {unit.title}
                              </h6>
                              <p className="mb-1 text-muted">{unit.description}</p>
                              <small className="text-muted">
                                {unit.lessons?.length || 0} درس
                                {unit.quiz?.questions?.length > 0 && ` • اختبار (${unit.quiz.questions.length} سؤال)`}
                              </small>
                            </div>
                            <div className="d-flex gap-2">
                              <button
                                className="btn btn-outline-primary btn-sm"
                                onClick={() => router.push(`/admin/courses/${courseId}/units/${unit._id}/manage`)}
                              >
                                <Edit className="w-4 h-4" />
                                إدارة
                              </button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-5">
                      <BookOpen className="w-12 h-12 text-muted mx-auto mb-3" />
                      <h5 className="text-muted">لا توجد وحدات بعد</h5>
                      <p className="text-muted">ابدأ بإضافة وحدة جديدة للدورة</p>
                      <button
                        className="btn btn-primary"
                        onClick={addUnit}
                      >
                        <Plus className="w-4 h-4 me-1" />
                        إضافة أول وحدة
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Course Edit Modal */}
        {editingCourse && (
          <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
            <div className="modal-dialog modal-lg">
              <div className="modal-content">
                <div className="modal-header">
                  <h5 className="modal-title">تعديل الدورة</h5>
                  <button
                    type="button"
                    className="btn-close"
                    onClick={() => setEditingCourse(false)}
                  ></button>
                </div>
                <form onSubmit={handleCourseUpdate}>
                  <div className="modal-body">
                    <div className="row">
                      <div className="col-12 mb-3">
                        <label className="form-label">عنوان الدورة</label>
                        <input
                          type="text"
                          className="form-control"
                          value={courseForm.title || ''}
                          onChange={(e) => setCourseForm(prev => ({
                            ...prev,
                            title: e.target.value
                          }))}
                          required
                        />
                      </div>
                      <div className="col-12 mb-3">
                        <label className="form-label">وصف الدورة</label>
                        <textarea
                          className="form-control"
                          rows="4"
                          value={courseForm.description || ''}
                          onChange={(e) => setCourseForm(prev => ({
                            ...prev,
                            description: e.target.value
                          }))}
                          required
                        ></textarea>
                      </div>
                      <div className="col-md-6 mb-3">
                        <label className="form-label">السعر</label>
                        <input
                          type="number"
                          className="form-control"
                          value={courseForm.price || 0}
                          onChange={(e) => setCourseForm(prev => ({
                            ...prev,
                            price: parseFloat(e.target.value) || 0
                          }))}
                          min="0"
                          step="0.01"
                        />
                      </div>
                      <div className="col-md-6 mb-3">
                        <div className="form-check mt-4">
                          <input
                            className="form-check-input"
                            type="checkbox"
                            checked={courseForm.isActive || false}
                            onChange={(e) => setCourseForm(prev => ({
                              ...prev,
                              isActive: e.target.checked
                            }))}
                          />
                          <label className="form-check-label">
                            دورة نشطة
                          </label>
                        </div>
                        <div className="form-check">
                          <input
                            className="form-check-input"
                            type="checkbox"
                            checked={courseForm.isFeatured || false}
                            onChange={(e) => setCourseForm(prev => ({
                              ...prev,
                              isFeatured: e.target.checked
                            }))}
                          />
                          <label className="form-check-label">
                            دورة مميزة
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="modal-footer">
                    <button
                      type="button"
                      className="btn btn-secondary"
                      onClick={() => setEditingCourse(false)}
                    >
                      إلغاء
                    </button>
                    <button
                      type="submit"
                      className="btn btn-primary"
                    >
                      <Save className="w-4 h-4 me-1" />
                      حفظ التغييرات
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        )}
      </div>
    </ProtectedRoute>
  );
}
