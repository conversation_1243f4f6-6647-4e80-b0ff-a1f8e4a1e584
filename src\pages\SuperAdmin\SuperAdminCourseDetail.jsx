import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  <PERSON>, 
  <PERSON>ge, 
  <PERSON>, 
  <PERSON>,
  Row,
  Col,
  Table,
  Modal
} from 'react-bootstrap';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { useTheme } from '../../context/ThemeContext';
import { coursesAPI } from '../../services/api';
import { API_CONFIG } from '../../config/api';
import { 
  ArrowLeft,
  Edit,
  Trash2,
  Users,
  Clock,
  BookOpen,
  Play,
  CheckCircle,
  AlertTriangle,
  Eye,
  Settings
} from 'lucide-react';

const SuperAdminCourseDetail = () => {
  const { id } = useParams();
  const { isDark } = useTheme();
  const navigate = useNavigate();
  const [course, setCourse] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  useEffect(() => {
    fetchCourse();
  }, [id]);

  const fetchCourse = async () => {
    try {
      setLoading(true);
      const response = await coursesAPI.getById(id);
      setCourse(response.data);
    } catch (err) {
      setError('فشل في تحميل بيانات الدورة');
      console.error('Error fetching course:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    try {
      await coursesAPI.delete(id);
      navigate('/super-admin/courses');
    } catch (err) {
      setError('فشل في حذف الدورة');
      console.error('Error deleting course:', err);
    }
    setShowDeleteModal(false);
  };

  const toggleStatus = async () => {
    try {
      const newStatus = !course.isActive;
      await coursesAPI.update(id, { isActive: newStatus });
      setCourse(prev => ({ ...prev, isActive: newStatus }));
    } catch (err) {
      setError('فشل في تغيير حالة الدورة');
      console.error('Error toggling status:', err);
    }
  };

  if (loading) {
    return (
      <div className="text-center py-5">
        <Spinner animation="border" variant="primary" />
        <p className="mt-3">جاري تحميل بيانات الدورة...</p>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="danger">
        <AlertTriangle size={20} className="me-2" />
        {error}
      </Alert>
    );
  }

  if (!course) {
    return (
      <Alert variant="warning">
        <AlertTriangle size={20} className="me-2" />
        الدورة غير موجودة
      </Alert>
    );
  }

  return (
    <div>
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h4 className="mb-1">{course.title}</h4>
          <p className="text-muted mb-0">تفاصيل الدورة - إدارة المدير العام</p>
        </div>
        <div className="d-flex gap-2">
          <Button 
            variant={course.isActive ? "success" : "secondary"}
            onClick={toggleStatus}
            className="d-flex align-items-center"
          >
            {course.isActive ? <CheckCircle size={18} className="me-2" /> : <AlertTriangle size={18} className="me-2" />}
            {course.isActive ? 'نشطة' : 'غير نشطة'}
          </Button>
          <Button 
            as={Link}
            to={`/courses/${id}`}
            variant="outline-info"
            className="d-flex align-items-center"
          >
            <Eye size={18} className="me-2" />
            عرض عام
          </Button>
          <Button 
            as={Link}
            to={`/super-admin/edit-course/${id}`}
            variant="outline-warning"
            className="d-flex align-items-center"
          >
            <Edit size={18} className="me-2" />
            تعديل
          </Button>
          <Button 
            variant="outline-danger"
            onClick={() => setShowDeleteModal(true)}
            className="d-flex align-items-center"
          >
            <Trash2 size={18} className="me-2" />
            حذف
          </Button>
          <Button 
            variant="outline-secondary" 
            onClick={() => navigate('/super-admin/courses')}
            className="d-flex align-items-center"
          >
            <ArrowLeft size={18} className="me-2" />
            العودة
          </Button>
        </div>
      </div>

      <Row className="g-4">
        {/* Course Info */}
        <Col lg={8}>
          {/* Media */}
          <Card className={`${isDark ? 'bg-dark text-light' : 'bg-white'} border-0 shadow-sm mb-4`}>
            <Card.Body className="p-0">
              {course.video ? (
                <video
                  controls
                  className="w-100"
                  style={{ height: '400px', objectFit: 'cover' }}
                  poster={course.image ? `${API_CONFIG.BASE_URL}/${course.image}` : undefined}
                >
                  <source src={`${API_CONFIG.BASE_URL}/${course.video}`} type="video/mp4" />
                  متصفحك لا يدعم تشغيل الفيديو.
                </video>
              ) : course.image ? (
                <img
                  src={`${API_CONFIG.BASE_URL}/${course.image}`}
                  alt={course.title}
                  className="w-100"
                  style={{ height: '400px', objectFit: 'cover' }}
                />
              ) : (
                <div 
                  className="w-100 d-flex align-items-center justify-content-center bg-light"
                  style={{ height: '400px' }}
                >
                  <BookOpen size={64} className="text-muted" />
                </div>
              )}
            </Card.Body>
          </Card>

          {/* Description */}
          <Card className={`${isDark ? 'bg-dark text-light' : 'bg-white'} border-0 shadow-sm mb-4`}>
            <Card.Header className="bg-transparent border-0">
              <h6 className="mb-0">وصف الدورة</h6>
            </Card.Header>
            <Card.Body>
              <p className="mb-0">{course.description}</p>
            </Card.Body>
          </Card>

          {/* Units and Lessons */}
          {course.units && course.units.length > 0 && (
            <Card className={`${isDark ? 'bg-dark text-light' : 'bg-white'} border-0 shadow-sm mb-4`}>
              <Card.Header className="bg-transparent border-0">
                <h6 className="mb-0">محتوى الدورة</h6>
              </Card.Header>
              <Card.Body>
                {course.units.map((unit, unitIndex) => (
                  <Card key={unitIndex} className={`mb-3 ${isDark ? 'bg-secondary' : 'bg-light'}`}>
                    <Card.Header className="bg-transparent">
                      <h6 className="mb-0">
                        الوحدة {unitIndex + 1}: {unit.title}
                      </h6>
                      {unit.description && (
                        <small className="text-muted">{unit.description}</small>
                      )}
                    </Card.Header>
                    <Card.Body>
                      {unit.lessons && unit.lessons.length > 0 ? (
                        <div className="list-group list-group-flush">
                          {unit.lessons.map((lesson, lessonIndex) => (
                            <div key={lessonIndex} className="list-group-item bg-transparent border-0 px-0 py-2">
                              <div className="d-flex justify-content-between align-items-center">
                                <div className="d-flex align-items-center">
                                  <Play size={16} className="me-2 text-primary" />
                                  <span>{lesson.title}</span>
                                </div>
                                {lesson.duration && (
                                  <Badge bg="secondary" className="d-flex align-items-center">
                                    <Clock size={12} className="me-1" />
                                    {lesson.duration}
                                  </Badge>
                                )}
                              </div>
                              {lesson.content && (
                                <small className="text-muted d-block mt-1 ms-4">
                                  {lesson.content.substring(0, 100)}...
                                </small>
                              )}
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-muted mb-0">لا توجد دروس في هذه الوحدة</p>
                      )}
                    </Card.Body>
                  </Card>
                ))}
              </Card.Body>
            </Card>
          )}
        </Col>

        {/* Sidebar */}
        <Col lg={4}>
          {/* Course Stats */}
          <Card className={`${isDark ? 'bg-dark text-light' : 'bg-white'} border-0 shadow-sm mb-4`}>
            <Card.Header className="bg-transparent border-0">
              <h6 className="mb-0 d-flex align-items-center">
                <Settings size={18} className="me-2" />
                معلومات الدورة
              </h6>
            </Card.Header>
            <Card.Body>
              <Table borderless className={isDark ? 'table-dark' : ''}>
                <tbody>
                  <tr>
                    <td><strong>الحالة:</strong></td>
                    <td>
                      <Badge bg={course.isActive ? 'success' : 'secondary'}>
                        {course.isActive ? 'نشطة' : 'غير نشطة'}
                      </Badge>
                    </td>
                  </tr>
                  <tr>
                    <td><strong>المستوى:</strong></td>
                    <td>
                      <Badge bg="info">
                        {course.level === 'beginner' ? 'مبتدئ' : 
                         course.level === 'intermediate' ? 'متوسط' : 'متقدم'}
                      </Badge>
                    </td>
                  </tr>
                  <tr>
                    <td><strong>التصنيف:</strong></td>
                    <td>{course.category || 'غير محدد'}</td>
                  </tr>
                  <tr>
                    <td><strong>المدة:</strong></td>
                    <td>{course.duration || 'غير محدد'}</td>
                  </tr>
                  <tr>
                    <td><strong>السعر:</strong></td>
                    <td>{course.price ? `${course.price} ريال` : 'مجاني'}</td>
                  </tr>
                  <tr>
                    <td><strong>المسجلين:</strong></td>
                    <td>
                      <Badge bg="primary" className="d-flex align-items-center">
                        <Users size={12} className="me-1" />
                        {course.enrollmentCount || 0}
                      </Badge>
                    </td>
                  </tr>
                  <tr>
                    <td><strong>تاريخ الإنشاء:</strong></td>
                    <td>{new Date(course.createdAt).toLocaleDateString('ar-SA')}</td>
                  </tr>
                  <tr>
                    <td><strong>آخر تحديث:</strong></td>
                    <td>{new Date(course.updatedAt).toLocaleDateString('ar-SA')}</td>
                  </tr>
                </tbody>
              </Table>
            </Card.Body>
          </Card>

          {/* Quick Actions */}
          <Card className={`${isDark ? 'bg-dark text-light' : 'bg-white'} border-0 shadow-sm`}>
            <Card.Header className="bg-transparent border-0">
              <h6 className="mb-0">إجراءات سريعة</h6>
            </Card.Header>
            <Card.Body className="d-grid gap-2">
              <Button 
                as={Link}
                to={`/super-admin/edit-course/${id}`}
                variant="primary"
                className="d-flex align-items-center justify-content-center"
              >
                <Edit size={16} className="me-2" />
                تعديل الدورة
              </Button>
              <Button 
                variant={course.isActive ? "warning" : "success"}
                onClick={toggleStatus}
                className="d-flex align-items-center justify-content-center"
              >
                {course.isActive ? (
                  <>
                    <AlertTriangle size={16} className="me-2" />
                    إيقاف الدورة
                  </>
                ) : (
                  <>
                    <CheckCircle size={16} className="me-2" />
                    تفعيل الدورة
                  </>
                )}
              </Button>
              <Button 
                as={Link}
                to={`/courses/${id}`}
                variant="outline-info"
                className="d-flex align-items-center justify-content-center"
              >
                <Eye size={16} className="me-2" />
                عرض للطلاب
              </Button>
              <Button 
                variant="outline-danger"
                onClick={() => setShowDeleteModal(true)}
                className="d-flex align-items-center justify-content-center"
              >
                <Trash2 size={16} className="me-2" />
                حذف الدورة
              </Button>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title className="text-danger">تأكيد الحذف</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="text-center">
            <AlertTriangle size={48} className="text-danger mb-3" />
            <p>هل أنت متأكد من حذف الدورة: <strong>{course.title}</strong>؟</p>
            <p className="text-muted">سيتم حذف جميع البيانات المرتبطة بهذه الدورة نهائياً.</p>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
            إلغاء
          </Button>
          <Button variant="danger" onClick={handleDelete}>
            حذف الدورة
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default SuperAdminCourseDetail;
