import { NextResponse } from 'next/server';
import { withAuth } from '../../../../../lib/auth';
import connectDB from '../../../../../lib/mongodb';
import Quiz from '../../../../../models/Quiz';
import QuizAttempt from '../../../../../models/QuizAttempt';
import Question from '../../../../../models/Question';
import Enrollment from '../../../../../models/Enrollment';
import mongoose from 'mongoose';

// GET - جلب محاولات الاختبار
export const GET = withAuth(async (request, { params }) => {
  try {
    await connectDB();
    
    const { id: quizId } = params;
    const { searchParams } = new URL(request.url);
    const studentId = searchParams.get('studentId');
    
    // بناء الاستعلام
    const query = { quiz: quizId };
    
    // للمدراء: يمكن عرض محاولات جميع الطلاب
    if (['admin', 'super-admin'].includes(request.user.role)) {
      if (studentId) {
        query.student = studentId;
      }
    } else {
      // للطلاب: عرض محاولاتهم فقط
      query.student = request.user._id;
    }
    
    const attempts = await QuizAttempt.find(query)
      .populate('student', 'name email')
      .populate('quiz', 'title settings')
      .sort({ attemptNumber: -1 })
      .lean();
    
    return NextResponse.json({
      attempts
    }, { status: 200 });

  } catch (error) {
    console.error('Quiz attempts fetch error:', error);
    return NextResponse.json(
      { message: 'خطأ في جلب محاولات الاختبار' },
      { status: 500 }
    );
  }
});

// POST - بدء محاولة جديدة
export const POST = withAuth(async (request, { params }) => {
  try {
    await connectDB();
    
    const { id: quizId } = params;
    
    // جلب الاختبار
    const quiz = await Quiz.findById(quizId).populate('questions');
    if (!quiz) {
      return NextResponse.json(
        { message: 'الاختبار غير موجود' },
        { status: 404 }
      );
    }

    // التحقق من التسجيل في الدورة
    const enrollment = await Enrollment.findOne({
      student: request.user._id,
      course: quiz.course,
      isActive: true
    });

    if (!enrollment) {
      return NextResponse.json(
        { message: 'يجب التسجيل في الدورة أولاً' },
        { status: 403 }
      );
    }

    // التحقق من إمكانية أداء الاختبار
    if (!quiz.canTakeQuiz(request.user, enrollment)) {
      return NextResponse.json(
        { message: 'لا يمكن أداء هذا الاختبار حالياً' },
        { status: 403 }
      );
    }

    // التحقق من عدد المحاولات
    const existingAttempts = await QuizAttempt.countDocuments({
      quiz: quizId,
      student: request.user._id
    });

    if (existingAttempts >= quiz.settings.attempts) {
      return NextResponse.json(
        { message: 'لقد استنفدت جميع المحاولات المسموحة' },
        { status: 403 }
      );
    }

    // التحقق من وجود محاولة قيد التقدم
    const inProgressAttempt = await QuizAttempt.findOne({
      quiz: quizId,
      student: request.user._id,
      status: 'in_progress'
    });

    if (inProgressAttempt) {
      return NextResponse.json({
        message: 'لديك محاولة قيد التقدم',
        attempt: inProgressAttempt
      }, { status: 200 });
    }

    // إنشاء محاولة جديدة
    const attempt = new QuizAttempt({
      quiz: quizId,
      student: request.user._id,
      enrollment: enrollment._id,
      attemptNumber: existingAttempts + 1,
      totalPoints: quiz.questions.reduce((sum, q) => sum + (q.points || 1), 0)
    });

    await attempt.save();

    // جلب الأسئلة (مع أو بدون ترتيب عشوائي)
    let questions = quiz.questions;
    if (quiz.settings.randomizeQuestions) {
      questions = [...questions].sort(() => Math.random() - 0.5);
    }

    // إخفاء الإجابات الصحيحة
    const questionsForStudent = questions.map(q => ({
      _id: q._id,
      type: q.type,
      question: q.question,
      options: q.options?.map(opt => ({
        _id: opt._id,
        text: opt.text
      })),
      points: q.points,
      hints: q.hints,
      media: q.media
    }));

    return NextResponse.json({
      message: 'تم بدء الاختبار بنجاح',
      attempt: {
        _id: attempt._id,
        attemptNumber: attempt.attemptNumber,
        startedAt: attempt.startedAt,
        timeLimit: quiz.settings.timeLimit
      },
      questions: questionsForStudent,
      quiz: {
        title: quiz.title,
        description: quiz.description,
        settings: quiz.settings
      }
    }, { status: 201 });

  } catch (error) {
    console.error('Quiz attempt creation error:', error);
    return NextResponse.json(
      { message: 'خطأ في بدء الاختبار' },
      { status: 500 }
    );
  }
});
