import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import 'bootstrap/dist/css/bootstrap.min.css';
import { AuthProvider } from './context/AuthContext.jsx';
import { ThemeProvider } from './context/ThemeContext.jsx';
import AppNavbar from './components/Navbar.jsx';
import ProtectedRoute from './components/ProtectedRoute.jsx';
import Chatbot from './components/Chatbot.jsx';
import AdminLayout from './pages/Admin/AdminLayout.jsx';


// Pages
import Home from './pages/Home.jsx';
import Login from './pages/Login.jsx';
import Register from './pages/Register.jsx';
import Courses from './pages/Courses.jsx';
import CourseDetail from './pages/CourseDetail.jsx';
import StudentCourse from './pages/StudentCourse.jsx';
import HonorBoard from './pages/HonorBoard.jsx';
import PublicProfile from './pages/PublicProfile.jsx';
import Profile from './pages/Profile.jsx';

// Admin Pages
import Dashboard from './pages/Admin/Dashboard.jsx';
import ManageCourses from './pages/Admin/ManageCourses.jsx';
import ManageUsers from './pages/Admin/ManageUsers.jsx';
import AddCourse from './pages/Admin/AddCourse.jsx';
import EditCourse from './pages/Admin/EditCourse.jsx';
import PermissionsOverview from './pages/Admin/PermissionsOverview.jsx';

// Super Admin Pages
import SuperAdminLayout from './pages/SuperAdmin/SuperAdminLayout.jsx';
import SuperAdminDashboard from './pages/SuperAdmin/SuperAdminDashboard.jsx';
import SuperAdminUsers from './pages/SuperAdmin/SuperAdminUsers.jsx';
import SuperAdminCourses from './pages/SuperAdmin/SuperAdminCourses.jsx';
import SuperAdminAddCourse from './pages/SuperAdmin/SuperAdminAddCourse.jsx';
import SuperAdminEditCourse from './pages/SuperAdmin/SuperAdminEditCourse.jsx';
import SuperAdminCourseDetail from './pages/SuperAdmin/SuperAdminCourseDetail.jsx';
import SuperAdminAnalytics from './pages/SuperAdmin/SuperAdminAnalytics.jsx';
import SuperAdminSystemSettings from './pages/SuperAdmin/SuperAdminSystemSettings.jsx';


function App() {
  const token =localStorage.getItem('token');
  return (
    <ThemeProvider>
      <AuthProvider token={token}>
        <Router>
          <div className="min-vh-100">
            <AppNavbar />
            
            <Routes>
              {/* Public Routes */}
              <Route path="/" element={<Home />} />
              <Route path="/login" element={<Login />} />
              <Route path="/register" element={ <Register />} />
              <Route path="/profile" element={token ? <Profile /> : <Login />} />

                 {/* Public User Profile */}
              <Route path="/profile/:id" element={token ? <PublicProfile /> : <Login />} />
              
              {/* Public Course Routes - No login required for preview */}
              <Route path="/courses" element={<Courses />} />
              <Route path="/courses/:id" element={<CourseDetail />} />
              <Route path="/courses/:id/learn" element={
                <ProtectedRoute>
                  <StudentCourse />
                </ProtectedRoute>
              } />
              <Route path="/honor-board" element={<HonorBoard />} />

           
              

              

              {/* Admin Routes - للمديرين العاديين فقط */}
              <Route path="/admin" element={
                <ProtectedRoute adminOnly>
                  <AdminLayout>
                    <Dashboard />
                  </AdminLayout>
                </ProtectedRoute>
              } />
              <Route path="/admin/courses" element={
                <ProtectedRoute adminOnly>
                  <AdminLayout>
                    <ManageCourses />
                  </AdminLayout>
                </ProtectedRoute>
              } />
              <Route path="/admin/users" element={
                <ProtectedRoute adminOnly>
                  <AdminLayout>
                    <ManageUsers />
                  </AdminLayout>
                </ProtectedRoute>
              } />
              <Route path="/admin/add-course" element={
                <ProtectedRoute adminOnly>
                  <AdminLayout>
                    <AddCourse />
                  </AdminLayout>
                </ProtectedRoute>
              } />
              <Route path="/admin/edit-course/:id" element={
                <ProtectedRoute adminOnly>
                  <AdminLayout>
                    <EditCourse />
                  </AdminLayout>
                </ProtectedRoute>
              } />
              <Route path="/admin/permissions" element={
                <ProtectedRoute adminOnly>
                  <AdminLayout>
                    <PermissionsOverview />
                  </AdminLayout>
                </ProtectedRoute>
              } />

              {/* Super Admin Routes - للمدير العام فقط */}
              <Route path="/super-admin" element={
                <ProtectedRoute superAdminOnly>
                  <SuperAdminLayout>
                    <SuperAdminDashboard />
                  </SuperAdminLayout>
                </ProtectedRoute>
              } />
              <Route path="/super-admin/users" element={
                <ProtectedRoute superAdminOnly>
                  <SuperAdminLayout>
                    <SuperAdminUsers />
                  </SuperAdminLayout>
                </ProtectedRoute>
              } />
              <Route path="/super-admin/courses" element={
                <ProtectedRoute superAdminOnly>
                  <SuperAdminLayout>
                    <SuperAdminCourses />
                  </SuperAdminLayout>
                </ProtectedRoute>
              } />
              <Route path="/super-admin/add-course" element={
                <ProtectedRoute superAdminOnly>
                  <SuperAdminLayout>
                    <SuperAdminAddCourse />
                  </SuperAdminLayout>
                </ProtectedRoute>
              } />
              <Route path="/super-admin/course/:id" element={
                <ProtectedRoute superAdminOnly>
                  <SuperAdminLayout>
                    <SuperAdminCourseDetail />
                  </SuperAdminLayout>
                </ProtectedRoute>
              } />
              <Route path="/super-admin/edit-course/:id" element={
                <ProtectedRoute superAdminOnly>
                  <SuperAdminLayout>
                    <SuperAdminEditCourse />
                  </SuperAdminLayout>
                </ProtectedRoute>
              } />
              <Route path="/super-admin/analytics" element={
                <ProtectedRoute superAdminOnly>
                  <SuperAdminLayout>
                    <SuperAdminAnalytics />
                  </SuperAdminLayout>
                </ProtectedRoute>
              } />
              <Route path="/super-admin/system" element={
                <ProtectedRoute superAdminOnly>
                  <SuperAdminLayout>
                    <SuperAdminSystemSettings />
                  </SuperAdminLayout>
                </ProtectedRoute>
              } />


                 {/* super-Admin Routes */}
              {/* <Route path="/admin" element={
                <ProtectedRoute  isSuperAdminOnly>
                  <AdminLayout>
                    <Dashboard />
                  </AdminLayout>
                </ProtectedRoute>
              } />
              <Route path="/admin/courses" element={
                <ProtectedRoute  isSuperAdminOnly>
                  <AdminLayout>
                    <ManageCourses />
                  </AdminLayout>
                </ProtectedRoute>
              } />
              <Route path="/admin/users" element={
                <ProtectedRoute  isSuperAdminOnly>
                  <AdminLayout>
                    <ManageUsers />
                  </AdminLayout>
                </ProtectedRoute>
              } />
              <Route path="/admin/add-course" element={
                <ProtectedRoute  isSuperAdminOnly>
                  <AdminLayout>
                    <AddCourse />
                  </AdminLayout>
                </ProtectedRoute>
              } />
              <Route path="/admin/edit-course/:id" element={
                <ProtectedRoute  superAdminOnly>
                  <AdminLayout>
                    <EditCourse />
                  </AdminLayout>
                </ProtectedRoute>
              } />
              <Route path="/admin/permissions" element={
                <ProtectedRoute  isSuperAdminOnly>
                  <AdminLayout>
                    <PermissionsOverview />
                  </AdminLayout>
                </ProtectedRoute>
              } /> */}

              {/* Catch-all for 404 */}
              {/* <Route path="*" element={<h1 className="text-center mt-5">404 - Page Not Found</h1>} /> */}
            </Routes>

            {/* Chatbot Widget - Only for authenticated users */}
            <Chatbot />
          </div>
        </Router>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;