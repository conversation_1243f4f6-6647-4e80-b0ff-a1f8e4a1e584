{"name": "educational-platform-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "start": "vite && cd server && npm run dev"}, "dependencies": {"axios": "^1.6.2", "bootstrap": "^5.3.2", "dotenv": "^17.2.1", "formik": "^2.4.6", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-bootstrap": "^2.9.1", "react-dom": "^18.3.1", "react-hook-form": "^7.60.0", "react-router-dom": "^6.20.1", "react-toastify": "^11.0.5", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^7.0.6"}}