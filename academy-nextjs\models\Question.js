import mongoose from 'mongoose';

const questionSchema = new mongoose.Schema({
  quiz: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Quiz',
    required: true
  },
  type: {
    type: String,
    enum: ['multiple_choice', 'true_false', 'short_answer', 'essay', 'fill_blank', 'matching'],
    required: true
  },
  question: {
    type: String,
    required: [true, 'نص السؤال مطلوب']
  },
  // خيارات السؤال (للأسئلة متعددة الخيارات)
  options: [{
    text: String,
    isCorrect: Boolean,
    explanation: String // شرح الإجابة
  }],
  // الإجابة الصحيحة (للأسئلة النصية)
  correctAnswer: String,
  
  // معايير التقييم للأسئلة المقالية
  rubric: [{
    criteria: String,
    points: Number,
    description: String
  }],
  
  // النقاط
  points: {
    type: Number,
    default: 1
  },
  
  // الترتيب
  order: {
    type: Number,
    default: 0
  },
  
  // الصعوبة
  difficulty: {
    type: String,
    enum: ['easy', 'medium', 'hard'],
    default: 'medium'
  },
  
  // الوسائط المرفقة
  media: {
    type: {
      type: String,
      enum: ['image', 'video', 'audio']
    },
    url: String,
    caption: String
  },
  
  // التلميحات
  hints: [String],
  
  // الشرح التفصيلي
  explanation: String,
  
  // البيانات الوصفية
  tags: [String],
  category: String,
  
  // إحصائيات
  stats: {
    totalAnswers: { type: Number, default: 0 },
    correctAnswers: { type: Number, default: 0 },
    averageTime: { type: Number, default: 0 }
  },
  
  isActive: {
    type: Boolean,
    default: true
  },
  
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true
});

// فهارس
questionSchema.index({ quiz: 1, order: 1 });
questionSchema.index({ type: 1, difficulty: 1 });
questionSchema.index({ tags: 1 });

// دالة للتحقق من صحة الإجابة
questionSchema.methods.checkAnswer = function(userAnswer) {
  switch (this.type) {
    case 'multiple_choice':
      const correctOption = this.options.find(opt => opt.isCorrect);
      return correctOption && correctOption._id.toString() === userAnswer;
      
    case 'true_false':
      return this.correctAnswer === userAnswer;
      
    case 'short_answer':
    case 'fill_blank':
      // مقارنة نصية بسيطة (يمكن تحسينها)
      return this.correctAnswer.toLowerCase().trim() === userAnswer.toLowerCase().trim();
      
    case 'essay':
      // الأسئلة المقالية تحتاج تقييم يدوي
      return null;
      
    default:
      return false;
  }
};

// دالة لحساب النقاط
questionSchema.methods.calculateScore = function(userAnswer) {
  if (this.type === 'essay') {
    // الأسئلة المقالية تحتاج تقييم يدوي
    return 0;
  }
  
  const isCorrect = this.checkAnswer(userAnswer);
  return isCorrect ? this.points : 0;
};

export default mongoose.models.Question || mongoose.model('Question', questionSchema);
