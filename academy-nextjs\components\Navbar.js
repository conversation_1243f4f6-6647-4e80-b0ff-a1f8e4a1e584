'use client';

import { useState } from 'react';
import { Navbar, Nav, Container, But<PERSON>, Dropdown, Badge } from 'react-bootstrap';
import Link from 'next/link';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';
import { 
  GraduationCap, 
  User, 
  LogOut, 
  Settings, 
  Sun, 
  Moon,
  BookOpen,
  Users,
  Shield,
  BarChart3
} from 'lucide-react';

export default function AppNavbar() {
  const { user, isAuthenticated, logout, isAdmin, isSuperAdmin } = useAuth();
  const { isDark, toggleTheme, mounted } = useTheme();
  const [expanded, setExpanded] = useState(false);

  const handleLogout = () => {
    logout();
    setExpanded(false);
  };

  // تجنب hydration mismatch
  if (!mounted) {
    return (
      <Navbar expand="lg" className="shadow-sm">
        <Container>
          <Navbar.Brand>
            <GraduationCap size={24} className="me-2" />
            أكاديمية التعلم
          </Navbar.Brand>
        </Container>
      </Navbar>
    );
  }

  return (
    <Navbar 
      expand="lg" 
      className={`shadow-sm ${isDark ? 'navbar-dark bg-dark' : 'navbar-light bg-white'}`}
      expanded={expanded}
      onToggle={setExpanded}
    >
      <Container>
        <Link href="/" className="navbar-brand text-decoration-none">
          <GraduationCap size={24} className="me-2" />
          أكاديمية التعلم
        </Link>

        <Navbar.Toggle aria-controls="basic-navbar-nav" />
        
        <Navbar.Collapse id="basic-navbar-nav">
          <Nav className="me-auto">
            <Link href="/" className="nav-link">الرئيسية</Link>
            <Link href="/courses" className="nav-link">الدورات</Link>
            <Link href="/leaderboard" className="nav-link">لوحة الشرف</Link>

            {isAuthenticated && (
              <>
                <Link href="/my-courses" className="nav-link">دوراتي</Link>
                <Link href="/profile" className="nav-link">الملف الشخصي</Link>
              </>
            )}

            {/* روابط المدير */}
            {isAdmin && (
              <Dropdown as={Nav.Item}>
                <Dropdown.Toggle as={Nav.Link} className="d-flex align-items-center">
                  <Shield size={16} className="me-1" />
                  لوحة الإدارة
                </Dropdown.Toggle>
                <Dropdown.Menu>
                  <Link href="/admin" className="dropdown-item">
                    <BarChart3 size={16} className="me-2" />
                    الإحصائيات
                  </Link>
                  <Link href="/admin/courses" className="dropdown-item">
                    <BookOpen size={16} className="me-2" />
                    إدارة الدورات
                  </Link>
                  <Link href="/admin/users" className="dropdown-item">
                    <Users size={16} className="me-2" />
                    إدارة المستخدمين
                  </Link>
                </Dropdown.Menu>
              </Dropdown>
            )}

            {/* روابط المدير العام */}
            {isSuperAdmin && (
              <Dropdown as={Nav.Item}>
                <Dropdown.Toggle as={Nav.Link} className="d-flex align-items-center">
                  <Shield size={16} className="me-1" />
                  <Badge bg="warning" className="ms-1">Super</Badge>
                  الإدارة العليا
                </Dropdown.Toggle>
                <Dropdown.Menu>
                  <Link href="/super-admin" className="dropdown-item">
                    <BarChart3 size={16} className="me-2" />
                    لوحة التحكم
                  </Link>
                  <Link href="/super-admin/users" className="dropdown-item">
                    <Users size={16} className="me-2" />
                    إدارة المستخدمين
                  </Link>
                  <Link href="/super-admin/courses" className="dropdown-item">
                    <BookOpen size={16} className="me-2" />
                    إدارة الدورات
                  </Link>
                  <Link href="/super-admin/settings" className="dropdown-item">
                    <Settings size={16} className="me-2" />
                    إعدادات النظام
                  </Link>
                </Dropdown.Menu>
              </Dropdown>
            )}
          </Nav>

          <Nav className="d-flex align-items-center gap-2">
            {/* زر تغيير الثيم */}
            <Button
              variant="outline-secondary"
              size="sm"
              onClick={toggleTheme}
              className="d-flex align-items-center"
            >
              {isDark ? <Sun size={16} /> : <Moon size={16} />}
            </Button>

            {isAuthenticated && user ? (
              <Dropdown align="end">
                <Dropdown.Toggle 
                  variant="outline-primary" 
                  size="sm"
                  className="d-flex align-items-center"
                >
                  <User size={16} className="me-1" />
                  {user.name}
                </Dropdown.Toggle>
                <Dropdown.Menu>
                  <Link href="/profile" className="dropdown-item">
                    <User size={16} className="me-2" />
                    الملف الشخصي
                  </Link>
                  <Link href="/settings" className="dropdown-item">
                    <Settings size={16} className="me-2" />
                    الإعدادات
                  </Link>
                  <Dropdown.Divider />
                  <Dropdown.Item onClick={handleLogout}>
                    <LogOut size={16} className="me-2" />
                    تسجيل الخروج
                  </Dropdown.Item>
                </Dropdown.Menu>
              </Dropdown>
            ) : (
              <div className="d-flex gap-2">
                <Link href="/login">
                  <Button variant="outline-primary" size="sm">
                    تسجيل الدخول
                  </Button>
                </Link>
                <Link href="/register">
                  <Button variant="primary" size="sm">
                    إنشاء حساب
                  </Button>
                </Link>
              </div>
            )}
          </Nav>
        </Navbar.Collapse>
      </Container>
    </Navbar>
  );
}
