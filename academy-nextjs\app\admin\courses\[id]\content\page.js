'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useAuth } from '../../../../../contexts/AuthContext';
import ProtectedRoute from '../../../../../components/ProtectedRoute';
import api from '../../../../../lib/api';
import { toast } from 'react-toastify';
import { 
  BookOpen, 
  Plus, 
  Edit3, 
  Trash2, 
  Video, 
  FileText, 
  HelpCircle,
  Eye,
  EyeOff,
  Clock,
  Users,
  ChevronDown,
  ChevronUp,
  Save,
  X
} from 'lucide-react';

export default function CourseContentManagement() {
  const params = useParams();
  const router = useRouter();
  const { user } = useAuth();
  
  const [course, setCourse] = useState(null);
  const [units, setUnits] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddUnit, setShowAddUnit] = useState(false);
  const [showAddLesson, setShowAddLesson] = useState(null);
  const [editingLesson, setEditingLesson] = useState(null);
  const [expandedUnits, setExpandedUnits] = useState(new Set());

  // نماذج البيانات
  const [newUnit, setNewUnit] = useState({
    title: '',
    description: ''
  });

  const [newLesson, setNewLesson] = useState({
    title: '',
    description: '',
    type: 'text',
    content: {},
    duration: 0,
    isPreview: false
  });

  useEffect(() => {
    fetchCourseContent();
  }, [params.id]);

  const fetchCourseContent = async () => {
    try {
      setLoading(true);
      
      // جلب بيانات الدورة
      const courseResponse = await api.get(`/courses/${params.id}`);
      setCourse(courseResponse.data.course);
      
      // جلب الوحدات والدروس
      const unitsResponse = await api.get(`/courses/${params.id}/units`);
      setUnits(unitsResponse.data.units || []);
      
      // توسيع الوحدة الأولى افتراضياً
      if (unitsResponse.data.units?.length > 0) {
        setExpandedUnits(new Set([unitsResponse.data.units[0]._id]));
      }
      
    } catch (error) {
      console.error('Error fetching course content:', error);
      toast.error('خطأ في جلب محتوى الدورة');
    } finally {
      setLoading(false);
    }
  };

  const handleAddUnit = async (e) => {
    e.preventDefault();
    
    if (!newUnit.title.trim()) {
      toast.error('عنوان الوحدة مطلوب');
      return;
    }

    try {
      const response = await api.post(`/courses/${params.id}/units`, newUnit);
      
      setUnits(prev => [...prev, response.data.unit]);
      setNewUnit({ title: '', description: '' });
      setShowAddUnit(false);
      toast.success('تم إضافة الوحدة بنجاح');
      
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'خطأ في إضافة الوحدة';
      toast.error(errorMessage);
    }
  };

  const handleAddLesson = async (unitId, e) => {
    e.preventDefault();
    
    if (!newLesson.title.trim()) {
      toast.error('عنوان الدرس مطلوب');
      return;
    }

    try {
      const lessonData = {
        ...newLesson,
        courseId: params.id,
        unitId
      };
      
      const response = await api.post('/lessons', lessonData);
      
      // تحديث الوحدة بالدرس الجديد
      setUnits(prev => prev.map(unit => 
        unit._id === unitId 
          ? { ...unit, lessons: [...unit.lessons, response.data.lesson] }
          : unit
      ));
      
      setNewLesson({
        title: '',
        description: '',
        type: 'text',
        content: {},
        duration: 0,
        isPreview: false
      });
      setShowAddLesson(null);
      toast.success('تم إضافة الدرس بنجاح');
      
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'خطأ في إضافة الدرس';
      toast.error(errorMessage);
    }
  };

  const handleUpdateLesson = async (lessonId, updatedData) => {
    try {
      const response = await api.put(`/lessons/${lessonId}`, updatedData);
      
      // تحديث الدرس في الحالة
      setUnits(prev => prev.map(unit => ({
        ...unit,
        lessons: unit.lessons.map(lesson => 
          lesson._id === lessonId ? response.data.lesson : lesson
        )
      })));
      
      setEditingLesson(null);
      toast.success('تم تحديث الدرس بنجاح');
      
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'خطأ في تحديث الدرس';
      toast.error(errorMessage);
    }
  };

  const handleDeleteLesson = async (lessonId) => {
    if (!confirm('هل أنت متأكد من حذف هذا الدرس؟')) return;

    try {
      await api.delete(`/lessons/${lessonId}`);
      
      // إزالة الدرس من الحالة
      setUnits(prev => prev.map(unit => ({
        ...unit,
        lessons: unit.lessons.filter(lesson => lesson._id !== lessonId)
      })));
      
      toast.success('تم حذف الدرس بنجاح');
      
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'خطأ في حذف الدرس';
      toast.error(errorMessage);
    }
  };

  const toggleUnitExpansion = (unitId) => {
    setExpandedUnits(prev => {
      const newSet = new Set(prev);
      if (newSet.has(unitId)) {
        newSet.delete(unitId);
      } else {
        newSet.add(unitId);
      }
      return newSet;
    });
  };

  const getLessonTypeIcon = (type) => {
    switch (type) {
      case 'video': return <Video className="w-4 h-4" />;
      case 'quiz': return <HelpCircle className="w-4 h-4" />;
      default: return <FileText className="w-4 h-4" />;
    }
  };

  const getLessonTypeLabel = (type) => {
    switch (type) {
      case 'video': return 'فيديو';
      case 'text': return 'نص';
      case 'quiz': return 'اختبار';
      case 'assignment': return 'مهمة';
      case 'file': return 'ملف';
      default: return 'نص';
    }
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">جاري التحميل...</span>
        </div>
      </div>
    );
  }

  return (
    <ProtectedRoute allowedRoles={['admin', 'super-admin']}>
      <div className="container-fluid py-4">
        {/* Header */}
        <div className="row mb-4">
          <div className="col-12">
            <div className="d-flex justify-content-between align-items-center">
              <div>
                <h2 className="mb-1">إدارة محتوى الدورة</h2>
                <p className="text-muted mb-0">
                  {course?.title}
                </p>
              </div>
              <div>
                <button
                  className="btn btn-outline-secondary me-2"
                  onClick={() => router.back()}
                >
                  <X className="w-4 h-4 me-1" />
                  إغلاق
                </button>
                <button
                  className="btn btn-primary"
                  onClick={() => setShowAddUnit(true)}
                >
                  <Plus className="w-4 h-4 me-1" />
                  إضافة وحدة
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Add Unit Modal */}
        {showAddUnit && (
          <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
            <div className="modal-dialog">
              <div className="modal-content">
                <div className="modal-header">
                  <h5 className="modal-title">إضافة وحدة جديدة</h5>
                  <button
                    type="button"
                    className="btn-close"
                    onClick={() => setShowAddUnit(false)}
                  ></button>
                </div>
                <form onSubmit={handleAddUnit}>
                  <div className="modal-body">
                    <div className="mb-3">
                      <label className="form-label">عنوان الوحدة *</label>
                      <input
                        type="text"
                        className="form-control"
                        value={newUnit.title}
                        onChange={(e) => setNewUnit(prev => ({ ...prev, title: e.target.value }))}
                        required
                      />
                    </div>
                    <div className="mb-3">
                      <label className="form-label">وصف الوحدة</label>
                      <textarea
                        className="form-control"
                        rows="3"
                        value={newUnit.description}
                        onChange={(e) => setNewUnit(prev => ({ ...prev, description: e.target.value }))}
                      ></textarea>
                    </div>
                  </div>
                  <div className="modal-footer">
                    <button
                      type="button"
                      className="btn btn-secondary"
                      onClick={() => setShowAddUnit(false)}
                    >
                      إلغاء
                    </button>
                    <button type="submit" className="btn btn-primary">
                      إضافة الوحدة
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        )}

        {/* Course Stats */}
        <div className="row mb-4">
          <div className="col-md-3">
            <div className="card bg-primary text-white">
              <div className="card-body">
                <div className="d-flex align-items-center">
                  <BookOpen className="w-8 h-8 me-3" />
                  <div>
                    <h5 className="mb-0">{units.length}</h5>
                    <small>الوحدات</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="col-md-3">
            <div className="card bg-success text-white">
              <div className="card-body">
                <div className="d-flex align-items-center">
                  <FileText className="w-8 h-8 me-3" />
                  <div>
                    <h5 className="mb-0">
                      {units.reduce((total, unit) => total + unit.lessons.length, 0)}
                    </h5>
                    <small>الدروس</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="col-md-3">
            <div className="card bg-info text-white">
              <div className="card-body">
                <div className="d-flex align-items-center">
                  <Clock className="w-8 h-8 me-3" />
                  <div>
                    <h5 className="mb-0">
                      {units.reduce((total, unit) => 
                        total + unit.lessons.reduce((sum, lesson) => sum + (lesson.duration || 0), 0), 0
                      )} د
                    </h5>
                    <small>إجمالي المدة</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="col-md-3">
            <div className="card bg-warning text-white">
              <div className="card-body">
                <div className="d-flex align-items-center">
                  <Users className="w-8 h-8 me-3" />
                  <div>
                    <h5 className="mb-0">{course?.enrollmentCount || 0}</h5>
                    <small>المسجلين</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Units and Lessons */}
        <div className="row">
          <div className="col-12">
            {units.length === 0 ? (
              <div className="card">
                <div className="card-body text-center py-5">
                  <BookOpen className="w-16 h-16 text-muted mx-auto mb-3" />
                  <h5 className="text-muted">لا توجد وحدات في هذه الدورة</h5>
                  <p className="text-muted">ابدأ بإضافة وحدة جديدة لتنظيم محتوى الدورة</p>
                  <button
                    className="btn btn-primary"
                    onClick={() => setShowAddUnit(true)}
                  >
                    <Plus className="w-4 h-4 me-1" />
                    إضافة أول وحدة
                  </button>
                </div>
              </div>
            ) : (
              <div className="accordion" id="unitsAccordion">
                {units.map((unit, unitIndex) => (
                  <div key={unit._id} className="card mb-3">
                    <div className="card-header">
                      <div className="d-flex justify-content-between align-items-center">
                        <button
                          className="btn btn-link text-decoration-none p-0 text-start flex-grow-1"
                          onClick={() => toggleUnitExpansion(unit._id)}
                        >
                          <div className="d-flex align-items-center">
                            {expandedUnits.has(unit._id) ?
                              <ChevronUp className="w-4 h-4 me-2" /> :
                              <ChevronDown className="w-4 h-4 me-2" />
                            }
                            <div>
                              <h6 className="mb-0">الوحدة {unitIndex + 1}: {unit.title}</h6>
                              <small className="text-muted">
                                {unit.lessons.length} درس •
                                {unit.lessons.reduce((sum, lesson) => sum + (lesson.duration || 0), 0)} دقيقة
                              </small>
                            </div>
                          </div>
                        </button>
                        <button
                          className="btn btn-outline-primary btn-sm"
                          onClick={() => setShowAddLesson(unit._id)}
                        >
                          <Plus className="w-4 h-4 me-1" />
                          إضافة درس
                        </button>
                      </div>
                    </div>

                    {expandedUnits.has(unit._id) && (
                      <div className="card-body">
                        {unit.description && (
                          <p className="text-muted mb-3">{unit.description}</p>
                        )}

                        {/* Add Lesson Form */}
                        {showAddLesson === unit._id && (
                          <div className="border rounded p-3 mb-3 bg-light">
                            <h6 className="mb-3">إضافة درس جديد</h6>
                            <form onSubmit={(e) => handleAddLesson(unit._id, e)}>
                              <div className="row">
                                <div className="col-md-6 mb-3">
                                  <label className="form-label">عنوان الدرس *</label>
                                  <input
                                    type="text"
                                    className="form-control"
                                    value={newLesson.title}
                                    onChange={(e) => setNewLesson(prev => ({ ...prev, title: e.target.value }))}
                                    required
                                  />
                                </div>
                                <div className="col-md-3 mb-3">
                                  <label className="form-label">نوع الدرس</label>
                                  <select
                                    className="form-select"
                                    value={newLesson.type}
                                    onChange={(e) => setNewLesson(prev => ({ ...prev, type: e.target.value }))}
                                  >
                                    <option value="text">نص</option>
                                    <option value="video">فيديو</option>
                                    <option value="quiz">اختبار</option>
                                    <option value="file">ملف</option>
                                  </select>
                                </div>
                                <div className="col-md-3 mb-3">
                                  <label className="form-label">المدة (دقيقة)</label>
                                  <input
                                    type="number"
                                    className="form-control"
                                    value={newLesson.duration}
                                    onChange={(e) => setNewLesson(prev => ({ ...prev, duration: parseInt(e.target.value) || 0 }))}
                                    min="0"
                                  />
                                </div>
                              </div>
                              <div className="mb-3">
                                <label className="form-label">وصف الدرس</label>
                                <textarea
                                  className="form-control"
                                  rows="2"
                                  value={newLesson.description}
                                  onChange={(e) => setNewLesson(prev => ({ ...prev, description: e.target.value }))}
                                ></textarea>
                              </div>
                              <div className="mb-3">
                                <div className="form-check">
                                  <input
                                    className="form-check-input"
                                    type="checkbox"
                                    checked={newLesson.isPreview}
                                    onChange={(e) => setNewLesson(prev => ({ ...prev, isPreview: e.target.checked }))}
                                  />
                                  <label className="form-check-label">
                                    درس معاينة (يمكن مشاهدته بدون تسجيل)
                                  </label>
                                </div>
                              </div>
                              <div className="d-flex gap-2">
                                <button type="submit" className="btn btn-primary btn-sm">
                                  <Save className="w-4 h-4 me-1" />
                                  حفظ الدرس
                                </button>
                                <button
                                  type="button"
                                  className="btn btn-secondary btn-sm"
                                  onClick={() => setShowAddLesson(null)}
                                >
                                  إلغاء
                                </button>
                              </div>
                            </form>
                          </div>
                        )}

                        {/* Lessons List */}
                        {unit.lessons.length === 0 ? (
                          <div className="text-center py-4">
                            <FileText className="w-12 h-12 text-muted mx-auto mb-2" />
                            <p className="text-muted mb-0">لا توجد دروس في هذه الوحدة</p>
                          </div>
                        ) : (
                          <div className="list-group">
                            {unit.lessons.map((lesson, lessonIndex) => (
                              <div key={lesson._id} className="list-group-item">
                                <div className="d-flex justify-content-between align-items-start">
                                  <div className="flex-grow-1">
                                    <div className="d-flex align-items-center mb-1">
                                      {getLessonTypeIcon(lesson.type)}
                                      <span className="badge bg-secondary me-2 ms-2">
                                        {getLessonTypeLabel(lesson.type)}
                                      </span>
                                      <h6 className="mb-0">{lesson.title}</h6>
                                      {lesson.isPreview && (
                                        <span className="badge bg-info ms-2">معاينة</span>
                                      )}
                                    </div>
                                    <p className="text-muted mb-1 small">{lesson.description}</p>
                                    <div className="d-flex align-items-center text-muted small">
                                      <Clock className="w-3 h-3 me-1" />
                                      {lesson.duration} دقيقة
                                      <span className="mx-2">•</span>
                                      <Eye className="w-3 h-3 me-1" />
                                      {lesson.stats?.views || 0} مشاهدة
                                    </div>
                                  </div>
                                  <div className="d-flex gap-1">
                                    <button
                                      className="btn btn-outline-primary btn-sm"
                                      onClick={() => setEditingLesson(lesson)}
                                    >
                                      <Edit3 className="w-3 h-3" />
                                    </button>
                                    <button
                                      className="btn btn-outline-danger btn-sm"
                                      onClick={() => handleDeleteLesson(lesson._id)}
                                    >
                                      <Trash2 className="w-3 h-3" />
                                    </button>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
