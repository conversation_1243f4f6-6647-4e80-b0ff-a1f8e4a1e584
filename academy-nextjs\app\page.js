'use client';

import { Contain<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, Badge } from 'react-bootstrap';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';
import { BookOpen, Users, Award, TrendingUp, ArrowRight, Play } from 'lucide-react';
import Link from 'next/link';

export default function Home() {
  const { user, isAuthenticated } = useAuth();
  const { isDark } = useTheme();

  const stats = [
    { icon: Users, title: 'الطلاب', value: '1000+', color: 'primary' },
    { icon: BookOpen, title: 'الدورات', value: '50+', color: 'success' },
    { icon: Award, title: 'الشهادات', value: '500+', color: 'warning' },
    { icon: TrendingUp, title: 'معدل النجاح', value: '95%', color: 'info' }
  ];

  const features = [
    {
      icon: BookOpen,
      title: 'دورات متنوعة',
      description: 'مجموعة واسعة من الدورات في مختلف المجالات التقنية'
    },
    {
      icon: Users,
      title: 'مدربين خبراء',
      description: 'تعلم من أفضل المدربين في المجال'
    },
    {
      icon: Award,
      title: 'شهادات معتمدة',
      description: 'احصل على شهادات معتمدة عند إكمال الدورات'
    }
  ];

  return (
    <div className={`min-vh-100 ${isDark ? 'bg-dark text-light' : 'bg-light'}`}>
      {/* Hero Section */}
      <section className="gradient-bg text-white py-5">
        <Container>
          <Row className="align-items-center min-vh-75">
            <Col lg={6}>
              <div className="fade-in">
                <h1 className="display-4 fw-bold mb-4">
                  مرحباً بك في أكاديمية التعلم
                </h1>
                <p className="lead mb-4">
                  منصة تعليمية متطورة تقدم أفضل الدورات التقنية باللغة العربية
                </p>
                <div className="d-flex gap-3 flex-wrap">
                  {isAuthenticated ? (
                    <Link href="/courses">
                      <Button size="lg" variant="light" className="d-flex align-items-center">
                        <BookOpen size={20} className="me-2" />
                        تصفح الدورات
                      </Button>
                    </Link>
                  ) : (
                    <>
                      <Link href="/register">
                        <Button size="lg" variant="light" className="d-flex align-items-center">
                          <ArrowRight size={20} className="me-2" />
                          ابدأ التعلم الآن
                        </Button>
                      </Link>
                      <Link href="/courses">
                        <Button size="lg" variant="outline-light" className="d-flex align-items-center">
                          <Play size={20} className="me-2" />
                          شاهد الدورات
                        </Button>
                      </Link>
                    </>
                  )}
                </div>
              </div>
            </Col>
            <Col lg={6}>
              <div className="text-center">
                <div className="position-relative">
                  <div className="bg-white bg-opacity-10 rounded-circle p-5 d-inline-block">
                    <BookOpen size={120} className="text-white" />
                  </div>
                </div>
              </div>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Stats Section */}
      <section className="py-5">
        <Container>
          <Row className="g-4">
            {stats.map((stat, index) => (
              <Col md={6} lg={3} key={index}>
                <Card className={`text-center border-0 shadow-sm h-100 ${isDark ? 'bg-dark text-light' : ''}`}>
                  <Card.Body>
                    <div className={`text-${stat.color} mb-3`}>
                      <stat.icon size={48} />
                    </div>
                    <h3 className="fw-bold">{stat.value}</h3>
                    <p className="text-muted mb-0">{stat.title}</p>
                  </Card.Body>
                </Card>
              </Col>
            ))}
          </Row>
        </Container>
      </section>

      {/* Features Section */}
      <section className={`py-5 ${isDark ? 'bg-secondary' : 'bg-white'}`}>
        <Container>
          <Row className="text-center mb-5">
            <Col>
              <h2 className="fw-bold mb-3">لماذا تختار أكاديميتنا؟</h2>
              <p className="lead text-muted">
                نقدم تجربة تعليمية متميزة مع أحدث التقنيات والأساليب
              </p>
            </Col>
          </Row>
          <Row className="g-4">
            {features.map((feature, index) => (
              <Col md={4} key={index}>
                <Card className={`text-center border-0 shadow-sm h-100 hover-shadow ${isDark ? 'bg-dark text-light' : ''}`}>
                  <Card.Body className="p-4">
                    <div className="text-primary mb-3">
                      <feature.icon size={48} />
                    </div>
                    <h5 className="fw-bold mb-3">{feature.title}</h5>
                    <p className="text-muted">{feature.description}</p>
                  </Card.Body>
                </Card>
              </Col>
            ))}
          </Row>
        </Container>
      </section>

      {/* CTA Section */}
      {!isAuthenticated && (
        <section className="py-5 gradient-bg text-white">
          <Container>
            <Row className="text-center">
              <Col>
                <h2 className="fw-bold mb-3">ابدأ رحلتك التعليمية اليوم</h2>
                <p className="lead mb-4">
                  انضم إلى آلاف الطلاب الذين يطورون مهاراتهم معنا
                </p>
                <Link href="/register">
                  <Button size="lg" variant="light" className="btn-gradient">
                    إنشاء حساب مجاني
                  </Button>
                </Link>
              </Col>
            </Row>
          </Container>
        </section>
      )}
    </div>
  );
}
