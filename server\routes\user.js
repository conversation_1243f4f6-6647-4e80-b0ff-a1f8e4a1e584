import express from 'express';
import User from '../models/User.js';
import Course from '../models/Course.js';
import { verifyToken } from '../middleware/verifyToken.js';
import { allowRoles } from '../middleware/adminOnly.js';

const router = express.Router();

// Get all users
router.get('/users', verifyToken, allowRoles('student', 'admin', 'super-admin'), async (req, res) => {
  try {
    const users = await User.find().select('-password');
    res.json(users);
  } catch (error) {
    res.status(500).json({ message: 'خطأ في الخادم', error: error.message });
  }
});

// Get user details
router.get('/users/:id', verifyToken, allowRoles('super-admin'), async (req, res) => {
  try {
    const user = await User.findById(req.params.id).select('-password');
    res.json(user);
  } catch (error) {
    res.status(500).json({ message: 'خطاء في الخادم', error: error.message });
  }
});

// Get user courses with details
router.get('/users/:id/courses', verifyToken, allowRoles('student'), async (req, res) => {
  try {
    const user = await User.findById(req.params.id).populate('courses');
    res.json(user.courses);
  } catch (error) {
    res.status(500).json({ message: 'خطاء في الخادم', error: error.message });
  }
});

// Update user profile
router.put('/profile/:id', verifyToken, allowRoles('student', 'admin', 'super-admin'), async (req, res) => {
  try {
    const { name, email ,profilePicture } = req.body;
    const user = await User.findByIdAndUpdate(
      req.params.id,
      { name, email ,profilePicture },
      { new: true }
    ).select('-password');

    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }

    res.json(user);
  } catch (error) {
    res.status(500).json({ message: 'خطأ في الخادم', error: error.message });
  }
});

// Get user profile
router.get('/profile', verifyToken, allowRoles('student', 'admin', 'super-admin'), async (req, res) => {
  try {
    const user = await User.findById(req.user.id).select('-password');
    res.json(user);
  } catch (error) {
    res.status(500).json({ message: 'خطأ في الخادم', error: error.message });
  }
});

// Get user courses
router.get('/courses', verifyToken, allowRoles('student'), async (req, res) => {
  try {
    const user = await User.findById(req.user.id).populate('courses');
    res.json(user.courses);
  } catch (error) {
    res.status(500).json({ message: 'خطاء في الخادم', error: error.message });
  }
});


export default router;