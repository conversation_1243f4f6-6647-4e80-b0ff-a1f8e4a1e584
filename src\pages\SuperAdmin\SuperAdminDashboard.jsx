import React, { useState, useEffect } from 'react';
import { <PERSON>, Col, Card, Badge, Table, <PERSON><PERSON>, Spin<PERSON>, Button } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import { useTheme } from '../../context/ThemeContext';
import { superAdminAPI } from '../../services/api';
import {
  Users,
  BookOpen,
  UserCheck,
  TrendingUp,
  Shield,
  Activity,
  AlertTriangle,
  CheckCircle,
  Plus,
  Settings
} from 'lucide-react';

const SuperAdminDashboard = () => {
  const { isDark } = useTheme();
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchStats();
  }, []);

  const fetchStats = async () => {
    try {
      setLoading(true);
      const response = await superAdminAPI.getStatistics();
      setStats(response.data);
    } catch (err) {
      setError('فشل في تحميل الإحصائيات');
      console.error('Error fetching stats:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="text-center py-5">
        <Spinner animation="border" variant="primary" />
        <p className="mt-3">جاري تحميل الإحصائيات...</p>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="danger">
        <AlertTriangle size={20} className="me-2" />
        {error}
      </Alert>
    );
  }

  const statCards = [
    {
      title: 'إجمالي المستخدمين',
      value: stats?.users?.total || 0,
      icon: Users,
      color: 'primary',
      change: `+${stats?.users?.recentRegistrations || 0} هذا الأسبوع`
    },
    {
      title: 'الطلاب النشطين',
      value: stats?.users?.students || 0,
      icon: UserCheck,
      color: 'success',
      change: `${stats?.users?.activeToday || 0} نشط اليوم`
    },
    {
      title: 'إجمالي الدورات',
      value: stats?.courses?.total || 0,
      icon: BookOpen,
      color: 'info',
      change: `${stats?.courses?.active || 0} نشطة`
    },
    {
      title: 'المديرين',
      value: (stats?.users?.admins || 0) + (stats?.users?.superAdmins || 0),
      icon: Shield,
      color: 'warning',
      change: `${stats?.users?.superAdmins || 0} مدير عام`
    }
  ];

  return (
    <div>
      {/* Header with Action Buttons */}
      <Row className="mb-4">
        <Col>
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h2 className="mb-1">لوحة المدير العام</h2>
              <p className="text-muted mb-0">نظرة شاملة على النظام والإحصائيات</p>
            </div>
            <div className="d-flex gap-2">
              <Button
                as={Link}
                to="/super-admin/add-course"
                variant="primary"
                className="d-flex align-items-center"
              >
                <Plus size={16} className="me-1" />
                إضافة دورة
              </Button>
              <Button
                as={Link}
                to="/super-admin/courses"
                variant="outline-primary"
                className="d-flex align-items-center"
              >
                <BookOpen size={16} className="me-1" />
                إدارة الدورات
              </Button>
              <Button
                as={Link}
                to="/super-admin/system"
                variant="outline-secondary"
                className="d-flex align-items-center"
              >
                <Settings size={16} className="me-1" />
                إعدادات النظام
              </Button>
            </div>
          </div>
        </Col>
      </Row>

      {/* Statistics Cards */}
      <Row className="mb-4">
        {statCards.map((stat, index) => {
          const IconComponent = stat.icon;
          return (
            <Col md={6} lg={3} key={index} className="mb-3">
              <Card className={`h-100 ${isDark ? 'bg-dark text-light' : 'bg-white'} border-0 shadow-sm`}>
                <Card.Body>
                  <div className="d-flex justify-content-between align-items-start">
                    <div>
                      <h6 className="text-muted mb-2">{stat.title}</h6>
                      <h3 className="mb-1">{stat.value.toLocaleString()}</h3>
                      <small className={`text-${stat.color}`}>
                        <TrendingUp size={14} className="me-1" />
                        {stat.change}
                      </small>
                    </div>
                    <div className={`p-3 rounded-circle bg-${stat.color} bg-opacity-10`}>
                      <IconComponent size={24} className={`text-${stat.color}`} />
                    </div>
                  </div>
                </Card.Body>
              </Card>
            </Col>
          );
        })}
      </Row>

      {/* System Status */}
      <Row className="mb-4">
        <Col md={6}>
          <Card className={`${isDark ? 'bg-dark text-light' : 'bg-white'} border-0 shadow-sm`}>
            <Card.Header className="bg-transparent border-0">
              <h6 className="mb-0 d-flex align-items-center">
                <Activity size={18} className="me-2" />
                حالة النظام
              </h6>
            </Card.Header>
            <Card.Body>
              <div className="d-flex align-items-center justify-content-between mb-3">
                <span>الخادم</span>
                <Badge bg="success" className="d-flex align-items-center">
                  <CheckCircle size={14} className="me-1" />
                  يعمل بشكل طبيعي
                </Badge>
              </div>
              <div className="d-flex align-items-center justify-content-between mb-3">
                <span>قاعدة البيانات</span>
                <Badge bg="success" className="d-flex align-items-center">
                  <CheckCircle size={14} className="me-1" />
                  متصلة
                </Badge>
              </div>
              <div className="d-flex align-items-center justify-content-between mb-3">
                <span>التخزين</span>
                <Badge bg="warning" className="d-flex align-items-center">
                  <AlertTriangle size={14} className="me-1" />
                  75% مستخدم
                </Badge>
              </div>
              <div className="d-flex align-items-center justify-content-between">
                <span>وقت التشغيل</span>
                <Badge bg="info">
                  {Math.floor((stats?.system?.uptime || 0) / 3600)} ساعة
                </Badge>
              </div>
            </Card.Body>
          </Card>
        </Col>

        <Col md={6}>
          <Card className={`${isDark ? 'bg-dark text-light' : 'bg-white'} border-0 shadow-sm`}>
            <Card.Header className="bg-transparent border-0">
              <h6 className="mb-0 d-flex align-items-center">
                <TrendingUp size={18} className="me-2" />
                النشاط الأخير
              </h6>
            </Card.Header>
            <Card.Body>
              <div className="d-flex align-items-center justify-content-between mb-3">
                <div>
                  <div className="fw-bold">تسجيلات جديدة</div>
                  <small className="text-muted">آخر 7 أيام</small>
                </div>
                <Badge bg="primary" pill>
                  {stats?.users?.recentRegistrations || 0}
                </Badge>
              </div>
              <div className="d-flex align-items-center justify-content-between mb-3">
                <div>
                  <div className="fw-bold">دورات جديدة</div>
                  <small className="text-muted">آخر 30 يوم</small>
                </div>
                <Badge bg="info" pill>
                  {stats?.courses?.recentlyAdded || 0}
                </Badge>
              </div>
              <div className="d-flex align-items-center justify-content-between">
                <div>
                  <div className="fw-bold">المستخدمين النشطين</div>
                  <small className="text-muted">اليوم</small>
                </div>
                <Badge bg="success" pill>
                  {stats?.users?.activeToday || 0}
                </Badge>
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      <Row className="mb-4">
        {/* Recent Activity Table */}
        <Col lg={8}>
          <Card className={`${isDark ? 'bg-dark text-light' : 'bg-white'} border-0 shadow-sm`}>
            <Card.Header className="bg-transparent border-0">
              <h6 className="mb-0">الأنشطة الأخيرة</h6>
            </Card.Header>
            <Card.Body>
              <Table responsive className={isDark ? 'table-dark' : ''}>
                <thead>
                  <tr>
                    <th>النشاط</th>
                    <th>المستخدم</th>
                    <th>الوقت</th>
                    <th>الحالة</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>تسجيل مستخدم جديد</td>
                    <td>أحمد محمد</td>
                    <td>منذ 5 دقائق</td>
                    <td><Badge bg="success">مكتمل</Badge></td>
                  </tr>
                  <tr>
                    <td>إضافة دورة جديدة</td>
                    <td>سارة أحمد</td>
                    <td>منذ 15 دقيقة</td>
                    <td><Badge bg="success">مكتمل</Badge></td>
                  </tr>
                  <tr>
                    <td>تحديث صلاحيات مستخدم</td>
                    <td>محمد علي</td>
                    <td>منذ 30 دقيقة</td>
                    <td><Badge bg="warning">قيد المراجعة</Badge></td>
                  </tr>
                </tbody>
              </Table>
            </Card.Body>
          </Card>
        </Col>

        {/* Quick Actions */}
        <Col lg={4}>
          <Card className={`${isDark ? 'bg-dark text-light' : 'bg-white'} border-0 shadow-sm`}>
            <Card.Header className="bg-transparent border-0">
              <h6 className="mb-0">الإجراءات السريعة</h6>
            </Card.Header>
            <Card.Body>
              <div className="d-grid gap-2">
                <Button
                  as={Link}
                  to="/super-admin/users"
                  variant="outline-primary"
                  className="text-start d-flex align-items-center"
                >
                  <Users size={16} className="me-2" />
                  إدارة المستخدمين
                </Button>
                <Button
                  as={Link}
                  to="/super-admin/courses"
                  variant="outline-success"
                  className="text-start d-flex align-items-center"
                >
                  <BookOpen size={16} className="me-2" />
                  إدارة الدورات
                </Button>
                <Button
                  as={Link}
                  to="/super-admin/add-course"
                  variant="outline-info"
                  className="text-start d-flex align-items-center"
                >
                  <Plus size={16} className="me-2" />
                  إضافة دورة جديدة
                </Button>
                <Button
                  as={Link}
                  to="/super-admin/analytics"
                  variant="outline-warning"
                  className="text-start d-flex align-items-center"
                >
                  <Activity size={16} className="me-2" />
                  التحليلات المتقدمة
                </Button>
                <Button
                  as={Link}
                  to="/super-admin/system"
                  variant="outline-secondary"
                  className="text-start d-flex align-items-center"
                >
                  <Settings size={16} className="me-2" />
                  إعدادات النظام
                </Button>
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default SuperAdminDashboard;
