import { NextResponse } from 'next/server';
import connectDB from '../../../../lib/mongodb';
import User from '../../../../models/User';
import { generateToken } from '../../../../lib/auth';

export async function POST(request) {
  try {
    await connectDB();
    
    const { email, password } = await request.json();

    // التحقق من البيانات المطلوبة
    if (!email || !password) {
      return NextResponse.json(
        { message: 'البريد الإلكتروني وكلمة المرور مطلوبان' },
        { status: 400 }
      );
    }

    // البحث عن المستخدم
    const user = await User.findOne({ email: email.toLowerCase() });
    if (!user) {
      return NextResponse.json(
        { message: 'بيانات الدخول غير صحيحة' },
        { status: 401 }
      );
    }

    // التحقق من كلمة المرور
    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid) {
      return NextResponse.json(
        { message: 'بيانات الدخول غير صحيحة' },
        { status: 401 }
      );
    }

    // التحقق من أن الحساب نشط
    if (!user.isActive) {
      return NextResponse.json(
        { message: 'الحساب غير نشط. يرجى التواصل مع الإدارة' },
        { status: 401 }
      );
    }

    // تحديث آخر نشاط وعداد تسجيل الدخول
    user.lastActive = new Date();
    user.loginCount += 1;
    await user.save();

    // إنشاء JWT token
    const token = generateToken({
      id: user._id,
      email: user.email,
      role: user.role
    });

    // إرجاع البيانات (بدون كلمة المرور)
    const userResponse = user.toJSON();

    return NextResponse.json({
      message: 'تم تسجيل الدخول بنجاح',
      user: userResponse,
      token
    }, { status: 200 });

  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json(
      { message: 'خطأ في الخادم' },
      { status: 500 }
    );
  }
}
