'use client';

import { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Badge, Button, Form, Modal } from 'react-bootstrap';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { 
  BarChart3, 
  Users, 
  BookOpen, 
  TrendingUp, 
  Plus,
  Edit,
  Trash2,
  Eye,
  UserCheck,
  UserX,
  Calendar,
  Award
} from 'lucide-react';
import { toast } from 'react-toastify';

export default function AdminDashboard() {
  const { user, isAdminOrSuperAdmin, api } = useAuth();
  const { isDark } = useTheme();
  const router = useRouter();
  
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalCourses: 0,
    activeCourses: 0,
    totalEnrollments: 0
  });
  const [recentUsers, setRecentUsers] = useState([]);
  const [recentCourses, setRecentCourses] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!isAdminOrSuperAdmin) {
      router.push('/');
      return;
    }
    
    fetchDashboardData();
  }, [isAdminOrSuperAdmin, router]);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);

      // جلب الإحصائيات من API
      const [dashboardResponse, usersResponse, coursesResponse] = await Promise.all([
        api.post('/statistics', { type: 'dashboard' }),
        api.get('/users?limit=5'),
        api.get('/courses?limit=5')
      ]);

      const dashboardData = dashboardResponse.data.data;
      setStats(dashboardData.overview);

      // استخدام البيانات الحقيقية
      setRecentUsers(usersResponse.data.users || []);

      // استخدام بيانات الدورات الحقيقية
      setRecentCourses(coursesResponse.data.courses || []);

    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      toast.error('فشل في تحميل بيانات لوحة التحكم');
    } finally {
      setLoading(false);
    }
  };

  const toggleUserStatus = async (userId, currentStatus) => {
    try {
      // محاكاة تغيير حالة المستخدم
      setRecentUsers(prev => 
        prev.map(user => 
          user._id === userId 
            ? { ...user, isActive: !currentStatus }
            : user
        )
      );
      toast.success('تم تحديث حالة المستخدم بنجاح');
    } catch (error) {
      toast.error('فشل في تحديث حالة المستخدم');
    }
  };

  const getRoleBadge = (role) => {
    switch (role) {
      case 'admin':
        return <Badge bg="warning">مدير</Badge>;
      case 'super-admin':
        return <Badge bg="danger">مدير عام</Badge>;
      default:
        return <Badge bg="primary">طالب</Badge>;
    }
  };

  const getLevelBadge = (level) => {
    switch (level) {
      case 'مبتدئ':
        return <Badge bg="success">مبتدئ</Badge>;
      case 'متوسط':
        return <Badge bg="warning">متوسط</Badge>;
      case 'متقدم':
        return <Badge bg="danger">متقدم</Badge>;
      default:
        return <Badge bg="secondary">{level}</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center min-vh-100">
        <div className="spinner-border text-primary" />
      </div>
    );
  }

  return (
    <div className={`min-vh-100 ${isDark ? 'bg-dark text-light' : 'bg-light'}`}>
      <Container className="py-5">
        {/* Header */}
        <Row className="mb-4">
          <Col>
            <div className="d-flex justify-content-between align-items-center">
              <div>
                <h1 className="display-6 fw-bold mb-2">لوحة تحكم المدير</h1>
                <p className="text-muted">مرحباً {user?.name}، إليك نظرة عامة على النظام</p>
              </div>
              <div className="d-flex gap-2">
                <Link href="/admin/add-course">
                  <Button variant="primary">
                    <Plus size={16} className="me-1" />
                    إضافة دورة
                  </Button>
                </Link>
              </div>
            </div>
          </Col>
        </Row>

        {/* Stats Cards */}
        <Row className="mb-5">
          <Col md={3}>
            <Card className={`text-center border-0 shadow-sm h-100 ${isDark ? 'bg-dark text-light' : ''}`}>
              <Card.Body>
                <Users size={32} className="text-primary mb-2" />
                <h3 className="fw-bold">{stats.totalUsers.toLocaleString()}</h3>
                <p className="text-muted mb-0">إجمالي المستخدمين</p>
                <small className="text-success">
                  <TrendingUp size={12} className="me-1" />
                  +12% هذا الشهر
                </small>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3}>
            <Card className={`text-center border-0 shadow-sm h-100 ${isDark ? 'bg-dark text-light' : ''}`}>
              <Card.Body>
                <BookOpen size={32} className="text-success mb-2" />
                <h3 className="fw-bold">{stats.totalCourses}</h3>
                <p className="text-muted mb-0">إجمالي الدورات</p>
                <small className="text-info">
                  {stats.activeCourses} دورة نشطة
                </small>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3}>
            <Card className={`text-center border-0 shadow-sm h-100 ${isDark ? 'bg-dark text-light' : ''}`}>
              <Card.Body>
                <Award size={32} className="text-warning mb-2" />
                <h3 className="fw-bold">{stats.totalEnrollments.toLocaleString()}</h3>
                <p className="text-muted mb-0">إجمالي التسجيلات</p>
                <small className="text-success">
                  <TrendingUp size={12} className="me-1" />
                  +8% هذا الأسبوع
                </small>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3}>
            <Card className={`text-center border-0 shadow-sm h-100 ${isDark ? 'bg-dark text-light' : ''}`}>
              <Card.Body>
                <BarChart3 size={32} className="text-info mb-2" />
                <h3 className="fw-bold">95%</h3>
                <p className="text-muted mb-0">معدل الرضا</p>
                <small className="text-success">
                  ممتاز
                </small>
              </Card.Body>
            </Card>
          </Col>
        </Row>

        <Row>
          {/* Recent Users */}
          <Col md={6}>
            <Card className={`border-0 shadow-sm h-100 ${isDark ? 'bg-dark text-light' : ''}`}>
              <Card.Header className="bg-transparent border-0 d-flex justify-content-between align-items-center">
                <h5 className="mb-0">المستخدمون الجدد</h5>
                <Link href="/admin/users">
                  <Button variant="outline-primary" size="sm">
                    عرض الكل
                  </Button>
                </Link>
              </Card.Header>
              <Card.Body>
                <div className="table-responsive">
                  <Table hover className={isDark ? 'table-dark' : ''}>
                    <thead>
                      <tr>
                        <th>المستخدم</th>
                        <th>الدور</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                      </tr>
                    </thead>
                    <tbody>
                      {recentUsers.map((user) => (
                        <tr key={user._id}>
                          <td>
                            <div>
                              <div className="fw-bold">{user.name}</div>
                              <small className="text-muted">{user.email}</small>
                            </div>
                          </td>
                          <td>{getRoleBadge(user.role)}</td>
                          <td>
                            {user.isActive ? (
                              <Badge bg="success">نشط</Badge>
                            ) : (
                              <Badge bg="secondary">غير نشط</Badge>
                            )}
                          </td>
                          <td>
                            <div className="d-flex gap-1">
                              <Button
                                variant="outline-primary"
                                size="sm"
                                onClick={() => toggleUserStatus(user._id, user.isActive)}
                              >
                                {user.isActive ? <UserX size={14} /> : <UserCheck size={14} />}
                              </Button>
                              <Button variant="outline-info" size="sm">
                                <Eye size={14} />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </Table>
                </div>
              </Card.Body>
            </Card>
          </Col>

          {/* Recent Courses */}
          <Col md={6}>
            <Card className={`border-0 shadow-sm h-100 ${isDark ? 'bg-dark text-light' : ''}`}>
              <Card.Header className="bg-transparent border-0 d-flex justify-content-between align-items-center">
                <h5 className="mb-0">الدورات الحديثة</h5>
                <Link href="/admin/courses">
                  <Button variant="outline-primary" size="sm">
                    عرض الكل
                  </Button>
                </Link>
              </Card.Header>
              <Card.Body>
                <div className="table-responsive">
                  <Table hover className={isDark ? 'table-dark' : ''}>
                    <thead>
                      <tr>
                        <th>الدورة</th>
                        <th>المستوى</th>
                        <th>التسجيلات</th>
                        <th>الإجراءات</th>
                      </tr>
                    </thead>
                    <tbody>
                      {recentCourses.map((course) => (
                        <tr key={course._id}>
                          <td>
                            <div>
                              <div className="fw-bold">{course.title}</div>
                              <small className="text-muted">{course.instructor}</small>
                            </div>
                          </td>
                          <td>{getLevelBadge(course.level)}</td>
                          <td>
                            <Badge bg="info">{course.enrollmentCount}</Badge>
                          </td>
                          <td>
                            <div className="d-flex gap-1">
                              <Button variant="outline-primary" size="sm">
                                <Edit size={14} />
                              </Button>
                              <Button variant="outline-info" size="sm">
                                <Eye size={14} />
                              </Button>
                              <Button variant="outline-danger" size="sm">
                                <Trash2 size={14} />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </Table>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>

        {/* Quick Actions */}
        <Row className="mt-4">
          <Col>
            <Card className={`border-0 shadow-sm ${isDark ? 'bg-dark text-light' : ''}`}>
              <Card.Header className="bg-transparent border-0">
                <h5 className="mb-0">الإجراءات السريعة</h5>
              </Card.Header>
              <Card.Body>
                <Row>
                  <Col md={3}>
                    <Link href="/admin/courses/add" className="text-decoration-none">
                      <Card className={`text-center border-primary h-100 ${isDark ? 'bg-secondary' : 'bg-light'}`}>
                        <Card.Body>
                          <Plus size={32} className="text-primary mb-2" />
                          <h6>إضافة دورة جديدة</h6>
                        </Card.Body>
                      </Card>
                    </Link>
                  </Col>
                  <Col md={3}>
                    <Link href="/admin/users" className="text-decoration-none">
                      <Card className={`text-center border-success h-100 ${isDark ? 'bg-secondary' : 'bg-light'}`}>
                        <Card.Body>
                          <Users size={32} className="text-success mb-2" />
                          <h6>إدارة المستخدمين</h6>
                        </Card.Body>
                      </Card>
                    </Link>
                  </Col>
                  <Col md={3}>
                    <Link href="/admin/reports" className="text-decoration-none">
                      <Card className={`text-center border-warning h-100 ${isDark ? 'bg-secondary' : 'bg-light'}`}>
                        <Card.Body>
                          <BarChart3 size={32} className="text-warning mb-2" />
                          <h6>التقارير والإحصائيات</h6>
                        </Card.Body>
                      </Card>
                    </Link>
                  </Col>
                  <Col md={3}>
                    <Link href="/admin/settings" className="text-decoration-none">
                      <Card className={`text-center border-info h-100 ${isDark ? 'bg-secondary' : 'bg-light'}`}>
                        <Card.Body>
                          <Calendar size={32} className="text-info mb-2" />
                          <h6>الإعدادات</h6>
                        </Card.Body>
                      </Card>
                    </Link>
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>
    </div>
  );
}
