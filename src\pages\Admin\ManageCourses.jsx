import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Modal,
  Container
} from 'react-bootstrap';
import { Eye, Edit, Trash2, EyeOff, Copy, Archive, BarChart3 } from 'lucide-react';
import { coursesAPI, adminAPI } from '../../services/api';
import { useAuth } from '../../context/AuthContext';

const ManageCourses = () => {
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [courseToDelete, setCourseToDelete] = useState(null);
  const [showToggleModal, setShowToggleModal] = useState(false);
  const [courseToToggle, setCourseToToggle] = useState(null);
  const [showDuplicateModal, setShowDuplicateModal] = useState(false);
  const [courseToDuplicate, setCourseToDuplicate] = useState(null);
  const [stats, setStats] = useState(null);
  const [showStats, setShowStats] = useState(false);

  const navigate = useNavigate();
  const { user, isSuperAdmin } = useAuth();

  useEffect(() => {
    fetchCourses();
  }, []);

  const fetchCourses = async () => {
    try {
      setLoading(true);
      const response = await adminAPI.getCourses();
      console.log('Admin courses response:', response); // للتشخيص

      // التحقق من بنية البيانات المستلمة
      if (response && response.data) {
        // إذا كانت البيانات مصفوفة مباشرة
        if (Array.isArray(response.data)) {
          setCourses(response.data);
        }
        // إذا كانت البيانات داخل خاصية courses
        else if (response.data.courses && Array.isArray(response.data.courses)) {
          setCourses(response.data.courses);
        }
        // إذا كانت البيانات في شكل آخر
        else {
          console.warn('Unexpected data structure:', response.data);
          setCourses([]);
        }
      } else {
        console.warn('No data received:', response);
        setCourses([]);
      }
    } catch (err) {
      setError('فشل في تحميل الدورات: ' + (err.response?.data?.message || err.message));
      console.error('Error fetching courses:', err);
      setCourses([]);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteClick = (course) => {
    setCourseToDelete(course);
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      if (!courseToDelete?._id) {
        setError('معرف الدورة غير صحيح');
        return;
      }

      if (isSuperAdmin) {
        await coursesAPI.permanentDelete(courseToDelete._id);
      } else {
        await coursesAPI.softDelete(courseToDelete._id);
      }
      setShowDeleteModal(false);
      setCourseToDelete(null);
      fetchCourses();
    } catch (err) {
      setError('فشل في حذف الدورة');
      console.error('Error deleting course:', err);
    }
  };

  const handleToggleClick = (course) => {
    setCourseToToggle(course);
    setShowToggleModal(true);
  };

  const handleToggleConfirm = async () => {
    try {
      if (!courseToToggle?._id) {
        setError('معرف الدورة غير صحيح');
        return;
      }

      await coursesAPI.toggleStatus(courseToToggle._id);
      setShowToggleModal(false);
      setCourseToToggle(null);
      fetchCourses();
    } catch (err) {
      setError('فشل في تغيير حالة الدورة');
      console.error('Error toggling course status:', err);
    }
  };

  const handleDuplicateClick = (course) => {
    setCourseToDuplicate(course);
    setShowDuplicateModal(true);
  };

  const handleDuplicateConfirm = async () => {
    try {
      if (!courseToDuplicate?._id) {
        setError('معرف الدورة غير صحيح');
        return;
      }

      await coursesAPI.duplicate(courseToDuplicate._id);
      setShowDuplicateModal(false);
      setCourseToDuplicate(null);
      fetchCourses();
    } catch (err) {
      setError('فشل في نسخ الدورة');
      console.error('Error duplicating course:', err);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await coursesAPI.getStats();
      // The stats API returns: { overview: { total, active, inactive }, ... }
      setStats(response.data.overview);
      setShowStats(true);
    } catch (err) {
      setError('فشل في تحميل الإحصائيات');
      console.error('Error fetching stats:', err);
    }
  };

  const handleEditClick = (courseId) => {
    navigate(`/admin/edit-course/${courseId}`);
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
        <Spinner animation="border" role="status">
          <span className="visually-hidden">جاري التحميل...</span>
        </Spinner>
      </div>
    );
  }

  return (
    <Container fluid className="py-4">
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h2 className="fw-bold">إدارة الدورات</h2>
          <p className="text-muted mb-0">
            إدارة الدورات مع إمكانية التعديل والحذف
          </p>
        </div>
        <div className="d-flex gap-2">
          <Button variant="outline-info" onClick={fetchStats}>
            <BarChart3 size={16} className="me-1" />
            الإحصائيات
          </Button>
          <Button variant="primary" onClick={() => navigate('/admin/add-course')}>
            اضافة دورة
          </Button>
        </div>
      </div>

      {error && <Alert variant="danger">{error}</Alert>}

      {/* معلومات تشخيصية مؤقتة */}
      {import.meta.env.DEV && (
        <Alert variant="info">
          <small>
            <strong>معلومات التشخيص:</strong><br />
            عدد الدورات المحملة: {Array.isArray(courses) ? courses.length : 'غير صحيح'}<br />
            نوع البيانات: {typeof courses} - {Array.isArray(courses) ? 'مصفوفة' : 'ليس مصفوفة'}<br />
            حالة التحميل: {loading ? 'جاري التحميل...' : 'مكتمل'}<br />
            API URL: {import.meta.env.VITE_API_URL || 'localhost:5000 (تطوير)'}
          </small>
        </Alert>
      )}

      <Card>
        <Card.Body>
          {!Array.isArray(courses) || courses.length === 0 ? (
            <Alert variant="info">
              {!Array.isArray(courses) ? 'خطأ في تحميل البيانات - البيانات ليست مصفوفة' : 'لا توجد دورات متاحة حالياً.'}
            </Alert>
          ) : (
            <Table responsive striped hover>
              <thead>
                <tr>
                  <th>العنوان</th>
                  <th>المدرب</th>
                  <th>المستوى</th>
                  <th>المدة</th>
                  <th>الحالة</th>
                  <th>تاريخ الإنشاء</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {courses.map((course) => (
                  <tr key={course._id || course.id}>
                    <td>
                      <div>
                        <strong>{course.title || 'بدون عنوان'}</strong>
                        <br />
                        <small className="text-muted">
                          {course.description ? `${course.description.substring(0, 50)}...` : 'بدون وصف'}
                        </small>
                      </div>
                    </td>
                    <td>{course.instructor || 'غير محدد'}</td>
                    <td>
                      <Badge bg="secondary">{course.level || 'غير محدد'}</Badge>
                    </td>
                    <td>{course.duration || 'غير محدد'}</td>
                    <td>
                      <Badge bg={course.isActive ? 'success' : 'secondary'}>
                        {course.isActive ? 'مفعلة' : 'غير مفعلة'}
                      </Badge>
                    </td>
                    <td>
                      {course.createdAt ? new Date(course.createdAt).toLocaleDateString('ar-SA') : 'غير محدد'}
                    </td>
                    <td>
                      <div className="d-flex gap-1">
                        <Button
                          variant="outline-primary"
                          size="sm"
                          onClick={() => navigate(`/courses/${course._id || course.id}`)}
                          title="عرض الدورة"
                          disabled={!course._id && !course.id}
                        >
                          <Eye size={16} />
                        </Button>

                        <Button
                          variant="outline-warning"
                          size="sm"
                          onClick={() => handleEditClick(course._id || course.id)}
                          title="تعديل الدورة"
                          disabled={!course._id && !course.id}
                        >
                          <Edit size={16} />
                        </Button>

                        <Button
                          variant="outline-info"
                          size="sm"
                          onClick={() => handleDuplicateClick(course)}
                          title="نسخ الدورة"
                        >
                          <Copy size={16} />
                        </Button>

                        <Button
                          variant={course.isActive ? 'outline-secondary' : 'outline-success'}
                          size="sm"
                          onClick={() => handleToggleClick(course)}
                          title={course.isActive ? 'إلغاء التفعيل' : 'تفعيل'}
                        >
                          {course.isActive ? <EyeOff size={16} /> : <Eye size={16} />}
                        </Button>

                        <Button
                          variant="outline-danger"
                          size="sm"
                          onClick={() => handleDeleteClick(course)}
                          title={isSuperAdmin ? 'حذف نهائي' : 'أرشفة'}
                        >
                          {isSuperAdmin ? <Trash2 size={16} /> : <Archive size={16} />}
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          )}
        </Card.Body>
      </Card>

      {/* Delete Modal */}
      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>
            {isSuperAdmin ? 'حذف الدورة نهائياً' : 'أرشفة الدورة'}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          هل أنت متأكد من {isSuperAdmin ? 'حذف' : 'أرشفة'} الدورة "{courseToDelete?.title}"؟
          {isSuperAdmin && (
            <div className="alert alert-warning mt-2">
              <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه!
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
            إلغاء
          </Button>
          <Button variant="danger" onClick={handleDeleteConfirm}>
            {isSuperAdmin ? 'حذف نهائي' : 'أرشفة'}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Toggle Status Modal */}
      <Modal show={showToggleModal} onHide={() => setShowToggleModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>تغيير حالة الدورة</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          هل تريد {courseToToggle?.isActive ? 'إلغاء تفعيل' : 'تفعيل'} الدورة "{courseToToggle?.title}"؟
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowToggleModal(false)}>
            إلغاء
          </Button>
          <Button variant="primary" onClick={handleToggleConfirm}>
            {courseToToggle?.isActive ? 'إلغاء التفعيل' : 'تفعيل'}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Duplicate Course Modal */}
      <Modal show={showDuplicateModal} onHide={() => setShowDuplicateModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>نسخ الدورة</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          هل تريد إنشاء نسخة من الدورة "{courseToDuplicate?.title}"؟
          <br />
          <small className="text-muted">
            سيتم إنشاء نسخة جديدة من الدورة بجميع محتوياتها، وستكون غير مفعلة افتراضياً.
          </small>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDuplicateModal(false)}>
            إلغاء
          </Button>
          <Button variant="primary" onClick={handleDuplicateConfirm}>
            نسخ الدورة
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Statistics Modal */}
      <Modal show={showStats} onHide={() => setShowStats(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>إحصائيات الدورات</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {stats && (
            <div className="row">
              <div className="col-md-6">
                <div className="card text-center mb-3">
                  <div className="card-body">
                    <h5 className="card-title">إجمالي الدورات</h5>
                    <h2 className="text-primary">{stats.total}</h2>
                  </div>
                </div>
              </div>
              <div className="col-md-6">
                <div className="card text-center mb-3">
                  <div className="card-body">
                    <h5 className="card-title">الدورات المفعلة</h5>
                    <h2 className="text-success">{stats.active}</h2>
                  </div>
                </div>
              </div>
              <div className="col-md-6">
                <div className="card text-center mb-3">
                  <div className="card-body">
                    <h5 className="card-title">الدورات غير المفعلة</h5>
                    <h2 className="text-secondary">{stats.inactive}</h2>
                  </div>
                </div>
              </div>
              <div className="col-md-4">
                <div className="card text-center mb-3">
                  <div className="card-body">
                    <h5 className="card-title">معدل النشاط</h5>
                    <h2 className="text-info">
                      {stats.total > 0 ? Math.round((stats.active / stats.total) * 100) : 0}%
                    </h2>
                  </div>
                </div>
              </div>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowStats(false)}>
            إغلاق
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default ManageCourses;
