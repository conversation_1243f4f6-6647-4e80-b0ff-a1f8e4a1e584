'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useAuth } from '../../../../../../../../../contexts/AuthContext';
import ProtectedRoute from '../../../../../../../../../components/ProtectedRoute';
import api from '../../../../../../../../../lib/api';
import { toast } from 'react-toastify';
import {
  ArrowLeft,
  Save,
  Play,
  FileText,
  Edit,
  Upload,
  Eye
} from 'lucide-react';

export default function EditLesson() {
  const { user } = useAuth();
  const router = useRouter();
  const params = useParams();
  const { id: courseId, unitId, lessonId } = params;

  const [course, setCourse] = useState(null);
  const [unit, setUnit] = useState(null);
  const [lesson, setLesson] = useState(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [uploadingVideo, setUploadingVideo] = useState(false);

  const [lessonForm, setLessonForm] = useState({
    title: '',
    type: 'video',
    content: '',
    videoUrl: '',
    textContent: '',
    duration: ''
  });

  useEffect(() => {
    fetchData();
  }, [courseId, unitId, lessonId]);

  const fetchData = async () => {
    try {
      setLoading(true);
      
      const response = await api.get(`/courses/${courseId}`);
      const courseData = response.data.course;
      setCourse(courseData);
      
      const foundUnit = courseData.units.find(u => u._id === unitId);
      setUnit(foundUnit);
      
      if (foundUnit) {
        const foundLesson = foundUnit.lessons.find(l => l._id === lessonId);
        setLesson(foundLesson);
        
        if (foundLesson) {
          setLessonForm({
            title: foundLesson.title || '',
            type: foundLesson.type || 'video',
            content: foundLesson.content || '',
            videoUrl: foundLesson.videoUrl || '',
            textContent: foundLesson.textContent || '',
            duration: foundLesson.duration || ''
          });
        }
      }
      
    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('خطأ في جلب البيانات');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setLessonForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleVideoUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    if (!file.type.startsWith('video/')) {
      toast.error('يرجى اختيار ملف فيديو صحيح');
      return;
    }

    setUploadingVideo(true);

    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', 'video');

    try {
      const response = await api.post('/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      setLessonForm(prev => ({
        ...prev,
        videoUrl: response.data.url
      }));

      toast.success('تم رفع الفيديو بنجاح');
    } catch (error) {
      console.error('Error uploading video:', error);
      toast.error('خطأ في رفع الفيديو');
    } finally {
      setUploadingVideo(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!lessonForm.title) {
      toast.error('عنوان الدرس مطلوب');
      return;
    }

    try {
      setSaving(true);
      
      // تحديث الدرس في قاعدة البيانات
      await api.put(`/courses/${courseId}/units/${unitId}/lessons/${lessonId}`, lessonForm);
      
      toast.success('تم تحديث الدرس بنجاح');
      router.push(`/admin/courses/${courseId}/units/${unitId}/manage`);
      
    } catch (error) {
      console.error('Error updating lesson:', error);
      toast.error('خطأ في تحديث الدرس');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <ProtectedRoute allowedRoles={['admin', 'super-admin']}>
        <div className="container-fluid py-4">
          <div className="text-center">
            <div className="spinner-border" role="status">
              <span className="visually-hidden">جاري التحميل...</span>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  if (!course || !unit || !lesson) {
    return (
      <ProtectedRoute allowedRoles={['admin', 'super-admin']}>
        <div className="container-fluid py-4">
          <div className="alert alert-danger">
            الدرس غير موجود
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute allowedRoles={['admin', 'super-admin']}>
      <div className="container-fluid py-4">
        {/* Header */}
        <div className="row mb-4">
          <div className="col-12">
            <button
              className="btn btn-outline-secondary btn-sm mb-2"
              onClick={() => router.push(`/admin/courses/${courseId}/units/${unitId}/manage`)}
            >
              <ArrowLeft className="w-4 h-4 me-1" />
              العودة للوحدة
            </button>
            <h2 className="mb-1">تعديل الدرس</h2>
            <p className="text-muted mb-0">
              {course.title} • {unit.title}
            </p>
          </div>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="row">
            <div className="col-lg-8">
              <div className="card">
                <div className="card-header">
                  <h5 className="mb-0">معلومات الدرس</h5>
                </div>
                <div className="card-body">
                  <div className="row">
                    <div className="col-12 mb-3">
                      <label className="form-label">عنوان الدرس *</label>
                      <input
                        type="text"
                        className="form-control"
                        name="title"
                        value={lessonForm.title}
                        onChange={handleInputChange}
                        required
                      />
                    </div>

                    <div className="col-md-6 mb-3">
                      <label className="form-label">نوع الدرس</label>
                      <select
                        className="form-select"
                        name="type"
                        value={lessonForm.type}
                        onChange={handleInputChange}
                      >
                        <option value="video">فيديو</option>
                        <option value="reading">قراءة</option>
                        <option value="exercise">تمرين</option>
                      </select>
                    </div>

                    <div className="col-md-6 mb-3">
                      <label className="form-label">المدة</label>
                      <input
                        type="text"
                        className="form-control"
                        name="duration"
                        value={lessonForm.duration}
                        onChange={handleInputChange}
                        placeholder="مثال: 15 دقيقة"
                      />
                    </div>

                    {lessonForm.type === 'video' && (
                      <div className="col-12 mb-3">
                        <label className="form-label">رابط الفيديو</label>
                        <div className="row">
                          <div className="col-md-6">
                            <input
                              type="file"
                              className="form-control"
                              accept="video/*"
                              onChange={handleVideoUpload}
                              disabled={uploadingVideo}
                            />
                            <small className="text-muted">أو</small>
                          </div>
                          <div className="col-md-6">
                            <input
                              type="url"
                              className="form-control"
                              name="videoUrl"
                              value={lessonForm.videoUrl}
                              onChange={handleInputChange}
                              placeholder="https://example.com/video.mp4"
                              disabled={uploadingVideo}
                            />
                          </div>
                        </div>
                        {uploadingVideo && (
                          <div className="mt-2">
                            <div className="spinner-border spinner-border-sm me-2" role="status"></div>
                            جاري رفع الفيديو...
                          </div>
                        )}
                      </div>
                    )}

                    <div className="col-12 mb-3">
                      <label className="form-label">المحتوى النصي</label>
                      <textarea
                        className="form-control"
                        name="textContent"
                        value={lessonForm.textContent}
                        onChange={handleInputChange}
                        rows="8"
                        placeholder="أدخل محتوى الدرس النصي هنا..."
                      ></textarea>
                    </div>

                    <div className="col-12 mb-3">
                      <label className="form-label">محتوى إضافي</label>
                      <textarea
                        className="form-control"
                        name="content"
                        value={lessonForm.content}
                        onChange={handleInputChange}
                        rows="4"
                        placeholder="ملاحظات أو محتوى إضافي..."
                      ></textarea>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="col-lg-4">
              {/* Preview */}
              {lessonForm.videoUrl && (
                <div className="card mb-4">
                  <div className="card-header">
                    <h6 className="mb-0">معاينة الفيديو</h6>
                  </div>
                  <div className="card-body p-0">
                    <video
                      src={lessonForm.videoUrl}
                      controls
                      className="w-100"
                      style={{ maxHeight: '250px' }}
                    >
                      متصفحك لا يدعم تشغيل الفيديو
                    </video>
                  </div>
                </div>
              )}

              {/* Actions */}
              <div className="card">
                <div className="card-body">
                  <div className="d-grid gap-2">
                    <button
                      type="submit"
                      className="btn btn-primary"
                      disabled={saving}
                    >
                      {saving ? (
                        <>
                          <div className="spinner-border spinner-border-sm me-2" role="status"></div>
                          جاري الحفظ...
                        </>
                      ) : (
                        <>
                          <Save className="w-4 h-4 me-1" />
                          حفظ التغييرات
                        </>
                      )}
                    </button>

                    <button
                      type="button"
                      className="btn btn-outline-primary"
                      onClick={() => router.push(`/courses/${courseId}/units/${unitId}/lessons/${lessonId}`)}
                    >
                      <Eye className="w-4 h-4 me-1" />
                      معاينة الدرس
                    </button>

                    <button
                      type="button"
                      className="btn btn-outline-secondary"
                      onClick={() => router.push(`/admin/courses/${courseId}/units/${unitId}/manage`)}
                    >
                      إلغاء
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </ProtectedRoute>
  );
}
