import React, { useState, useEffect } from 'react';
import { useTheme } from '../context/ThemeContext';
import { useAuth } from '../context/AuthContext';
import {
  HeroSection,
  FeaturesSection,
  TestimonialsSection,
  CTASection,
  HomeStyles
} from '../components/HomeComponents';

const Home = () => {
  const { isDark } = useTheme();
  const { token } = useAuth();
  const [animatedStats, setAnimatedStats] = useState({ students: 0, courses: 0, instructors: 0 });
  const [isVisible, setIsVisible] = useState(false);

  // Animation for statistics
  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 500);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (isVisible) {
      const animateStats = () => {
        const targets = { students: 5000, courses: 150, instructors: 50 };
        const duration = 2000;
        const steps = 60;
        const stepTime = duration / steps;

        let currentStep = 0;
        const timer = setInterval(() => {
          currentStep++;
          const progress = currentStep / steps;

          setAnimatedStats({
            students: Math.floor(targets.students * progress),
            courses: Math.floor(targets.courses * progress),
            instructors: Math.floor(targets.instructors * progress)
          });

          if (currentStep >= steps) {
            clearInterval(timer);
            setAnimatedStats(targets);
          }
        }, stepTime);

        return () => clearInterval(timer);
      };

      animateStats();
    }
  }, [isVisible]);

  return (
    <div dir="rtl">
      <HomeStyles isDark={isDark} />
      <HeroSection isDark={isDark} token={token} animatedStats={animatedStats} />
      <FeaturesSection isDark={isDark} />
      <TestimonialsSection isDark={isDark} />
      <CTASection token={token} animatedStats={animatedStats} />
    </div>
  );
};

export default Home;