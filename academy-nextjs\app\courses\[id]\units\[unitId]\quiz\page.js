'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useAuth } from '../../../../../../contexts/AuthContext';
import api from '../../../../../../lib/api';
import { toast } from 'react-toastify';
import {
  ArrowLeft,
  Clock,
  CheckCircle,
  XCircle,
  Award,
  AlertTriangle,
  HelpCircle
} from 'lucide-react';

export default function UnitQuizPage() {
  const { user } = useAuth();
  const router = useRouter();
  const params = useParams();
  const { id: courseId, unitId } = params;

  const [course, setCourse] = useState(null);
  const [unit, setUnit] = useState(null);
  const [quiz, setQuiz] = useState(null);
  const [answers, setAnswers] = useState({});
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [timeLeft, setTimeLeft] = useState(0);
  const [quizStarted, setQuizStarted] = useState(false);
  const [quizCompleted, setQuizCompleted] = useState(false);
  const [results, setResults] = useState(null);

  useEffect(() => {
    fetchData();
  }, [courseId, unitId]);

  useEffect(() => {
    let timer;
    if (quizStarted && timeLeft > 0 && !quizCompleted) {
      timer = setInterval(() => {
        setTimeLeft(prev => {
          if (prev <= 1) {
            submitQuiz(); // إرسال تلقائي عند انتهاء الوقت
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => clearInterval(timer);
  }, [quizStarted, timeLeft, quizCompleted]);

  const fetchData = async () => {
    try {
      setLoading(true);
      
      // جلب الدورة
      const courseResponse = await api.get(`/courses/${courseId}`);
      const courseData = courseResponse.data.course;
      setCourse(courseData);
      
      // العثور على الوحدة
      const foundUnit = courseData.units.find(u => u._id === unitId);
      setUnit(foundUnit);
      
      if (foundUnit && foundUnit.quiz) {
        setQuiz(foundUnit.quiz);
        setTimeLeft(foundUnit.quiz.timeLimit * 60); // تحويل للثواني
      }
      
    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('خطأ في جلب البيانات');
    } finally {
      setLoading(false);
    }
  };

  const startQuiz = () => {
    setQuizStarted(true);
    setTimeLeft(quiz.timeLimit * 60);
  };

  const handleAnswerChange = (questionIndex, answer) => {
    setAnswers(prev => ({
      ...prev,
      [questionIndex]: answer
    }));
  };

  const submitQuiz = async () => {
    try {
      setSubmitting(true);
      
      // تحضير الإجابات
      const answersArray = quiz.questions.map((_, index) => answers[index] || '');
      
      const response = await api.post(`/courses/${courseId}/units/${unitId}/quiz`, {
        answers: answersArray
      });
      
      setResults(response.data);
      setQuizCompleted(true);
      
      if (response.data.passed) {
        toast.success('تهانينا! لقد نجحت في الاختبار');
      } else {
        toast.warning('لم تحصل على الدرجة المطلوبة للنجاح');
      }
      
    } catch (error) {
      console.error('Error submitting quiz:', error);
      toast.error('خطأ في إرسال الاختبار');
    } finally {
      setSubmitting(false);
    }
  };

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  if (loading) {
    return (
      <div className="container py-4">
        <div className="text-center">
          <div className="spinner-border" role="status">
            <span className="visually-hidden">جاري التحميل...</span>
          </div>
        </div>
      </div>
    );
  }

  if (!course || !unit || !quiz) {
    return (
      <div className="container py-4">
        <div className="alert alert-danger">
          الاختبار غير موجود
        </div>
      </div>
    );
  }

  return (
    <div className="container py-4">
      {/* Header */}
      <div className="row mb-4">
        <div className="col-12">
          <button
            className="btn btn-outline-secondary btn-sm mb-2"
            onClick={() => router.push(`/courses/${courseId}`)}
          >
            <ArrowLeft className="w-4 h-4 me-1" />
            العودة للدورة
          </button>
          <h2 className="mb-1">{quiz.title}</h2>
          <p className="text-muted mb-0">
            {course.title} • الوحدة: {unit.title}
          </p>
        </div>
      </div>

      {!quizStarted ? (
        /* Quiz Instructions */
        <div className="row justify-content-center">
          <div className="col-lg-8">
            <div className="card">
              <div className="card-body text-center">
                <Award className="w-16 h-16 text-primary mx-auto mb-4" />
                <h3 className="mb-3">اختبار الوحدة</h3>
                <p className="text-muted mb-4">
                  اختبر فهمك للمحتوى الذي تعلمته في هذه الوحدة
                </p>
                
                <div className="row mb-4">
                  <div className="col-md-4">
                    <div className="d-flex align-items-center justify-content-center">
                      <HelpCircle className="w-5 h-5 text-info me-2" />
                      <div>
                        <div className="fw-bold">{quiz.questions.length}</div>
                        <small className="text-muted">سؤال</small>
                      </div>
                    </div>
                  </div>
                  <div className="col-md-4">
                    <div className="d-flex align-items-center justify-content-center">
                      <Clock className="w-5 h-5 text-warning me-2" />
                      <div>
                        <div className="fw-bold">{quiz.timeLimit}</div>
                        <small className="text-muted">دقيقة</small>
                      </div>
                    </div>
                  </div>
                  <div className="col-md-4">
                    <div className="d-flex align-items-center justify-content-center">
                      <Award className="w-5 h-5 text-success me-2" />
                      <div>
                        <div className="fw-bold">{quiz.passingScore}%</div>
                        <small className="text-muted">للنجاح</small>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="alert alert-info">
                  <AlertTriangle className="w-4 h-4 me-2" />
                  تأكد من إكمال جميع الدروس قبل بدء الاختبار. لن تتمكن من العودة بعد البدء.
                </div>

                <button
                  className="btn btn-primary btn-lg"
                  onClick={startQuiz}
                >
                  بدء الاختبار
                </button>
              </div>
            </div>
          </div>
        </div>
      ) : quizCompleted && results ? (
        /* Quiz Results */
        <div className="row justify-content-center">
          <div className="col-lg-8">
            <div className="card">
              <div className="card-body text-center">
                {results.passed ? (
                  <CheckCircle className="w-16 h-16 text-success mx-auto mb-4" />
                ) : (
                  <XCircle className="w-16 h-16 text-danger mx-auto mb-4" />
                )}
                
                <h3 className={`mb-3 ${results.passed ? 'text-success' : 'text-danger'}`}>
                  {results.passed ? 'تهانينا! لقد نجحت' : 'لم تنجح في الاختبار'}
                </h3>
                
                <div className="row mb-4">
                  <div className="col-md-3">
                    <div className="fw-bold text-primary">{results.score}%</div>
                    <small className="text-muted">نتيجتك</small>
                  </div>
                  <div className="col-md-3">
                    <div className="fw-bold">{results.correctAnswers}</div>
                    <small className="text-muted">إجابة صحيحة</small>
                  </div>
                  <div className="col-md-3">
                    <div className="fw-bold">{results.totalQuestions}</div>
                    <small className="text-muted">إجمالي الأسئلة</small>
                  </div>
                  <div className="col-md-3">
                    <div className="fw-bold">{results.passingScore}%</div>
                    <small className="text-muted">درجة النجاح</small>
                  </div>
                </div>

                <div className="d-flex gap-2 justify-content-center">
                  <button
                    className="btn btn-outline-secondary"
                    onClick={() => router.push(`/courses/${courseId}`)}
                  >
                    العودة للدورة
                  </button>
                  {results.passed && (
                    <button
                      className="btn btn-primary"
                      onClick={() => {
                        // الانتقال للوحدة التالية إذا كانت متاحة
                        const nextUnitIndex = course.units.findIndex(u => u._id === unitId) + 1;
                        if (nextUnitIndex < course.units.length) {
                          const nextUnit = course.units[nextUnitIndex];
                          if (nextUnit.lessons.length > 0) {
                            router.push(`/courses/${courseId}/units/${nextUnit._id}/lessons/${nextUnit.lessons[0]._id}`);
                          }
                        } else {
                          router.push(`/courses/${courseId}`);
                        }
                      }}
                    >
                      {course.units.findIndex(u => u._id === unitId) + 1 < course.units.length ? 'الوحدة التالية' : 'إنهاء الدورة'}
                    </button>
                  )}
                  {!results.passed && (
                    <button
                      className="btn btn-warning"
                      onClick={() => {
                        setQuizStarted(false);
                        setQuizCompleted(false);
                        setResults(null);
                        setAnswers({});
                        setTimeLeft(quiz.timeLimit * 60);
                      }}
                    >
                      إعادة المحاولة
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        /* Quiz Questions */
        <div className="row">
          <div className="col-lg-8">
            <div className="card">
              <div className="card-header d-flex justify-content-between align-items-center">
                <h5 className="mb-0">{quiz.title}</h5>
                <div className="d-flex align-items-center text-warning">
                  <Clock className="w-4 h-4 me-1" />
                  <span className="fw-bold">{formatTime(timeLeft)}</span>
                </div>
              </div>
              <div className="card-body">
                {quiz.questions.map((question, index) => (
                  <div key={question._id} className="mb-4">
                    <h6 className="mb-3">
                      السؤال {index + 1}: {question.question}
                    </h6>
                    <div className="row">
                      {question.options.map((option, optionIndex) => (
                        <div key={optionIndex} className="col-12 mb-2">
                          <div className="form-check">
                            <input
                              className="form-check-input"
                              type="radio"
                              name={`question-${index}`}
                              id={`q${index}-opt${optionIndex}`}
                              value={option}
                              checked={answers[index] === option}
                              onChange={(e) => handleAnswerChange(index, e.target.value)}
                            />
                            <label className="form-check-label" htmlFor={`q${index}-opt${optionIndex}`}>
                              {String.fromCharCode(65 + optionIndex)}. {option}
                            </label>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}

                <div className="d-flex justify-content-between align-items-center mt-4">
                  <button
                    className="btn btn-outline-secondary"
                    onClick={() => router.push(`/courses/${courseId}`)}
                  >
                    <ArrowLeft className="w-4 h-4 me-1" />
                    العودة للدورة
                  </button>

                  <button
                    className="btn btn-success"
                    onClick={submitQuiz}
                    disabled={submitting || Object.keys(answers).length < quiz.questions.length}
                  >
                    {submitting ? (
                      <>
                        <div className="spinner-border spinner-border-sm me-2" role="status"></div>
                        جاري الإرسال...
                      </>
                    ) : (
                      <>
                        <CheckCircle className="w-4 h-4 me-1" />
                        إرسال الاختبار
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div className="col-lg-4">
            {/* Quiz Info */}
            <div className="card mb-4">
              <div className="card-header">
                <h6 className="mb-0">معلومات الاختبار</h6>
              </div>
              <div className="card-body">
                <div className="d-flex align-items-center mb-2">
                  <HelpCircle className="w-4 h-4 me-2 text-info" />
                  <span>{quiz.questions.length} سؤال</span>
                </div>
                <div className="d-flex align-items-center mb-2">
                  <Clock className="w-4 h-4 me-2 text-warning" />
                  <span>{quiz.timeLimit} دقيقة</span>
                </div>
                <div className="d-flex align-items-center mb-2">
                  <Award className="w-4 h-4 me-2 text-success" />
                  <span>درجة النجاح: {quiz.passingScore}%</span>
                </div>
              </div>
            </div>

            {/* Progress */}
            <div className="card">
              <div className="card-header">
                <h6 className="mb-0">التقدم</h6>
              </div>
              <div className="card-body">
                <div className="progress mb-2" style={{ height: '10px' }}>
                  <div
                    className="progress-bar"
                    role="progressbar"
                    style={{ width: `${(Object.keys(answers).length / quiz.questions.length) * 100}%` }}
                  ></div>
                </div>
                <small className="text-muted">
                  {Object.keys(answers).length} من {quiz.questions.length} أسئلة مجابة
                </small>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
