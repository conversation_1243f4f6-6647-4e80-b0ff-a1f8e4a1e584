import { NextResponse } from 'next/server';
import { withAuth } from '../../../lib/auth';
import connectDB from '../../../lib/mongodb';
import Statistics from '../../../models/Statistics';
import User from '../../../models/User';
import Course from '../../../models/Course';
import Enrollment from '../../../models/Enrollment';

// GET - جلب الإحصائيات
export const GET = withAuth(async (request) => {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || 'daily';
    const refresh = searchParams.get('refresh') === 'true';
    
    let stats;
    
    if (refresh) {
      // إعادة حساب الإحصائيات
      stats = await Statistics.calculateStats(period);
    } else {
      // جلب آخر إحصائيات محفوظة
      stats = await Statistics.getLatestStats(period);
      
      // إذا لم توجد إحصائيات أو كانت قديمة (أكثر من ساعة)
      if (!stats || (new Date() - stats.lastUpdated) > 60 * 60 * 1000) {
        stats = await Statistics.calculateStats(period);
      }
    }
    
    return NextResponse.json({
      statistics: stats
    }, { status: 200 });

  } catch (error) {
    console.error('Statistics fetch error:', error);
    return NextResponse.json(
      { message: 'خطأ في جلب الإحصائيات' },
      { status: 500 }
    );
  }
}, { roles: ['admin', 'super-admin'] });

// POST - حساب إحصائيات مخصصة
export const POST = withAuth(async (request) => {
  try {
    await connectDB();
    
    const { type, filters = {} } = await request.json();
    
    let result = {};
    
    switch (type) {
      case 'dashboard':
        result = await getDashboardStats(filters);
        break;
      case 'users':
        result = await getUserStats(filters);
        break;
      case 'courses':
        result = await getCourseStats(filters);
        break;
      case 'enrollments':
        result = await getEnrollmentStats(filters);
        break;
      case 'leaderboard':
        result = await getLeaderboardStats(filters);
        break;
      default:
        return NextResponse.json(
          { message: 'نوع الإحصائية غير مدعوم' },
          { status: 400 }
        );
    }
    
    return NextResponse.json({
      type,
      data: result,
      generatedAt: new Date()
    }, { status: 200 });

  } catch (error) {
    console.error('Custom statistics error:', error);
    return NextResponse.json(
      { message: 'خطأ في حساب الإحصائيات المخصصة' },
      { status: 500 }
    );
  }
}, { roles: ['admin', 'super-admin'] });

// دالة حساب إحصائيات لوحة التحكم
async function getDashboardStats(filters) {
  const now = new Date();
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
  const startOfWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  
  const [
    totalUsers,
    totalStudents,
    totalAdmins,
    totalCourses,
    activeCourses,
    totalEnrollments,
    completedEnrollments,
    monthlyNewUsers,
    weeklyActiveUsers,
    recentEnrollments
  ] = await Promise.all([
    User.countDocuments(),
    User.countDocuments({ role: 'student' }),
    User.countDocuments({ role: { $in: ['admin', 'super-admin'] } }),
    Course.countDocuments(),
    Course.countDocuments({ isActive: true }),
    Enrollment.countDocuments(),
    Enrollment.countDocuments({ isCompleted: true }),
    User.countDocuments({ createdAt: { $gte: startOfMonth } }),
    User.countDocuments({ lastActive: { $gte: startOfWeek } }),
    Enrollment.find()
      .populate('student', 'name')
      .populate('course', 'title')
      .sort({ enrolledAt: -1 })
      .limit(10)
      .lean()
  ]);
  
  return {
    overview: {
      totalUsers,
      totalStudents,
      totalAdmins,
      totalCourses,
      activeCourses,
      totalEnrollments,
      completedEnrollments,
      completionRate: totalEnrollments > 0 ? 
        Math.round((completedEnrollments / totalEnrollments) * 100) : 0
    },
    trends: {
      monthlyNewUsers,
      weeklyActiveUsers,
      userGrowthRate: totalUsers > 0 ? 
        Math.round((monthlyNewUsers / totalUsers) * 100) : 0
    },
    recentActivity: recentEnrollments
  };
}

// دالة حساب إحصائيات المستخدمين
async function getUserStats(filters) {
  const pipeline = [
    {
      $group: {
        _id: '$role',
        count: { $sum: 1 },
        activeCount: {
          $sum: {
            $cond: ['$isActive', 1, 0]
          }
        }
      }
    }
  ];
  
  const roleStats = await User.aggregate(pipeline);
  
  const registrationTrend = await User.aggregate([
    {
      $group: {
        _id: {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' }
        },
        count: { $sum: 1 }
      }
    },
    { $sort: { '_id.year': 1, '_id.month': 1 } },
    { $limit: 12 }
  ]);
  
  return {
    roleDistribution: roleStats,
    registrationTrend,
    totalUsers: await User.countDocuments()
  };
}

// دالة حساب إحصائيات الدورات
async function getCourseStats(filters) {
  const [
    levelStats,
    categoryStats,
    popularCourses,
    enrollmentTrend
  ] = await Promise.all([
    Course.aggregate([
      { $group: { _id: '$level', count: { $sum: 1 } } }
    ]),
    Course.aggregate([
      { $group: { _id: '$category', count: { $sum: 1 } } }
    ]),
    Course.find({ isActive: true })
      .sort({ enrollmentCount: -1 })
      .limit(10)
      .select('title instructor enrollmentCount rating')
      .lean(),
    Enrollment.aggregate([
      {
        $group: {
          _id: {
            year: { $year: '$enrolledAt' },
            month: { $month: '$enrolledAt' }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1 } },
      { $limit: 12 }
    ])
  ]);
  
  return {
    levelDistribution: levelStats,
    categoryDistribution: categoryStats,
    popularCourses,
    enrollmentTrend
  };
}

// دالة حساب إحصائيات التسجيلات
async function getEnrollmentStats(filters) {
  const completionStats = await Enrollment.aggregate([
    {
      $group: {
        _id: '$isCompleted',
        count: { $sum: 1 },
        averageProgress: { $avg: '$progress' }
      }
    }
  ]);
  
  const studyTimeStats = await Enrollment.aggregate([
    {
      $group: {
        _id: null,
        totalMinutes: { $sum: '$studyTime.totalMinutes' },
        averageMinutes: { $avg: '$studyTime.totalMinutes' },
        totalSessions: { $sum: '$studyTime.sessionsCount' }
      }
    }
  ]);
  
  return {
    completionStats,
    studyTimeStats: studyTimeStats[0] || {
      totalMinutes: 0,
      averageMinutes: 0,
      totalSessions: 0
    }
  };
}

// دالة حساب إحصائيات لوحة الشرف
async function getLeaderboardStats(filters) {
  const limit = filters.limit || 20;
  
  const topStudents = await User.aggregate([
    { $match: { role: 'student' } },
    {
      $lookup: {
        from: 'enrollments',
        localField: '_id',
        foreignField: 'student',
        as: 'enrollments'
      }
    },
    {
      $addFields: {
        completedCourses: {
          $size: {
            $filter: {
              input: '$enrollments',
              cond: { $eq: ['$$this.isCompleted', true] }
            }
          }
        },
        totalPoints: {
          $sum: {
            $map: {
              input: {
                $filter: {
                  input: '$enrollments',
                  cond: { $eq: ['$$this.isCompleted', true] }
                }
              },
              in: { $ifNull: ['$$this.finalScore', 0] }
            }
          }
        },
        totalStudyHours: {
          $divide: [
            {
              $sum: {
                $map: {
                  input: '$enrollments',
                  in: { $ifNull: ['$$this.studyTime.totalMinutes', 0] }
                }
              }
            },
            60
          ]
        }
      }
    },
    {
      $project: {
        name: 1,
        email: 1,
        completedCourses: 1,
        totalPoints: 1,
        totalStudyHours: { $round: ['$totalStudyHours', 1] },
        createdAt: 1
      }
    },
    { $sort: { totalPoints: -1 } },
    { $limit: limit }
  ]);
  
  return {
    topStudents,
    totalStudents: await User.countDocuments({ role: 'student' })
  };
}
