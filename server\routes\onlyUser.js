import express from 'express';
import UserProgress from '../models/UserProgress.js';
import HonorBoard from '../models/HonorBoard.js';
import Course from '../models/Course.js';
import { verifyToken } from '../middleware/verifyToken.js';
import { allowRoles } from '../middleware/adminOnly.js';



const router = express.Router();

// Update user profile
router.put('/profile/:id', verifyToken, allowRoles('student',  'super-admin'), async (req, res) => {
  try {
    const { name, email } = req.body;
    const user = await User.findByIdAndUpdate(
      req.params.id,
      { name, email },
      { new: true }
    ).select('-password');

    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }

    res.json(user);
  } catch (error) {
    res.status(500).json({ message: 'خطأ في الخادم', error: error.message });
  }
});

// Get user profile
router.get('/profile', verifyToken, allowRoles('student', 'admin', 'super-admin'), async (req, res) => {
  try {
    const user = await User.findById(req.user.id).select('-password');
    res.json(user);
  } catch (error) {
    res.status(500).json({ message: 'خطأ في الخادم', error: error.message });
  }
});

// Get user courses
router.get('/courses', verifyToken, allowRoles('student'), async (req, res) => {
  try {
    const user = await User.findById(req.user.id).populate('courses');
    res.json(user.courses);
  } catch (error) {
    res.status(500).json({ message: 'خطاء في الخادم', error: error.message });
  }
});
export default router;