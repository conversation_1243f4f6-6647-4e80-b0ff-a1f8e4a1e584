import express from 'express';
import multer from 'multer';
import Course from '../models/Course.js';
import UserProgress from '../models/UserProgress.js';
import { verifyToken } from '../middleware/verifyToken.js';
import { allowRoles } from '../middleware/adminOnly.js';

const router = express.Router();

// Configure multer for video and image uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    if (file.fieldname === 'video') {
      cb(null, 'uploads/videos/');
    } else if (file.fieldname === 'image') {
      cb(null, 'uploads/images/');
    } else {
      cb(null, 'uploads/');
    }
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + '.' + file.originalname.split('.').pop());
  }
});

const upload = multer({
  storage,
  limits: { fileSize: 100 * 1024 * 1024 }, // 100MB limit
  fileFilter: (req, file, cb) => {
    if (file.fieldname === 'video' && file.mimetype.startsWith('video/')) {
      cb(null, true);
    } else if (file.fieldname === 'image' && file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type!'), false);
    }
  }
});

// Get all courses (public)
router.get('/', async (req, res) => {
  try {
    const { search, page = 1, limit = 10 } = req.query;
    const query = { isActive: true };

    if (search) {
      query.$text = { $search: search };
    }

    const courses = await Course.find(query)
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Course.countDocuments(query);

    res.json({
      courses,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Get single course
router.get('/:id', async (req, res) => {
  try {
    const course = await Course.findById(req.params.id);
    if (!course || !course.isActive) {
      return res.status(404).json({ message: 'Course not found' });
    }
    res.json(course);
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Create course (admin and super-admin only)
router.post('/', verifyToken, allowRoles('admin', 'super-admin'), upload.fields([
  { name: 'video', maxCount: 1 },
  { name: 'image', maxCount: 1 }
]), async (req, res) => {
  try {
    console.log('Creating course with data:', req.body);
    console.log('Files received:', req.files);

    const { title, description, instructor, duration, level, tags, units } = req.body;

    // التحقق من البيانات المطلوبة
    if (!title || !description || !instructor || !duration || !level) {
      return res.status(400).json({
        message: 'Missing required fields',
        required: ['title', 'description', 'instructor', 'duration', 'level']
      });
    }

    let parsedUnits = [];
    if (units) {
      try {
        parsedUnits = JSON.parse(units);
      } catch (parseError) {
        return res.status(400).json({
          message: 'Invalid units format',
          error: parseError.message
        });
      }
    }

    const course = new Course({
      title,
      description,
      instructor,
      duration,
      level,
      tags: tags ? tags.split(',').map(tag => tag.trim()) : [],
      video: req.files?.video?.[0]?.path,
      image: req.files?.image?.[0]?.path,
      units: parsedUnits,
      createdBy: req.user.id
    });

    console.log('Saving course:', course);
    await course.save();
    console.log('Course saved successfully');

    res.status(201).json(course);
  } catch (error) {
    console.error('Error creating course:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Update course (admin and super-admin only)
router.put('/:id', verifyToken, allowRoles('admin', 'super-admin'), upload.fields([
  { name: 'video', maxCount: 1 },
  { name: 'image', maxCount: 1 }
]), async (req, res) => {
  try {
    console.log('Updating course with ID:', req.params.id);
    console.log('Update data:', req.body);
    console.log('Files received:', req.files);

    const {
      title,
      description,
      instructor,
      duration,
      level,
      tags,
      units,
      isActive,
      removeVideo,
      removeImage
    } = req.body;

    // البحث عن الدورة الحالية
    const existingCourse = await Course.findById(req.params.id);
    if (!existingCourse) {
      return res.status(404).json({ message: 'Course not found' });
    }

    // التحقق من الصلاحيات - المالك أو super-admin
    if (existingCourse.createdBy.toString() !== req.user.id && req.user.role !== 'super-admin') {
      return res.status(403).json({ message: 'Access denied. You can only edit your own courses.' });
    }

    const updateData = {
      title: title || existingCourse.title,
      description: description || existingCourse.description,
      instructor: instructor || existingCourse.instructor,
      duration: duration || existingCourse.duration,
      level: level || existingCourse.level,
      isActive: isActive !== undefined ? isActive === 'true' : existingCourse.isActive,
      updatedBy: req.user.id,
      updatedAt: new Date()
    };

    // معالجة الوسوم
    if (tags) {
      updateData.tags = tags.split(',').map(t => t.trim());
    }

    // معالجة الوحدات والدروس
    if (units) {
      try {
        const parsedUnits = JSON.parse(units);
        // تنظيف بيانات الوحدات
        updateData.units = parsedUnits.map(unit => ({
          title: unit.title || '',
          lessons: (unit.lessons || []).map(lesson => ({
            title: lesson.title || '',
            type: lesson.type || 'video',
            videoUrl: lesson.videoUrl || '',
            textContent: lesson.textContent || '',
            duration: lesson.duration || 0,
            order: lesson.order || 0
          })),
          quiz: {
            questions: (unit.quiz?.questions || []).map(q => ({
              question: q.question || '',
              options: q.options || ['', '', '', ''],
              correctAnswer: q.correctAnswer || '',
              explanation: q.explanation || ''
            }))
          },
          order: unit.order || 0
        }));
      } catch (parseError) {
        return res.status(400).json({
          message: 'Invalid units format',
          error: parseError.message
        });
      }
    }

    // معالجة الفيديو الجديد
    if (req.files?.video?.[0]) {
      updateData.video = req.files.video[0].path;
    } else if (removeVideo === 'true') {
      updateData.video = null;
    }

    // معالجة الصورة الجديدة
    if (req.files?.image?.[0]) {
      updateData.image = req.files.image[0].path;
    } else if (removeImage === 'true') {
      updateData.image = null;
    }

    const course = await Course.findByIdAndUpdate(req.params.id, updateData, {
      new: true,
      runValidators: true
    });

    if (!course) {
      return res.status(404).json({ message: 'Course not found after update' });
    }

    console.log('Course updated successfully:', course._id);
    res.json({
      message: 'Course updated successfully',
      course: course
    });
  } catch (error) {
    console.error('Error updating course:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});


// Toggle course status (admin and super-admin only)
router.patch('/:id/toggle-status', verifyToken, allowRoles('admin', 'super-admin'), async (req, res) => {
  try {
    const course = await Course.findById(req.params.id);

    if (!course) {
      return res.status(404).json({ message: 'Course not found' });
    }

    // التحقق من الصلاحيات
    if (course.createdBy.toString() !== req.user.id && req.user.role !== 'super-admin') {
      return res.status(403).json({ message: 'Access denied' });
    }

    course.isActive = !course.isActive;
    course.updatedBy = req.user.id;
    course.updatedAt = new Date();

    await course.save();

    res.json({
      message: `Course ${course.isActive ? 'activated' : 'deactivated'} successfully`,
      course: course
    });
  } catch (error) {
    console.error('Error toggling course status:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Delete course permanently (super-admin only)
router.delete('/:id', verifyToken, allowRoles('super-admin'), async (req, res) => {
  try {
    const course = await Course.findByIdAndDelete(req.params.id);

    if (!course) {
      return res.status(404).json({ message: 'Course not found' });
    }

    res.json({ message: 'Course deleted permanently' });
  } catch (error) {
    console.error('Error deleting course:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Soft delete course (admin and super-admin only)
router.patch('/:id/soft-delete', verifyToken, allowRoles('admin', 'super-admin'), async (req, res) => {
  try {
    const course = await Course.findById(req.params.id);

    if (!course) {
      return res.status(404).json({ message: 'Course not found' });
    }

    // التحقق من الصلاحيات
    if (course.createdBy.toString() !== req.user.id && req.user.role !== 'super-admin') {
      return res.status(403).json({ message: 'Access denied' });
    }

    course.isActive = false;
    course.deletedAt = new Date();
    course.updatedBy = req.user.id;
    course.updatedAt = new Date();

    await course.save();

    res.json({ message: 'Course archived successfully' });
  } catch (error) {
    console.error('Error archiving course:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Get course statistics (admin and super-admin only)
router.get('/stats/overview', verifyToken, allowRoles('admin', 'super-admin'), async (req, res) => {
  try {
    const totalCourses = await Course.countDocuments();
    const activeCourses = await Course.countDocuments({ isActive: true });
    const inactiveCourses = await Course.countDocuments({ isActive: false });

    // إحصائيات حسب المستوى
    const levelStats = await Course.aggregate([
      { $group: { _id: '$level', count: { $sum: 1 } } }
    ]);

    // إحصائيات حسب المدرب
    const instructorStats = await Course.aggregate([
      { $group: { _id: '$instructor', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);

    // الدورات الأحدث
    const recentCourses = await Course.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .select('title instructor createdAt isActive');

    res.json({
      overview: {
        total: totalCourses,
        active: activeCourses,
        inactive: inactiveCourses
      },
      levelStats,
      instructorStats,
      recentCourses
    });
  } catch (error) {
    console.error('Error fetching course statistics:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Enroll in course (authenticated users only)
router.post('/:id/enroll', verifyToken, async (req, res) => {
  try {
    const courseId = req.params.id;
    const userId = req.user.id;

    // Check if course exists and is active
    const course = await Course.findById(courseId);
    if (!course) {
      return res.status(404).json({ message: 'Course not found' });
    }

    if (!course.isActive) {
      return res.status(400).json({ message: 'Course is not active' });
    }

    // Check if user is already enrolled
    const existingProgress = await UserProgress.findOne({
      userId: userId,
      courseId: courseId
    });

    if (existingProgress) {
      return res.status(400).json({
        message: 'Already enrolled in this course',
        progress: existingProgress
      });
    }

    // Create initial progress record
    const progress = new UserProgress({
      userId: userId,
      courseId: courseId,
      currentUnitIndex: 0,
      completedLessons: [],
      passedQuizzes: [],
      progressPercentage: 0
    });

    await progress.save();

    // Update course enrollment count
    course.enrollmentCount = (course.enrollmentCount || 0) + 1;
    await course.save();

    res.status(201).json({
      message: 'Successfully enrolled in course',
      progress: progress,
      course: {
        id: course._id,
        title: course.title,
        instructor: course.instructor
      }
    });

  } catch (error) {
    console.error('Error enrolling in course:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Check enrollment status
router.get('/:id/enrollment-status', verifyToken, async (req, res) => {
  try {
    const courseId = req.params.id;
    const userId = req.user.id;

    const progress = await UserProgress.findOne({
      userId: userId,
      courseId: courseId
    });

    res.json({
      isEnrolled: !!progress,
      progress: progress
    });

  } catch (error) {
    console.error('Error checking enrollment status:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Advanced search for courses
router.get('/search/advanced', async (req, res) => {
  try {
    const {
      q,
      level,
      instructor,
      tags,
      minDuration,
      maxDuration,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      page = 1,
      limit = 10
    } = req.query;

    const query = { isActive: true };

    // البحث النصي
    if (q) {
      query.$or = [
        { title: { $regex: q, $options: 'i' } },
        { description: { $regex: q, $options: 'i' } },
        { instructor: { $regex: q, $options: 'i' } }
      ];
    }

    // فلترة حسب المستوى
    if (level) {
      query.level = level;
    }

    // فلترة حسب المدرب
    if (instructor) {
      query.instructor = { $regex: instructor, $options: 'i' };
    }

    // فلترة حسب الوسوم
    if (tags) {
      const tagArray = tags.split(',').map(tag => tag.trim());
      query.tags = { $in: tagArray };
    }

    // فلترة حسب المدة
    if (minDuration || maxDuration) {
      query.duration = {};
      if (minDuration) query.duration.$gte = minDuration;
      if (maxDuration) query.duration.$lte = maxDuration;
    }

    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    const skip = (page - 1) * limit;

    const courses = await Course.find(query)
      .sort(sortOptions)
      .skip(skip)
      .limit(parseInt(limit))
      .select('-__v');

    const total = await Course.countDocuments(query);

    res.json({
      courses,
      pagination: {
        current: parseInt(page),
        total: Math.ceil(total / limit),
        count: courses.length,
        totalCourses: total
      }
    });
  } catch (error) {
    console.error('Error in advanced search:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Duplicate course (admin and super-admin only)
router.post('/:id/duplicate', verifyToken, allowRoles('admin', 'super-admin'), async (req, res) => {
  try {
    const originalCourse = await Course.findById(req.params.id);

    if (!originalCourse) {
      return res.status(404).json({ message: 'Course not found' });
    }

    // إنشاء نسخة جديدة
    const duplicatedCourse = new Course({
      title: `${originalCourse.title} - نسخة`,
      description: originalCourse.description,
      instructor: originalCourse.instructor,
      duration: originalCourse.duration,
      level: originalCourse.level,
      tags: [...originalCourse.tags],
      units: originalCourse.units.map(unit => ({
        title: unit.title,
        lessons: unit.lessons.map(lesson => ({ ...lesson })),
        quiz: {
          questions: unit.quiz.questions.map(q => ({ ...q }))
        },
        order: unit.order
      })),
      createdBy: req.user.id,
      isActive: false // تبدأ غير مفعلة
    });

    await duplicatedCourse.save();

    res.status(201).json({
      message: 'Course duplicated successfully',
      course: duplicatedCourse
    });
  } catch (error) {
    console.error('Error duplicating course:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Update specific lesson in a unit (admin and super-admin only)
router.put('/:courseId/units/:unitIndex/lessons/:lessonIndex',
  verifyToken,
  allowRoles('admin', 'super-admin'),
  async (req, res) => {
    try {
      const { courseId, unitIndex, lessonIndex } = req.params;
      const { title, type, videoUrl, textContent, duration } = req.body;

      const course = await Course.findById(courseId);
      if (!course) {
        return res.status(404).json({ message: 'Course not found' });
      }

      // التحقق من الصلاحيات
      if (course.createdBy.toString() !== req.user.id && req.user.role !== 'super-admin') {
        return res.status(403).json({ message: 'Access denied' });
      }

      // التحقق من وجود الوحدة والدرس
      if (!course.units[unitIndex] || !course.units[unitIndex].lessons[lessonIndex]) {
        return res.status(404).json({ message: 'Unit or lesson not found' });
      }

      // تحديث الدرس
      course.units[unitIndex].lessons[lessonIndex] = {
        ...course.units[unitIndex].lessons[lessonIndex],
        title: title || course.units[unitIndex].lessons[lessonIndex].title,
        type: type || course.units[unitIndex].lessons[lessonIndex].type,
        videoUrl: videoUrl || course.units[unitIndex].lessons[lessonIndex].videoUrl,
        textContent: textContent || course.units[unitIndex].lessons[lessonIndex].textContent,
        duration: duration || course.units[unitIndex].lessons[lessonIndex].duration
      };

      course.updatedBy = req.user.id;
      course.updatedAt = new Date();

      await course.save();

      res.json({
        message: 'Lesson updated successfully',
        lesson: course.units[unitIndex].lessons[lessonIndex]
      });
    } catch (error) {
      console.error('Error updating lesson:', error);
      res.status(500).json({ message: 'Server error', error: error.message });
    }
  }
);

// Add new lesson to a unit (admin and super-admin only)
router.post('/:courseId/units/:unitIndex/lessons',
  verifyToken,
  allowRoles('admin', 'super-admin'),
  async (req, res) => {
    try {
      const { courseId, unitIndex } = req.params;
      const { title, type, videoUrl, textContent, duration } = req.body;

      const course = await Course.findById(courseId);
      if (!course) {
        return res.status(404).json({ message: 'Course not found' });
      }

      // التحقق من الصلاحيات
      if (course.createdBy.toString() !== req.user.id && req.user.role !== 'super-admin') {
        return res.status(403).json({ message: 'Access denied' });
      }

      // التحقق من وجود الوحدة
      if (!course.units[unitIndex]) {
        return res.status(404).json({ message: 'Unit not found' });
      }

      // إضافة الدرس الجديد
      const newLesson = {
        title: title || 'درس جديد',
        type: type || 'video',
        videoUrl: videoUrl || '',
        textContent: textContent || '',
        duration: duration || 0,
        order: course.units[unitIndex].lessons.length
      };

      course.units[unitIndex].lessons.push(newLesson);
      course.updatedBy = req.user.id;
      course.updatedAt = new Date();

      await course.save();

      res.status(201).json({
        message: 'Lesson added successfully',
        lesson: newLesson
      });
    } catch (error) {
      console.error('Error adding lesson:', error);
      res.status(500).json({ message: 'Server error', error: error.message });
    }
  }
);

// Delete lesson from a unit (admin and super-admin only)
router.delete('/:courseId/units/:unitIndex/lessons/:lessonIndex',
  verifyToken,
  allowRoles('admin', 'super-admin'),
  async (req, res) => {
    try {
      const { courseId, unitIndex, lessonIndex } = req.params;

      const course = await Course.findById(courseId);
      if (!course) {
        return res.status(404).json({ message: 'Course not found' });
      }

      // التحقق من الصلاحيات
      if (course.createdBy.toString() !== req.user.id && req.user.role !== 'super-admin') {
        return res.status(403).json({ message: 'Access denied' });
      }

      // التحقق من وجود الوحدة والدرس
      if (!course.units[unitIndex] || !course.units[unitIndex].lessons[lessonIndex]) {
        return res.status(404).json({ message: 'Unit or lesson not found' });
      }

      // حذف الدرس
      course.units[unitIndex].lessons.splice(lessonIndex, 1);

      // إعادة ترتيب الدروس
      course.units[unitIndex].lessons.forEach((lesson, index) => {
        lesson.order = index;
      });

      course.updatedBy = req.user.id;
      course.updatedAt = new Date();

      await course.save();

      res.json({ message: 'Lesson deleted successfully' });
    } catch (error) {
      console.error('Error deleting lesson:', error);
      res.status(500).json({ message: 'Server error', error: error.message });
    }
  }
);

export default router;