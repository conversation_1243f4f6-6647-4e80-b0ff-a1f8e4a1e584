'use client';

import { Container, <PERSON>, <PERSON>, <PERSON>, Button } from 'react-bootstrap';
import { useTheme } from '../../contexts/ThemeContext';
import { useRouter } from 'next/navigation';
import { Shield, ArrowLeft, Home } from 'lucide-react';

export default function Unauthorized() {
  const { isDark } = useTheme();
  const router = useRouter();

  return (
    <div className={`min-vh-100 d-flex align-items-center ${isDark ? 'bg-dark text-light' : 'bg-light'}`}>
      <Container>
        <Row className="justify-content-center">
          <Col md={6} lg={5}>
            <Card className={`text-center border-0 shadow-lg ${isDark ? 'bg-dark text-light' : ''}`}>
              <Card.Body className="p-5">
                <div className="text-warning mb-4">
                  <Shield size={80} />
                </div>
                
                <h1 className="display-6 fw-bold mb-3">غير مصرح</h1>
                
                <p className="lead text-muted mb-4">
                  عذراً، ليس لديك صلاحية للوصول إلى هذه الصفحة
                </p>
                
                <p className="text-muted mb-4">
                  إذا كنت تعتقد أن هذا خطأ، يرجى التواصل مع الإدارة أو التأكد من تسجيل الدخول بالحساب الصحيح.
                </p>
                
                <div className="d-flex gap-3 justify-content-center">
                  <Button 
                    variant="outline-secondary"
                    onClick={() => router.back()}
                    className="d-flex align-items-center"
                  >
                    <ArrowLeft size={16} className="me-2" />
                    العودة
                  </Button>
                  
                  <Button 
                    variant="primary"
                    onClick={() => router.push('/')}
                    className="d-flex align-items-center"
                  >
                    <Home size={16} className="me-2" />
                    الصفحة الرئيسية
                  </Button>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>
    </div>
  );
}
