@tailwind base;
@tailwind components;
@tailwind utilities;

/* Theme Support */
[data-bs-theme="dark"] {
  --bs-body-bg: #212529;
  --bs-body-color: #dee2e6;
}

[data-bs-theme="light"] {
  --bs-body-bg: #f8f9fa;
  --bs-body-color: #212529;
}

/* Arabic RTL Support */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .me-1,
[dir="rtl"] .me-2,
[dir="rtl"] .me-3 {
  margin-right: 0 !important;
  margin-left: 0.25rem !important;
}

[dir="rtl"] .ms-1,
[dir="rtl"] .ms-2,
[dir="rtl"] .ms-3 {
  margin-left: 0 !important;
  margin-right: 0.25rem !important;
}

[dir="rtl"] .navbar-brand {
  margin-right: 0;
  margin-left: 1rem;
}

[dir="rtl"] .input-group .btn {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

[dir="rtl"] .input-group .form-control {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

/* Custom styles for the educational platform */
.hover-shadow {
  transition: box-shadow 0.15s ease-in-out;
}

.hover-shadow:hover {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

/* Sticky positioning for admin sidebar */
.sticky-top {
  position: sticky;
  top: 100px;
  z-index: 1020;
}

/* Custom video player styles */
video {
  border-radius: 0.375rem;
}

/* Loading spinner styles */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* Custom navbar styling */
.navbar {
  backdrop-filter: blur(10px);
}

/* Chat widget animations */
.position-fixed {
  transition: all 0.3s ease-in-out;
}

/* Course card hover effects */
.card {
  transition: transform 0.2s ease-in-out;
}

.card:hover {
  transform: translateY(-2px);
}

/* Progress bar styling */
.progress {
  border-radius: 10px;
  overflow: hidden;
}

/* Form styling improvements */
.form-control:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Button hover effects */
.btn {
  transition: all 0.15s ease-in-out;
}

/* Table styling */
.table > :not(caption) > * > * {
  padding: 1rem 0.75rem;
}

.table-responsive {
  border-radius: 0.375rem;
}

/* Badge styling */
.badge {
  font-weight: 500;
}

/* Alert styling */
.alert {
  border: none;
  border-radius: 0.5rem;
}

/* Modal styling */
.modal-content {
  border: none;
  border-radius: 0.5rem;
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}