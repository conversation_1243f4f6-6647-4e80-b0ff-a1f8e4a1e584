import React, { useEffect, useState } from "react";
import { authAPI } from "../services/api";
import * as Yup from "yup";
import { useFormik } from "formik";
import { toast } from "react-toastify";
import { useNavigate } from "react-router-dom";
import { Container, Card, Button, Modal, Form, Spinner } from "react-bootstrap";

const Profile = () => {
  const navigate = useNavigate();
  const [profile, setProfile] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [showModal, setShowModal] = useState(false);
  const { getProfile, updateProfile } = authAPI;

  const [initialValues, setInitialValues] = useState({
    name: "",
    email: "",
    password: "",
    confirmPassword: "",
  });

  const validationSchema = Yup.object({
    name: Yup.string().required("الاسم مطلوب"),
    email: Yup.string().email("البريد الإلكتروني غير صالح").required("البريد الإلكتروني مطلوب"),
    password: Yup.string().min(6, "يجب أن تكون كلمة المرور 6 أحرف على الأقل"),
    confirmPassword: Yup.string()
      .oneOf([Yup.ref("password"), null], "كلمات المرور غير متطابقة")
      .required("تأكيد كلمة المرور مطلوب"),
  });

  useEffect(() => {
    const fetchProfile = async () => {
      try {
        const response = await getProfile();
        console.log('📦 البيانات:', response.data);
        const data =  response.data; // حسب ما يعيد الخادم
        setProfile(data);
        setInitialValues({
          name: data.name,
          email: data.email,
          password: "",
          confirmPassword: "",
        });
        setLoading(false);
      } catch (error) {
        setError(error.message);
        setLoading(false);
      }
    };
  
    fetchProfile();
  }, []);

  const formik = useFormik({
    initialValues,
    validationSchema,
    enableReinitialize: true,
    
    onSubmit: async (values) => {
      try {
        const response = await updateProfile(profile._id, values); // استخدم _id
        setProfile(response.data);
        toast.success("تم تحديث الملف الشخصي بنجاح");
        setShowModal(false);
      } catch (error) {
        toast.error("حدث خطأ أثناء التحديث");
      }
    }
  });

  if (loading) {
    return <div className="text-center mt-5"><Spinner animation="border" variant="primary" /></div>;
  }

  if (error) {
    return <div className="text-danger text-center mt-5">خطأ: {error}</div>;
  }

  return (
    <Container className="mt-5">
      <Card className="shadow">
        <Card.Body>
          <Card.Title className="mb-4">الملف الشخصي</Card.Title>
          <p><strong>الاسم:</strong> {profile.name}</p>
          <p><strong>البريد الإلكتروني:</strong> {profile.email}</p>
          <Button variant="primary" onClick={() => setShowModal(true)}>تعديل</Button>
        </Card.Body>
      </Card>
      
      {/* Card show the user's progress in the course */}
     {profile.courses && profile.courses.length > 0 ? (
  <Card className="mt-4 shadow">
    <Card.Body>
      <Card.Title className="mb-4">تقدمك في الدورات</Card.Title>
      {profile.courses.map((course, index) => (
        <div key={index} className="mb-3">
          <p><strong>دورة:</strong> {course.title}</p>
          <div className="d-flex align-items-center mb-2">
            <div style={{ flex: 1 }}>
              <div className="progress" style={{ height: '20px' }}>
                <div
                  className="progress-bar"
                  role="progressbar"
                  style={{ width: `${course.progress}%` }}
                  aria-valuenow={course.progress}
                  aria-valuemin={0}
                  aria-valuemax={100}
                >
                  {course.progress}%
                </div>
              </div>
            </div>
          </div>
        </div>
      ))}
    </Card.Body>
  </Card>
) : (
  <Card className="mt-4 shadow">
    <Card.Body>
      <Card.Title className="mb-4">تقدمك في الدورات</Card.Title>
      <p>لاتوجد دورات تقدم فيها حالياً.</p>
    </Card.Body>
  </Card>
)}

      {/* Modal for editing profile */}
      <Modal show={showModal} onHide={() => setShowModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>تعديل الملف الشخصي</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form onSubmit={formik.handleSubmit}>
            <Form.Group controlId="name" className="mb-3">
              <Form.Label>الاسم</Form.Label>
              <Form.Control
                type="text"
                name="name"
                value={formik.values.name}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                isInvalid={formik.touched.name && !!formik.errors.name}
              />
              <Form.Control.Feedback type="invalid">{formik.errors.name}</Form.Control.Feedback>
            </Form.Group>

            <Form.Group controlId="email" className="mb-3">
              <Form.Label>البريد الإلكتروني</Form.Label>
              <Form.Control
                type="email"
                name="email"
                value={formik.values.email}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                isInvalid={formik.touched.email && !!formik.errors.email}
              />
              <Form.Control.Feedback type="invalid">{formik.errors.email}</Form.Control.Feedback>
            </Form.Group>

            <Form.Group controlId="password" className="mb-3">
              <Form.Label>كلمة المرور</Form.Label>
              <Form.Control
                type="password"
                name="password"
                value={formik.values.password}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                isInvalid={formik.touched.password && !!formik.errors.password}
              />
              <Form.Control.Feedback type="invalid">{formik.errors.password}</Form.Control.Feedback>
            </Form.Group>

            <Form.Group controlId="confirmPassword" className="mb-3">
              <Form.Label>تأكيد كلمة المرور</Form.Label>
              <Form.Control
                type="password"
                name="confirmPassword"
                value={formik.values.confirmPassword}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                isInvalid={formik.touched.confirmPassword && !!formik.errors.confirmPassword}
              />
              <Form.Control.Feedback type="invalid">{formik.errors.confirmPassword}</Form.Control.Feedback>
            </Form.Group>

            <Button type="submit" variant="success" className="w-100" onClick={() => formik.handleSubmit()}>حفظ التغييرات</Button>
          </Form>
        </Modal.Body>
      </Modal>
    </Container>
  );
};

export default Profile;
