'use client';

import { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Form, Button, Alert, Tab, Tabs } from 'react-bootstrap';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import { useRouter } from 'next/navigation';
import { 
  Settings, 
  User, 
  Bell, 
  Shield, 
  Palette,
  Globe,
  Save,
  Eye,
  EyeOff
} from 'lucide-react';
import { toast } from 'react-toastify';

export default function SettingsPage() {
  const { user, updateProfile, isAuthenticated, loading: authLoading } = useAuth();
  const { isDark, toggleTheme } = useTheme();
  const router = useRouter();
  
  const [activeTab, setActiveTab] = useState('profile');
  const [loading, setLoading] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  
  const [profileData, setProfileData] = useState({
    name: '',
    email: '',
    bio: ''
  });
  
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  
  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    courseUpdates: true,
    promotions: false,
    weeklyDigest: true
  });
  
  const [privacySettings, setPrivacySettings] = useState({
    profileVisibility: 'public',
    showProgress: true,
    showAchievements: true
  });

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/login');
      return;
    }

    if (user) {
      setProfileData({
        name: user.name || '',
        email: user.email || '',
        bio: user.bio || ''
      });
      
      setNotificationSettings({
        emailNotifications: user.preferences?.notifications ?? true,
        courseUpdates: user.preferences?.courseUpdates ?? true,
        promotions: user.preferences?.promotions ?? false,
        weeklyDigest: user.preferences?.weeklyDigest ?? true
      });
    }
  }, [user, isAuthenticated, authLoading, router]);

  const handleProfileSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const result = await updateProfile({
        name: profileData.name,
        bio: profileData.bio
      });

      if (result.success) {
        toast.success('تم تحديث الملف الشخصي بنجاح');
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      toast.error('فشل في تحديث الملف الشخصي');
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordSubmit = async (e) => {
    e.preventDefault();
    
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      toast.error('كلمات المرور الجديدة غير متطابقة');
      return;
    }

    if (passwordData.newPassword.length < 6) {
      toast.error('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
      return;
    }

    setLoading(true);

    try {
      // محاكاة تغيير كلمة المرور
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success('تم تغيير كلمة المرور بنجاح');
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
    } catch (error) {
      toast.error('فشل في تغيير كلمة المرور');
    } finally {
      setLoading(false);
    }
  };

  const handleNotificationSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const result = await updateProfile({
        preferences: {
          ...user.preferences,
          ...notificationSettings
        }
      });

      if (result.success) {
        toast.success('تم تحديث إعدادات الإشعارات بنجاح');
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      toast.error('فشل في تحديث إعدادات الإشعارات');
    } finally {
      setLoading(false);
    }
  };

  if (authLoading) {
    return (
      <div className="d-flex justify-content-center align-items-center min-vh-100">
        <div className="spinner-border text-primary" />
      </div>
    );
  }

  return (
    <div className={`min-vh-100 ${isDark ? 'bg-dark text-light' : 'bg-light'}`}>
      <Container className="py-5">
        {/* Header */}
        <Row className="mb-4">
          <Col>
            <div className="d-flex align-items-center mb-3">
              <Settings size={32} className="text-primary me-3" />
              <div>
                <h1 className="display-6 fw-bold mb-0">الإعدادات</h1>
                <p className="text-muted mb-0">إدارة حسابك وتفضيلاتك</p>
              </div>
            </div>
          </Col>
        </Row>

        <Row>
          <Col>
            <Card className={`border-0 shadow-sm ${isDark ? 'bg-dark text-light' : ''}`}>
              <Card.Body>
                <Tabs
                  activeKey={activeTab}
                  onSelect={setActiveTab}
                  className="mb-4"
                >
                  {/* Profile Tab */}
                  <Tab eventKey="profile" title={<><User size={16} className="me-1" />الملف الشخصي</>}>
                    <Form onSubmit={handleProfileSubmit}>
                      <Row>
                        <Col md={6}>
                          <Form.Group className="mb-3">
                            <Form.Label>الاسم الكامل</Form.Label>
                            <Form.Control
                              type="text"
                              value={profileData.name}
                              onChange={(e) => setProfileData(prev => ({
                                ...prev,
                                name: e.target.value
                              }))}
                              required
                            />
                          </Form.Group>
                        </Col>
                        <Col md={6}>
                          <Form.Group className="mb-3">
                            <Form.Label>البريد الإلكتروني</Form.Label>
                            <Form.Control
                              type="email"
                              value={profileData.email}
                              disabled
                              className="bg-light"
                            />
                            <Form.Text className="text-muted">
                              لا يمكن تغيير البريد الإلكتروني
                            </Form.Text>
                          </Form.Group>
                        </Col>
                      </Row>
                      
                      <Form.Group className="mb-4">
                        <Form.Label>نبذة شخصية</Form.Label>
                        <Form.Control
                          as="textarea"
                          rows={3}
                          value={profileData.bio}
                          onChange={(e) => setProfileData(prev => ({
                            ...prev,
                            bio: e.target.value
                          }))}
                          placeholder="اكتب نبذة مختصرة عنك..."
                        />
                      </Form.Group>

                      <Button
                        type="submit"
                        variant="primary"
                        disabled={loading}
                      >
                        {loading ? (
                          <>
                            <div className="spinner-border spinner-border-sm me-2" />
                            جاري الحفظ...
                          </>
                        ) : (
                          <>
                            <Save size={16} className="me-1" />
                            حفظ التغييرات
                          </>
                        )}
                      </Button>
                    </Form>
                  </Tab>

                  {/* Security Tab */}
                  <Tab eventKey="security" title={<><Shield size={16} className="me-1" />الأمان</>}>
                    <Form onSubmit={handlePasswordSubmit}>
                      <h5 className="mb-3">تغيير كلمة المرور</h5>
                      
                      <Row>
                        <Col md={6}>
                          <Form.Group className="mb-3">
                            <Form.Label>كلمة المرور الحالية</Form.Label>
                            <div className="position-relative">
                              <Form.Control
                                type={showCurrentPassword ? 'text' : 'password'}
                                value={passwordData.currentPassword}
                                onChange={(e) => setPasswordData(prev => ({
                                  ...prev,
                                  currentPassword: e.target.value
                                }))}
                                required
                              />
                              <Button
                                variant="link"
                                className="position-absolute top-50 end-0 translate-middle-y p-0 me-3"
                                onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                                type="button"
                              >
                                {showCurrentPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                              </Button>
                            </div>
                          </Form.Group>
                        </Col>
                      </Row>
                      
                      <Row>
                        <Col md={6}>
                          <Form.Group className="mb-3">
                            <Form.Label>كلمة المرور الجديدة</Form.Label>
                            <div className="position-relative">
                              <Form.Control
                                type={showNewPassword ? 'text' : 'password'}
                                value={passwordData.newPassword}
                                onChange={(e) => setPasswordData(prev => ({
                                  ...prev,
                                  newPassword: e.target.value
                                }))}
                                required
                                minLength={6}
                              />
                              <Button
                                variant="link"
                                className="position-absolute top-50 end-0 translate-middle-y p-0 me-3"
                                onClick={() => setShowNewPassword(!showNewPassword)}
                                type="button"
                              >
                                {showNewPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                              </Button>
                            </div>
                          </Form.Group>
                        </Col>
                        <Col md={6}>
                          <Form.Group className="mb-3">
                            <Form.Label>تأكيد كلمة المرور الجديدة</Form.Label>
                            <Form.Control
                              type="password"
                              value={passwordData.confirmPassword}
                              onChange={(e) => setPasswordData(prev => ({
                                ...prev,
                                confirmPassword: e.target.value
                              }))}
                              required
                            />
                          </Form.Group>
                        </Col>
                      </Row>

                      <Button
                        type="submit"
                        variant="warning"
                        disabled={loading}
                      >
                        {loading ? (
                          <>
                            <div className="spinner-border spinner-border-sm me-2" />
                            جاري التحديث...
                          </>
                        ) : (
                          <>
                            <Shield size={16} className="me-1" />
                            تغيير كلمة المرور
                          </>
                        )}
                      </Button>
                    </Form>
                  </Tab>

                  {/* Notifications Tab */}
                  <Tab eventKey="notifications" title={<><Bell size={16} className="me-1" />الإشعارات</>}>
                    <Form onSubmit={handleNotificationSubmit}>
                      <h5 className="mb-3">إعدادات الإشعارات</h5>
                      
                      <Form.Group className="mb-3">
                        <Form.Check
                          type="checkbox"
                          label="إشعارات البريد الإلكتروني"
                          checked={notificationSettings.emailNotifications}
                          onChange={(e) => setNotificationSettings(prev => ({
                            ...prev,
                            emailNotifications: e.target.checked
                          }))}
                        />
                        <Form.Text className="text-muted">
                          تلقي إشعارات عامة عبر البريد الإلكتروني
                        </Form.Text>
                      </Form.Group>

                      <Form.Group className="mb-3">
                        <Form.Check
                          type="checkbox"
                          label="تحديثات الدورات"
                          checked={notificationSettings.courseUpdates}
                          onChange={(e) => setNotificationSettings(prev => ({
                            ...prev,
                            courseUpdates: e.target.checked
                          }))}
                        />
                        <Form.Text className="text-muted">
                          إشعارات عند إضافة دروس جديدة أو تحديثات في الدورات المسجل بها
                        </Form.Text>
                      </Form.Group>

                      <Form.Group className="mb-3">
                        <Form.Check
                          type="checkbox"
                          label="العروض والخصومات"
                          checked={notificationSettings.promotions}
                          onChange={(e) => setNotificationSettings(prev => ({
                            ...prev,
                            promotions: e.target.checked
                          }))}
                        />
                        <Form.Text className="text-muted">
                          تلقي إشعارات حول العروض الخاصة والخصومات
                        </Form.Text>
                      </Form.Group>

                      <Form.Group className="mb-4">
                        <Form.Check
                          type="checkbox"
                          label="الملخص الأسبوعي"
                          checked={notificationSettings.weeklyDigest}
                          onChange={(e) => setNotificationSettings(prev => ({
                            ...prev,
                            weeklyDigest: e.target.checked
                          }))}
                        />
                        <Form.Text className="text-muted">
                          ملخص أسبوعي بتقدمك وأحدث الدورات
                        </Form.Text>
                      </Form.Group>

                      <Button
                        type="submit"
                        variant="primary"
                        disabled={loading}
                      >
                        {loading ? (
                          <>
                            <div className="spinner-border spinner-border-sm me-2" />
                            جاري الحفظ...
                          </>
                        ) : (
                          <>
                            <Save size={16} className="me-1" />
                            حفظ الإعدادات
                          </>
                        )}
                      </Button>
                    </Form>
                  </Tab>

                  {/* Appearance Tab */}
                  <Tab eventKey="appearance" title={<><Palette size={16} className="me-1" />المظهر</>}>
                    <h5 className="mb-3">إعدادات المظهر</h5>
                    
                    <Form.Group className="mb-4">
                      <Form.Label>المظهر</Form.Label>
                      <div className="d-flex gap-3">
                        <Button
                          variant={!isDark ? 'primary' : 'outline-primary'}
                          onClick={() => !isDark || toggleTheme()}
                        >
                          ☀️ فاتح
                        </Button>
                        <Button
                          variant={isDark ? 'primary' : 'outline-primary'}
                          onClick={() => isDark || toggleTheme()}
                        >
                          🌙 مظلم
                        </Button>
                      </div>
                      <Form.Text className="text-muted">
                        اختر المظهر المفضل لديك
                      </Form.Text>
                    </Form.Group>

                    <Form.Group className="mb-4">
                      <Form.Label>اللغة</Form.Label>
                      <Form.Select defaultValue="ar">
                        <option value="ar">العربية</option>
                        <option value="en">English</option>
                      </Form.Select>
                      <Form.Text className="text-muted">
                        لغة واجهة المستخدم
                      </Form.Text>
                    </Form.Group>
                  </Tab>
                </Tabs>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>
    </div>
  );
}
