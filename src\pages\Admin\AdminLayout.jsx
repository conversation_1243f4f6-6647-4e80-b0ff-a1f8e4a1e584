import React from 'react';
import { Container, Row, Col, Nav } from 'react-bootstrap';
import { Link, useLocation } from 'react-router-dom';
import { BarChart3, BookOpen, Users, Plus, Home, Shield, Edit } from 'lucide-react';
import { useAuth } from '../../context/AuthContext';

const AdminLayout = ({ children }) => {
  const location = useLocation();
  const { user, isSuperAdmin } = useAuth();

  const navItems = [
    { path: '/admin', label: 'لوحة التحكم', icon: BarChart3, exact: true },
    { path: '/admin/courses', label: 'إدارة الدورات', icon: BookOpen },
    { path: '/admin/users', label: 'إدارة المستخدمين', icon: Users, superAdminOnly: true },
    { path: '/admin/add-course', label: 'إضافة دورة', icon: Plus },
    { path: '/admin/permissions', label: 'الصلاحيات', icon: Shield },
    { path: '/admin/edit-course', label: 'تحرير دورة', icon: Edit, superAdminOnly: true },


  ];

  return (
    <Container fluid className="py-4" dir="rtl">
      <Row>
        <Col md={3} lg={2} className="mb-4">
          <div className="bg-light rounded p-3 sticky-top" style={{ top: '100px' }}>
            <h5 className="fw-bold mb-3 text-primary">لوحة الإدارة</h5>
            <Nav className="flex-column">
              <Nav.Link as={Link} to="/" className="d-flex align-items-center mb-2 text-muted">
                <Home size={16} className="me-2" />
                العودة للموقع
              </Nav.Link>
              <hr />

              {/* User Role Badge */}
              <div className="mb-3 p-2 bg-light rounded text-center">
                <div className="d-flex align-items-center justify-content-center">
                  <Shield size={16} className="me-2 text-primary" />
                  <span className="fw-bold text-primary">
                    {isSuperAdmin ? 'مدير عام' : 'مدير'}
                  </span>
                </div>
                <small className="text-muted">{user?.name}</small>
              </div>

              {navItems.map((item) => {
                // Hide super admin only items for regular admins
                if (item.superAdminOnly && !isSuperAdmin) {
                  return null;
                }

                const isActive = item.exact
                  ? location.pathname === item.path
                  : location.pathname.startsWith(item.path) && item.path !== '/admin';

                return (
                  <Nav.Link
                    key={item.path}
                    as={Link}
                    to={item.path}
                    className={`d-flex align-items-center mb-2 ${isActive ? 'bg-primary text-white rounded' : 'text-muted'}`}
                  >
                    <item.icon size={16} className="me-2" />
                    {item.label}
                  </Nav.Link>
                );
              })}
            </Nav>
          </div>
        </Col>
        <Col md={9} lg={10}>
          {children}
        </Col>
      </Row>
    </Container>
  );
};

export default AdminLayout;