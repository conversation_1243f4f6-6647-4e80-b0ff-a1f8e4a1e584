import mongoose from 'mongoose';

const quizAttemptSchema = new mongoose.Schema({
  quiz: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Quiz',
    required: true
  },
  student: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  enrollment: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Enrollment',
    required: true
  },
  
  // معلومات المحاولة
  attemptNumber: {
    type: Number,
    required: true,
    default: 1
  },
  
  // الإجابات
  answers: [{
    question: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Question',
      required: true
    },
    answer: mongoose.Schema.Types.Mixed, // يمكن أن تكون نص أو معرف خيار
    isCorrect: Boolean,
    points: {
      type: Number,
      default: 0
    },
    timeSpent: Number, // الوقت المستغرق في السؤال (بالثواني)
    flagged: {
      type: Boolean,
      default: false // هل تم وضع علامة على السؤال للمراجعة
    }
  }],
  
  // النتائج
  score: {
    type: Number,
    default: 0
  },
  totalPoints: {
    type: Number,
    default: 0
  },
  percentage: {
    type: Number,
    default: 0
  },
  passed: {
    type: Boolean,
    default: false
  },
  
  // التوقيت
  startedAt: {
    type: Date,
    default: Date.now
  },
  submittedAt: Date,
  timeSpent: Number, // إجمالي الوقت المستغرق (بالثواني)
  
  // الحالة
  status: {
    type: String,
    enum: ['in_progress', 'submitted', 'graded', 'expired'],
    default: 'in_progress'
  },
  
  // التقييم اليدوي (للأسئلة المقالية)
  manualGrading: {
    gradedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    gradedAt: Date,
    feedback: String,
    manualPoints: {
      type: Number,
      default: 0
    }
  },
  
  // ملاحظات المدرس
  instructorNotes: String,
  
  // بيانات إضافية
  metadata: {
    userAgent: String,
    ipAddress: String,
    browserInfo: String
  }
}, {
  timestamps: true
});

// فهارس
quizAttemptSchema.index({ quiz: 1, student: 1, attemptNumber: 1 }, { unique: true });
quizAttemptSchema.index({ student: 1, status: 1 });
quizAttemptSchema.index({ quiz: 1, status: 1 });
quizAttemptSchema.index({ submittedAt: -1 });

// دالة لحساب النتيجة
quizAttemptSchema.methods.calculateScore = function() {
  let totalScore = 0;
  let totalPoints = 0;
  
  this.answers.forEach(answer => {
    totalScore += answer.points || 0;
    // نحتاج لجلب السؤال لمعرفة النقاط الكاملة
  });
  
  // إضافة النقاط اليدوية
  totalScore += this.manualGrading?.manualPoints || 0;
  
  this.score = totalScore;
  this.percentage = totalPoints > 0 ? Math.round((totalScore / totalPoints) * 100) : 0;
  
  return this.save();
};

// دالة لتحديد النجاح/الرسوب
quizAttemptSchema.methods.checkPassed = async function() {
  await this.populate('quiz');
  this.passed = this.percentage >= this.quiz.settings.passingScore;
  return this.save();
};

// دالة لإنهاء المحاولة
quizAttemptSchema.methods.submit = function() {
  this.status = 'submitted';
  this.submittedAt = new Date();
  this.timeSpent = Math.floor((this.submittedAt - this.startedAt) / 1000);
  return this.save();
};

// دالة للتحقق من انتهاء الوقت
quizAttemptSchema.methods.isExpired = function(quiz) {
  if (!quiz.settings.timeLimit) return false;
  
  const timeLimit = quiz.settings.timeLimit * 60 * 1000; // تحويل إلى ميلي ثانية
  const elapsed = new Date() - this.startedAt;
  
  return elapsed > timeLimit;
};

// دالة لحفظ إجابة سؤال
quizAttemptSchema.methods.saveAnswer = function(questionId, answer) {
  const existingAnswer = this.answers.find(a => a.question.toString() === questionId);
  
  if (existingAnswer) {
    existingAnswer.answer = answer;
    existingAnswer.timeSpent = Date.now(); // يمكن تحسين حساب الوقت
  } else {
    this.answers.push({
      question: questionId,
      answer: answer,
      timeSpent: 0
    });
  }
  
  return this.save();
};

export default mongoose.models.QuizAttempt || mongoose.model('QuizAttempt', quizAttemptSchema);
