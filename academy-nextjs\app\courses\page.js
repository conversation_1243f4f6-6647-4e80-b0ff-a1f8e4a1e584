'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Col, Card, Form, Button, Badge, Spin<PERSON>, <PERSON>ert } from 'react-bootstrap';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import Link from 'next/link';
import { Search, BookOpen, Clock, User, Star, Filter } from 'lucide-react';

export default function Courses() {
  const { api } = useAuth();
  const { isDark } = useTheme();
  
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [search, setSearch] = useState('');
  const [level, setLevel] = useState('');
  const [category, setCategory] = useState('');
  const [pagination, setPagination] = useState({
    current: 1,
    total: 1,
    totalCourses: 0
  });

  useEffect(() => {
    fetchCourses();
  }, [search, level, category, pagination.current]);

  const fetchCourses = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: pagination.current.toString(),
        limit: '12'
      });
      
      if (search) params.append('search', search);
      if (level) params.append('level', level);
      if (category) params.append('category', category);

      const response = await api.get(`/courses?${params}`);
      setCourses(response.data.courses);
      setPagination(response.data.pagination);
    } catch (err) {
      setError('فشل في تحميل الدورات');
      console.error('Error fetching courses:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSearchSubmit = (e) => {
    e.preventDefault();
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchCourses();
  };

  const handleFilterChange = (filterType, value) => {
    if (filterType === 'level') setLevel(value);
    if (filterType === 'category') setCategory(value);
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  const clearFilters = () => {
    setSearch('');
    setLevel('');
    setCategory('');
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  const getLevelBadgeVariant = (courseLevel) => {
    switch (courseLevel) {
      case 'مبتدئ':
      case 'Beginner':
        return 'success';
      case 'متوسط':
      case 'Intermediate':
        return 'warning';
      case 'متقدم':
      case 'Advanced':
        return 'danger';
      default:
        return 'secondary';
    }
  };

  return (
    <div className={`min-vh-100 ${isDark ? 'bg-dark text-light' : 'bg-light'}`}>
      <Container className="py-5">
        {/* Header */}
        <Row className="mb-5">
          <Col>
            <div className="text-center">
              <h1 className="display-5 fw-bold mb-3">الدورات التعليمية</h1>
              <p className="lead text-muted">
                اكتشف مجموعة واسعة من الدورات التقنية المتخصصة
              </p>
            </div>
          </Col>
        </Row>

        {/* Search and Filters */}
        <Row className="mb-4">
          <Col>
            <Card className={`border-0 shadow-sm ${isDark ? 'bg-dark text-light' : ''}`}>
              <Card.Body>
                <Form onSubmit={handleSearchSubmit}>
                  <Row className="g-3 align-items-end">
                    <Col md={4}>
                      <Form.Label>البحث</Form.Label>
                      <div className="position-relative">
                        <Form.Control
                          type="text"
                          placeholder="ابحث عن دورة..."
                          value={search}
                          onChange={(e) => setSearch(e.target.value)}
                          className="pe-5"
                        />
                        <Search 
                          size={18} 
                          className="position-absolute top-50 translate-middle-y text-muted"
                          style={{ right: '12px' }}
                        />
                      </div>
                    </Col>
                    
                    <Col md={3}>
                      <Form.Label>المستوى</Form.Label>
                      <Form.Select
                        value={level}
                        onChange={(e) => handleFilterChange('level', e.target.value)}
                      >
                        <option value="">جميع المستويات</option>
                        <option value="مبتدئ">مبتدئ</option>
                        <option value="متوسط">متوسط</option>
                        <option value="متقدم">متقدم</option>
                      </Form.Select>
                    </Col>
                    
                    <Col md={3}>
                      <Form.Label>التصنيف</Form.Label>
                      <Form.Select
                        value={category}
                        onChange={(e) => handleFilterChange('category', e.target.value)}
                      >
                        <option value="">جميع التصنيفات</option>
                        <option value="البرمجة">البرمجة</option>
                        <option value="التصميم">التصميم</option>
                        <option value="الشبكات">الشبكات</option>
                        <option value="الأمن السيبراني">الأمن السيبراني</option>
                      </Form.Select>
                    </Col>
                    
                    <Col md={2}>
                      <div className="d-flex gap-2">
                        <Button type="submit" variant="primary" className="flex-grow-1">
                          بحث
                        </Button>
                        <Button 
                          type="button" 
                          variant="outline-secondary"
                          onClick={clearFilters}
                        >
                          <Filter size={16} />
                        </Button>
                      </div>
                    </Col>
                  </Row>
                </Form>
              </Card.Body>
            </Card>
          </Col>
        </Row>

        {/* Results Info */}
        {!loading && (
          <Row className="mb-3">
            <Col>
              <p className="text-muted">
                عرض {courses.length} من أصل {pagination.totalCourses} دورة
              </p>
            </Col>
          </Row>
        )}

        {/* Error State */}
        {error && (
          <Row className="mb-4">
            <Col>
              <Alert variant="danger">{error}</Alert>
            </Col>
          </Row>
        )}

        {/* Loading State */}
        {loading && (
          <Row className="justify-content-center">
            <Col xs="auto">
              <div className="text-center py-5">
                <Spinner animation="border" variant="primary" />
                <p className="mt-3 text-muted">جاري تحميل الدورات...</p>
              </div>
            </Col>
          </Row>
        )}

        {/* Courses Grid */}
        {!loading && courses.length > 0 && (
          <Row className="g-4">
            {courses.map((course) => (
              <Col md={6} lg={4} key={course._id}>
                <Card className={`h-100 course-card border-0 shadow-sm ${isDark ? 'bg-dark text-light' : ''}`}>
                  {course.image && (
                    <div className="position-relative">
                      <Card.Img 
                        variant="top" 
                        src={course.image} 
                        alt={course.title}
                        className="course-image"
                      />
                      <Badge 
                        bg={getLevelBadgeVariant(course.level)}
                        className="position-absolute top-0 end-0 m-2"
                      >
                        {course.level}
                      </Badge>
                    </div>
                  )}
                  
                  <Card.Body className="d-flex flex-column">
                    <div className="mb-2">
                      <Badge bg="primary" className="me-2">{course.category}</Badge>
                      {course.price === 0 && (
                        <Badge bg="success">مجاني</Badge>
                      )}
                    </div>
                    
                    <Card.Title className="h5 mb-3">{course.title}</Card.Title>
                    
                    <Card.Text className="text-muted flex-grow-1">
                      {course.description.length > 100 
                        ? `${course.description.substring(0, 100)}...`
                        : course.description
                      }
                    </Card.Text>
                    
                    <div className="mt-auto">
                      <div className="d-flex justify-content-between align-items-center mb-3">
                        <small className="text-muted d-flex align-items-center">
                          <User size={14} className="me-1" />
                          {course.instructor}
                        </small>
                        <small className="text-muted d-flex align-items-center">
                          <Clock size={14} className="me-1" />
                          {course.duration}
                        </small>
                      </div>
                      
                      <div className="d-flex justify-content-between align-items-center">
                        <div className="d-flex align-items-center">
                          <Star size={14} className="text-warning me-1" />
                          <small>{course.rating || 0}</small>
                          <small className="text-muted ms-1">
                            ({course.ratingCount || 0})
                          </small>
                        </div>
                        
                        <Link href={`/courses/${course._id}`}>
                          <Button variant="primary" size="sm">
                            <BookOpen size={14} className="me-1" />
                            عرض الدورة
                          </Button>
                        </Link>
                      </div>
                    </div>
                  </Card.Body>
                </Card>
              </Col>
            ))}
          </Row>
        )}

        {/* Empty State */}
        {!loading && courses.length === 0 && !error && (
          <Row className="justify-content-center">
            <Col md={6} className="text-center py-5">
              <BookOpen size={64} className="text-muted mb-3" />
              <h4>لا توجد دورات</h4>
              <p className="text-muted">
                لم يتم العثور على دورات تطابق معايير البحث الخاصة بك
              </p>
              <Button variant="outline-primary" onClick={clearFilters}>
                إزالة الفلاتر
              </Button>
            </Col>
          </Row>
        )}

        {/* Pagination */}
        {!loading && pagination.total > 1 && (
          <Row className="mt-5">
            <Col className="d-flex justify-content-center">
              <div className="d-flex gap-2">
                <Button
                  variant="outline-primary"
                  disabled={pagination.current === 1}
                  onClick={() => setPagination(prev => ({ ...prev, current: prev.current - 1 }))}
                >
                  السابق
                </Button>
                
                <span className="d-flex align-items-center px-3">
                  صفحة {pagination.current} من {pagination.total}
                </span>
                
                <Button
                  variant="outline-primary"
                  disabled={pagination.current === pagination.total}
                  onClick={() => setPagination(prev => ({ ...prev, current: prev.current + 1 }))}
                >
                  التالي
                </Button>
              </div>
            </Col>
          </Row>
        )}
      </Container>
    </div>
  );
}
