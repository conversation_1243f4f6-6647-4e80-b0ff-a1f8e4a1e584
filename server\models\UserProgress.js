import mongoose from 'mongoose';

const userProgressSchema = new mongoose.Schema({
  userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  courseId: { type: mongoose.Schema.Types.ObjectId, ref: 'Course', required: true },
  currentUnitIndex: { type: Number, default: 0 },
  completedLessons: [{ type: String }], // يمكن استخدام ID الدرس أو عنوانه
  passedQuizzes: [{ type: Number }], // رقم الوحدة التي اجتاز اختبارها
  progressPercentage: { type: Number, default: 0 },
  updatedAt: { type: Date, default: Date.now }
}, { timestamps: true });

export default mongoose.model('UserProgress', userProgressSchema); 