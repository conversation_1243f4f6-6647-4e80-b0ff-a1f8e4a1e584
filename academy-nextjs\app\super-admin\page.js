'use client';

import { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Badge, Button, Form, Modal, Alert } from 'react-bootstrap';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { 
  Shield, 
  Users, 
  BookOpen, 
  TrendingUp, 
  Plus,
  Edit,
  Trash2,
  Eye,
  UserCheck,
  UserX,
  Settings,
  BarChart3,
  Database,
  Activity,
  AlertTriangle,
  CheckCircle,
  Crown
} from 'lucide-react';
import { toast } from 'react-toastify';

export default function SuperAdminDashboard() {
  const { user, isSuperAdmin, api } = useAuth();
  const { isDark } = useTheme();
  const router = useRouter();
  
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalAdmins: 0,
    totalCourses: 0,
    systemHealth: 'excellent',
    serverUptime: '99.9%',
    totalRevenue: 0
  });
  const [systemAlerts, setSystemAlerts] = useState([]);
  const [recentActivities, setRecentActivities] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!isSuperAdmin) {
      router.push('/');
      return;
    }
    
    fetchSuperAdminData();
  }, [isSuperAdmin, router]);

  const fetchSuperAdminData = async () => {
    try {
      setLoading(true);
      
      // محاكاة بيانات المدير العام
      setStats({
        totalUsers: 1250,
        totalAdmins: 8,
        totalCourses: 45,
        systemHealth: 'excellent',
        serverUptime: '99.9%',
        totalRevenue: 125000
      });

      // محاكاة تنبيهات النظام
      setSystemAlerts([
        {
          id: 1,
          type: 'warning',
          message: 'استخدام الخادم وصل إلى 85%',
          timestamp: new Date(),
          resolved: false
        },
        {
          id: 2,
          type: 'info',
          message: 'تم تحديث النظام بنجاح',
          timestamp: new Date(Date.now() - 3600000),
          resolved: true
        },
        {
          id: 3,
          type: 'success',
          message: 'تم إنشاء نسخة احتياطية',
          timestamp: new Date(Date.now() - 7200000),
          resolved: true
        }
      ]);

      // محاكاة الأنشطة الأخيرة
      setRecentActivities([
        {
          id: 1,
          user: 'أحمد المدير',
          action: 'أضاف دورة جديدة',
          target: 'تطوير تطبيقات React',
          timestamp: new Date(),
          type: 'course'
        },
        {
          id: 2,
          user: 'فاطمة الإدارية',
          action: 'عدلت بيانات مستخدم',
          target: 'محمد علي',
          timestamp: new Date(Date.now() - 1800000),
          type: 'user'
        },
        {
          id: 3,
          user: 'سالم المشرف',
          action: 'حذف دورة',
          target: 'دورة قديمة',
          timestamp: new Date(Date.now() - 3600000),
          type: 'course'
        }
      ]);

    } catch (error) {
      console.error('Error fetching super admin data:', error);
      toast.error('فشل في تحميل بيانات لوحة التحكم');
    } finally {
      setLoading(false);
    }
  };

  const getHealthBadge = (health) => {
    switch (health) {
      case 'excellent':
        return <Badge bg="success">ممتاز</Badge>;
      case 'good':
        return <Badge bg="primary">جيد</Badge>;
      case 'warning':
        return <Badge bg="warning">تحذير</Badge>;
      case 'critical':
        return <Badge bg="danger">حرج</Badge>;
      default:
        return <Badge bg="secondary">غير معروف</Badge>;
    }
  };

  const getAlertIcon = (type) => {
    switch (type) {
      case 'warning':
        return <AlertTriangle size={16} className="text-warning" />;
      case 'error':
        return <AlertTriangle size={16} className="text-danger" />;
      case 'success':
        return <CheckCircle size={16} className="text-success" />;
      default:
        return <Activity size={16} className="text-info" />;
    }
  };

  const getActivityIcon = (type) => {
    switch (type) {
      case 'course':
        return <BookOpen size={16} className="text-primary" />;
      case 'user':
        return <Users size={16} className="text-success" />;
      case 'system':
        return <Settings size={16} className="text-warning" />;
      default:
        return <Activity size={16} className="text-info" />;
    }
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center min-vh-100">
        <div className="spinner-border text-primary" />
      </div>
    );
  }

  return (
    <div className={`min-vh-100 ${isDark ? 'bg-dark text-light' : 'bg-light'}`}>
      <Container className="py-5">
        {/* Header */}
        <Row className="mb-4">
          <Col>
            <div className="d-flex justify-content-between align-items-center">
              <div>
                <div className="d-flex align-items-center mb-2">
                  <Crown size={32} className="text-warning me-2" />
                  <h1 className="display-6 fw-bold mb-0">لوحة المدير العام</h1>
                </div>
                <p className="text-muted">مرحباً {user?.name}، إليك نظرة شاملة على النظام</p>
              </div>
              <div className="d-flex gap-2">
                <Link href="/super-admin/add-course">
                  <Button variant="primary">
                    <Plus size={16} className="me-1" />
                    إضافة دورة
                  </Button>
                </Link>
                <Link href="/super-admin/settings">
                  <Button variant="outline-secondary">
                    <Settings size={16} className="me-1" />
                    الإعدادات
                  </Button>
                </Link>
              </div>
            </div>
          </Col>
        </Row>

        {/* System Health Alert */}
        <Row className="mb-4">
          <Col>
            <Alert variant="success" className="d-flex align-items-center">
              <CheckCircle size={20} className="me-2" />
              <strong>حالة النظام: </strong>
              <span className="ms-2">جميع الأنظمة تعمل بشكل طبيعي</span>
              {getHealthBadge(stats.systemHealth)}
            </Alert>
          </Col>
        </Row>

        {/* Stats Cards */}
        <Row className="mb-5">
          <Col md={3}>
            <Card className={`text-center border-0 shadow-sm h-100 ${isDark ? 'bg-dark text-light' : ''}`}>
              <Card.Body>
                <Users size={32} className="text-primary mb-2" />
                <h3 className="fw-bold">{stats.totalUsers.toLocaleString()}</h3>
                <p className="text-muted mb-1">إجمالي المستخدمين</p>
                <small className="text-success">
                  <TrendingUp size={12} className="me-1" />
                  +15% هذا الشهر
                </small>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3}>
            <Card className={`text-center border-0 shadow-sm h-100 ${isDark ? 'bg-dark text-light' : ''}`}>
              <Card.Body>
                <Shield size={32} className="text-warning mb-2" />
                <h3 className="fw-bold">{stats.totalAdmins}</h3>
                <p className="text-muted mb-1">المدراء</p>
                <small className="text-info">
                  نشطين
                </small>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3}>
            <Card className={`text-center border-0 shadow-sm h-100 ${isDark ? 'bg-dark text-light' : ''}`}>
              <Card.Body>
                <BookOpen size={32} className="text-success mb-2" />
                <h3 className="fw-bold">{stats.totalCourses}</h3>
                <p className="text-muted mb-1">إجمالي الدورات</p>
                <small className="text-success">
                  <TrendingUp size={12} className="me-1" />
                  +3 هذا الأسبوع
                </small>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3}>
            <Card className={`text-center border-0 shadow-sm h-100 ${isDark ? 'bg-dark text-light' : ''}`}>
              <Card.Body>
                <BarChart3 size={32} className="text-info mb-2" />
                <h3 className="fw-bold">{stats.totalRevenue.toLocaleString()} ر.س</h3>
                <p className="text-muted mb-1">إجمالي الإيرادات</p>
                <small className="text-success">
                  <TrendingUp size={12} className="me-1" />
                  +22% هذا الشهر
                </small>
              </Card.Body>
            </Card>
          </Col>
        </Row>

        {/* System Status */}
        <Row className="mb-4">
          <Col md={6}>
            <Card className={`border-0 shadow-sm h-100 ${isDark ? 'bg-dark text-light' : ''}`}>
              <Card.Header className="bg-transparent border-0">
                <h5 className="mb-0">
                  <Database size={20} className="me-2" />
                  حالة النظام
                </h5>
              </Card.Header>
              <Card.Body>
                <Row className="text-center">
                  <Col>
                    <div className="mb-3">
                      <div className="bg-success rounded-circle mx-auto mb-2 d-flex align-items-center justify-content-center" 
                           style={{ width: '50px', height: '50px' }}>
                        <Activity size={24} className="text-white" />
                      </div>
                      <h6 className="fw-bold">وقت التشغيل</h6>
                      <p className="text-success mb-0">{stats.serverUptime}</p>
                    </div>
                  </Col>
                  <Col>
                    <div className="mb-3">
                      <div className="bg-primary rounded-circle mx-auto mb-2 d-flex align-items-center justify-content-center" 
                           style={{ width: '50px', height: '50px' }}>
                        <Database size={24} className="text-white" />
                      </div>
                      <h6 className="fw-bold">قاعدة البيانات</h6>
                      <p className="text-success mb-0">متصلة</p>
                    </div>
                  </Col>
                  <Col>
                    <div className="mb-3">
                      <div className="bg-warning rounded-circle mx-auto mb-2 d-flex align-items-center justify-content-center" 
                           style={{ width: '50px', height: '50px' }}>
                        <Shield size={24} className="text-white" />
                      </div>
                      <h6 className="fw-bold">الأمان</h6>
                      <p className="text-success mb-0">محمي</p>
                    </div>
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          </Col>

          <Col md={6}>
            <Card className={`border-0 shadow-sm h-100 ${isDark ? 'bg-dark text-light' : ''}`}>
              <Card.Header className="bg-transparent border-0">
                <h5 className="mb-0">
                  <AlertTriangle size={20} className="me-2" />
                  تنبيهات النظام
                </h5>
              </Card.Header>
              <Card.Body>
                {systemAlerts.length > 0 ? (
                  <div style={{ maxHeight: '200px', overflowY: 'auto' }}>
                    {systemAlerts.map((alert) => (
                      <div key={alert.id} className="d-flex align-items-center mb-2 p-2 rounded bg-light">
                        {getAlertIcon(alert.type)}
                        <div className="ms-2 flex-grow-1">
                          <small className="d-block">{alert.message}</small>
                          <small className="text-muted">
                            {alert.timestamp.toLocaleTimeString('ar-SA')}
                          </small>
                        </div>
                        {alert.resolved && (
                          <CheckCircle size={14} className="text-success" />
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-muted text-center">لا توجد تنبيهات</p>
                )}
              </Card.Body>
            </Card>
          </Col>
        </Row>

        <Row>
          {/* Recent Activities */}
          <Col md={8}>
            <Card className={`border-0 shadow-sm h-100 ${isDark ? 'bg-dark text-light' : ''}`}>
              <Card.Header className="bg-transparent border-0 d-flex justify-content-between align-items-center">
                <h5 className="mb-0">الأنشطة الأخيرة</h5>
                <Link href="/super-admin/activities">
                  <Button variant="outline-primary" size="sm">
                    عرض الكل
                  </Button>
                </Link>
              </Card.Header>
              <Card.Body>
                <div className="table-responsive">
                  <Table hover className={isDark ? 'table-dark' : ''}>
                    <thead>
                      <tr>
                        <th>النشاط</th>
                        <th>المستخدم</th>
                        <th>الهدف</th>
                        <th>الوقت</th>
                      </tr>
                    </thead>
                    <tbody>
                      {recentActivities.map((activity) => (
                        <tr key={activity.id}>
                          <td>
                            <div className="d-flex align-items-center">
                              {getActivityIcon(activity.type)}
                              <span className="ms-2">{activity.action}</span>
                            </div>
                          </td>
                          <td>{activity.user}</td>
                          <td>{activity.target}</td>
                          <td>
                            <small className="text-muted">
                              {activity.timestamp.toLocaleTimeString('ar-SA')}
                            </small>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </Table>
                </div>
              </Card.Body>
            </Card>
          </Col>

          {/* Quick Actions */}
          <Col md={4}>
            <Card className={`border-0 shadow-sm h-100 ${isDark ? 'bg-dark text-light' : ''}`}>
              <Card.Header className="bg-transparent border-0">
                <h5 className="mb-0">الإجراءات السريعة</h5>
              </Card.Header>
              <Card.Body>
                <div className="d-grid gap-2">
                  <Link href="/super-admin/users">
                    <Button variant="outline-primary" className="w-100 text-start">
                      <Users size={16} className="me-2" />
                      إدارة المستخدمين
                    </Button>
                  </Link>
                  <Link href="/super-admin/courses">
                    <Button variant="outline-success" className="w-100 text-start">
                      <BookOpen size={16} className="me-2" />
                      إدارة الدورات
                    </Button>
                  </Link>
                  <Link href="/super-admin/reports">
                    <Button variant="outline-warning" className="w-100 text-start">
                      <BarChart3 size={16} className="me-2" />
                      التقارير المتقدمة
                    </Button>
                  </Link>
                  <Link href="/super-admin/settings">
                    <Button variant="outline-info" className="w-100 text-start">
                      <Settings size={16} className="me-2" />
                      إعدادات النظام
                    </Button>
                  </Link>
                  <Link href="/super-admin/backup">
                    <Button variant="outline-secondary" className="w-100 text-start">
                      <Database size={16} className="me-2" />
                      النسخ الاحتياطي
                    </Button>
                  </Link>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>
    </div>
  );
}
