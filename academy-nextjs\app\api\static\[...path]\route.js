import { NextResponse } from 'next/server';
import { readFile } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';

export async function GET(request, { params }) {
  try {
    const { path } = params;
    const filePath = join(process.cwd(), 'uploads', ...path);
    
    // التحقق من وجود الملف
    if (!existsSync(filePath)) {
      return NextResponse.json(
        { message: 'الملف غير موجود' },
        { status: 404 }
      );
    }

    // قراءة الملف
    const fileBuffer = await readFile(filePath);
    
    // تحديد نوع المحتوى بناءً على امتداد الملف
    const extension = path[path.length - 1].split('.').pop().toLowerCase();
    let contentType = 'application/octet-stream';
    
    const mimeTypes = {
      // Images
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'webp': 'image/webp',
      'avif': 'image/avif',
      'svg': 'image/svg+xml',
      
      // Videos
      'mp4': 'video/mp4',
      'webm': 'video/webm',
      'ogg': 'video/ogg',
      'avi': 'video/x-msvideo',
      'mov': 'video/quicktime',
      
      // Documents
      'pdf': 'application/pdf',
      'doc': 'application/msword',
      'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'txt': 'text/plain',
    };
    
    if (mimeTypes[extension]) {
      contentType = mimeTypes[extension];
    }

    // إرجاع الملف مع headers مناسبة
    return new NextResponse(fileBuffer, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Cache-Control': 'public, max-age=31536000, immutable', // تخزين مؤقت لسنة
      },
    });

  } catch (error) {
    console.error('Error serving static file:', error);
    return NextResponse.json(
      { message: 'خطأ في تحميل الملف' },
      { status: 500 }
    );
  }
}
