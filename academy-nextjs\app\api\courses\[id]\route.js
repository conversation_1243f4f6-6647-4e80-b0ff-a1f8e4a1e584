import { NextResponse } from 'next/server';
import { withAuth } from '../../../../lib/auth';
import connectDB from '../../../../lib/mongodb';
import Course from '../../../../models/Course';

// GET - جلب دورة محددة
export async function GET(request, { params }) {
  try {
    await connectDB();
    
    const { id } = params;
    
    const course = await Course.findById(id)
      .populate('createdBy', 'name')
      .populate('updatedBy', 'name');
    
    if (!course) {
      return NextResponse.json(
        { message: 'الدورة غير موجودة' },
        { status: 404 }
      );
    }

    // التحقق من أن الدورة نشطة (إلا إذا كان المستخدم مدير)
    const authHeader = request.headers.get('authorization');
    let isAdmin = false;
    
    if (authHeader) {
      // محاولة التحقق من المستخدم
      try {
        const { authenticateUser } = await import('../../../../lib/auth');
        const authResult = await authenticateUser(request);
        if (authResult.user && ['admin', 'super-admin'].includes(authResult.user.role)) {
          isAdmin = true;
        }
      } catch (error) {
        // تجاهل أخطاء المصادقة للدورات العامة
      }
    }

    if (!course.isActive && !isAdmin) {
      return NextResponse.json(
        { message: 'الدورة غير متاحة حالياً' },
        { status: 404 }
      );
    }

    // زيادة عدد المشاهدات
    course.views += 1;
    await course.save();

    return NextResponse.json({
      course
    }, { status: 200 });

  } catch (error) {
    console.error('Course fetch error:', error);
    return NextResponse.json(
      { message: 'خطأ في جلب الدورة' },
      { status: 500 }
    );
  }
}

// PUT - تحديث دورة (للمدراء فقط)
export const PUT = withAuth(async (request, { params }) => {
  try {
    await connectDB();
    
    const { id } = params;
    const data = await request.json();
    
    const course = await Course.findById(id);
    if (!course) {
      return NextResponse.json(
        { message: 'الدورة غير موجودة' },
        { status: 404 }
      );
    }

    // تحديث الحقول المسموح بها
    const allowedFields = [
      'title', 'description', 'instructor', 'duration',
      'level', 'category', 'tags', 'price', 'units', 'isActive',
      'isFeatured', 'image', 'video', 'prerequisites', 'learningOutcomes', 'language'
    ];

    allowedFields.forEach(field => {
      if (data[field] !== undefined) {
        course[field] = data[field];
      }
    });

    course.updatedBy = request.user._id;
    await course.save();

    const updatedCourse = await Course.findById(id)
      .populate('createdBy', 'name')
      .populate('updatedBy', 'name');

    return NextResponse.json({
      message: 'تم تحديث الدورة بنجاح',
      course: updatedCourse
    }, { status: 200 });

  } catch (error) {
    console.error('Course update error:', error);
    return NextResponse.json(
      { message: 'خطأ في تحديث الدورة' },
      { status: 500 }
    );
  }
}, { roles: ['admin', 'super-admin'] });

// DELETE - حذف دورة (للمدراء فقط)
export const DELETE = withAuth(async (request, { params }) => {
  try {
    await connectDB();
    
    const { id } = params;
    
    const course = await Course.findById(id);
    if (!course) {
      return NextResponse.json(
        { message: 'الدورة غير موجودة' },
        { status: 404 }
      );
    }

    await Course.findByIdAndDelete(id);

    return NextResponse.json({
      message: 'تم حذف الدورة بنجاح'
    }, { status: 200 });

  } catch (error) {
    console.error('Course deletion error:', error);
    return NextResponse.json(
      { message: 'خطأ في حذف الدورة' },
      { status: 500 }
    );
  }
}, { roles: ['admin', 'super-admin'] });
