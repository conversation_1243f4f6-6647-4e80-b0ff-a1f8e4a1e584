import { NextResponse } from 'next/server';
import { withAuth } from '../../../lib/auth';
import connectDB from '../../../lib/mongodb';
import Enrollment from '../../../models/Enrollment';
import Course from '../../../models/Course';
import User from '../../../models/User';

// GET - جلب تسجيلات المستخدم
export const GET = withAuth(async (request) => {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId') || request.user._id;
    const status = searchParams.get('status'); // 'completed', 'in-progress', 'all'
    
    // بناء الاستعلام
    const query = { student: userId };
    
    if (status === 'completed') {
      query.isCompleted = true;
    } else if (status === 'in-progress') {
      query.isCompleted = false;
    }
    
    const enrollments = await Enrollment.find(query)
      .populate('course', 'title instructor level category duration image rating ratingCount')
      .sort({ enrolledAt: -1 })
      .lean();
    
    // حساب الإحصائيات
    const stats = {
      total: enrollments.length,
      completed: enrollments.filter(e => e.isCompleted).length,
      inProgress: enrollments.filter(e => !e.isCompleted).length,
      totalStudyHours: Math.round(
        enrollments.reduce((sum, e) => sum + (e.studyTime?.totalMinutes || 0), 0) / 60
      ),
      averageProgress: enrollments.length > 0 
        ? Math.round(enrollments.reduce((sum, e) => sum + e.progress, 0) / enrollments.length)
        : 0
    };

    return NextResponse.json({
      enrollments,
      stats
    }, { status: 200 });

  } catch (error) {
    console.error('Enrollments fetch error:', error);
    return NextResponse.json(
      { message: 'خطأ في جلب التسجيلات' },
      { status: 500 }
    );
  }
});

// POST - التسجيل في دورة جديدة
export const POST = withAuth(async (request) => {
  try {
    await connectDB();
    
    const { courseId } = await request.json();
    const userId = request.user._id;

    if (!courseId) {
      return NextResponse.json(
        { message: 'معرف الدورة مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من وجود الدورة
    const course = await Course.findById(courseId);
    if (!course) {
      return NextResponse.json(
        { message: 'الدورة غير موجودة' },
        { status: 404 }
      );
    }

    if (!course.isActive) {
      return NextResponse.json(
        { message: 'الدورة غير متاحة حالياً' },
        { status: 400 }
      );
    }

    // التحقق من عدم التسجيل المسبق
    const existingEnrollment = await Enrollment.findOne({
      student: userId,
      course: courseId
    });

    if (existingEnrollment) {
      return NextResponse.json(
        { message: 'أنت مسجل في هذه الدورة بالفعل' },
        { status: 400 }
      );
    }

    // إنشاء التسجيل الجديد
    const enrollment = new Enrollment({
      student: userId,
      course: courseId
    });

    await enrollment.save();

    // تحديث عدد المسجلين في الدورة
    await Course.findByIdAndUpdate(courseId, {
      $inc: { enrollmentCount: 1 }
    });

    // تحديث إحصائيات المستخدم
    await User.findByIdAndUpdate(userId, {
      $inc: { 'stats.coursesStarted': 1 }
    });

    // جلب التسجيل مع بيانات الدورة
    const populatedEnrollment = await Enrollment.findById(enrollment._id)
      .populate('course', 'title instructor level category duration image');

    return NextResponse.json({
      message: 'تم التسجيل في الدورة بنجاح',
      enrollment: populatedEnrollment
    }, { status: 201 });

  } catch (error) {
    console.error('Enrollment creation error:', error);
    return NextResponse.json(
      { message: 'خطأ في التسجيل في الدورة' },
      { status: 500 }
    );
  }
});
