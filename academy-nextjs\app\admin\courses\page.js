'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '../../../contexts/AuthContext';
import ProtectedRoute from '../../../components/ProtectedRoute';
import api from '../../../lib/api';
import { toast } from 'react-toastify';
import {
  BookOpen,
  Plus,
  Edit,
  Eye,
  Users,
  Star,
  Clock,
  Search,
  Filter,
  MoreVertical,
  Settings
} from 'lucide-react';

export default function AdminCourses() {
  const { user } = useAuth();
  const router = useRouter();

  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('');
  const [filterStatus, setFilterStatus] = useState('');

  useEffect(() => {
    fetchCourses();
  }, []);

  const fetchCourses = async () => {
    try {
      setLoading(true);
      const response = await api.get('/courses', {
        params: {
          admin: true, // جلب جميع الدورات للمدراء
          search: searchTerm,
          category: filterCategory,
          status: filterStatus
        }
      });
      setCourses(response.data.courses || []);
    } catch (error) {
      console.error('Error fetching courses:', error);
      toast.error('خطأ في جلب الدورات');
    } finally {
      setLoading(false);
    }
  };

  const toggleCourseStatus = async (courseId, currentStatus) => {
    try {
      await api.put(`/courses/${courseId}`, {
        isActive: !currentStatus
      });
      toast.success('تم تحديث حالة الدورة');
      fetchCourses();
    } catch (error) {
      console.error('Error updating course status:', error);
      toast.error('خطأ في تحديث حالة الدورة');
    }
  };

  const toggleFeatured = async (courseId, currentFeatured) => {
    try {
      await api.put(`/courses/${courseId}`, {
        isFeatured: !currentFeatured
      });
      toast.success('تم تحديث حالة التميز');
      fetchCourses();
    } catch (error) {
      console.error('Error updating featured status:', error);
      toast.error('خطأ في تحديث حالة التميز');
    }
  };

  const filteredCourses = courses.filter(course => {
    const matchesSearch = course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         course.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = !filterCategory || course.category === filterCategory;
    const matchesStatus = !filterStatus || 
                         (filterStatus === 'active' && course.isActive) ||
                         (filterStatus === 'inactive' && !course.isActive) ||
                         (filterStatus === 'featured' && course.isFeatured);
    
    return matchesSearch && matchesCategory && matchesStatus;
  });

  return (
    <ProtectedRoute allowedRoles={['admin', 'super-admin']}>
      <div className="container-fluid py-4">
        {/* Header */}
        <div className="row mb-4">
          <div className="col-12">
            <div className="d-flex justify-content-between align-items-center">
              <div>
                <h2 className="mb-1">إدارة الدورات</h2>
                <p className="text-muted mb-0">
                  إدارة وتحرير الدورات التدريبية
                </p>
              </div>
              <button
                className="btn btn-primary"
                onClick={() => router.push('/admin/add-course')}
              >
                <Plus className="w-4 h-4 me-1" />
                إضافة دورة جديدة
              </button>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="row mb-4">
          <div className="col-12">
            <div className="card">
              <div className="card-body">
                <div className="row">
                  <div className="col-md-4">
                    <div className="input-group">
                      <span className="input-group-text">
                        <Search className="w-4 h-4" />
                      </span>
                      <input
                        type="text"
                        className="form-control"
                        placeholder="البحث في الدورات..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                    </div>
                  </div>
                  <div className="col-md-3">
                    <select
                      className="form-select"
                      value={filterCategory}
                      onChange={(e) => setFilterCategory(e.target.value)}
                    >
                      <option value="">جميع الفئات</option>
                      <option value="البرمجة">البرمجة</option>
                      <option value="التصميم">التصميم</option>
                      <option value="التسويق الرقمي">التسويق الرقمي</option>
                      <option value="إدارة الأعمال">إدارة الأعمال</option>
                    </select>
                  </div>
                  <div className="col-md-3">
                    <select
                      className="form-select"
                      value={filterStatus}
                      onChange={(e) => setFilterStatus(e.target.value)}
                    >
                      <option value="">جميع الحالات</option>
                      <option value="active">نشطة</option>
                      <option value="inactive">غير نشطة</option>
                      <option value="featured">مميزة</option>
                    </select>
                  </div>
                  <div className="col-md-2">
                    <button
                      className="btn btn-outline-primary w-100"
                      onClick={fetchCourses}
                    >
                      <Filter className="w-4 h-4 me-1" />
                      تطبيق
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Courses List */}
        <div className="row">
          <div className="col-12">
            <div className="card">
              <div className="card-header">
                <h5 className="mb-0">
                  الدورات ({filteredCourses.length})
                </h5>
              </div>
              <div className="card-body">
                {loading ? (
                  <div className="text-center py-5">
                    <div className="spinner-border" role="status">
                      <span className="visually-hidden">جاري التحميل...</span>
                    </div>
                  </div>
                ) : filteredCourses.length > 0 ? (
                  <div className="table-responsive">
                    <table className="table table-hover">
                      <thead>
                        <tr>
                          <th>الدورة</th>
                          <th>الفئة</th>
                          <th>المستوى</th>
                          <th>الطلاب</th>
                          <th>التقييم</th>
                          <th>الحالة</th>
                          <th>الإجراءات</th>
                        </tr>
                      </thead>
                      <tbody>
                        {filteredCourses.map(course => (
                          <tr key={course._id}>
                            <td>
                              <div className="d-flex align-items-center">
                                {course.image ? (
                                  <img
                                    src={course.image}
                                    alt={course.title}
                                    className="rounded me-3"
                                    style={{ width: '50px', height: '50px', objectFit: 'cover' }}
                                  />
                                ) : (
                                  <div 
                                    className="bg-light rounded me-3 d-flex align-items-center justify-content-center"
                                    style={{ width: '50px', height: '50px' }}
                                  >
                                    <BookOpen className="w-5 h-5 text-muted" />
                                  </div>
                                )}
                                <div>
                                  <div className="fw-bold">{course.title}</div>
                                  <small className="text-muted">{course.instructor}</small>
                                  {course.isFeatured && (
                                    <Star className="w-4 h-4 text-warning ms-1" />
                                  )}
                                </div>
                              </div>
                            </td>
                            <td>{course.category}</td>
                            <td>{course.level}</td>
                            <td>
                              <Users className="w-4 h-4 me-1" />
                              {course.enrollmentCount || 0}
                            </td>
                            <td>
                              {course.rating > 0 ? (
                                <div className="d-flex align-items-center">
                                  <Star className="w-4 h-4 text-warning me-1" />
                                  {course.rating.toFixed(1)}
                                  <small className="text-muted ms-1">({course.ratingCount})</small>
                                </div>
                              ) : (
                                <span className="text-muted">لا يوجد تقييم</span>
                              )}
                            </td>
                            <td>
                              <div className="d-flex gap-1">
                                <span className={`badge ${course.isActive ? 'bg-success' : 'bg-secondary'}`}>
                                  {course.isActive ? 'نشطة' : 'غير نشطة'}
                                </span>
                                {course.isFeatured && (
                                  <span className="badge bg-warning">مميزة</span>
                                )}
                              </div>
                            </td>
                            <td>
                              <div className="dropdown">
                                <button
                                  className="btn btn-outline-secondary btn-sm dropdown-toggle"
                                  type="button"
                                  data-bs-toggle="dropdown"
                                >
                                  <MoreVertical className="w-4 h-4" />
                                </button>
                                <ul className="dropdown-menu">
                                  <li>
                                    <button
                                      className="dropdown-item"
                                      onClick={() => router.push(`/admin/courses/${course._id}/manage`)}
                                    >
                                      <Settings className="w-4 h-4 me-2" />
                                      إدارة المحتوى
                                    </button>
                                  </li>
                                  <li>
                                    <button
                                      className="dropdown-item"
                                      onClick={() => router.push(`/courses/${course._id}`)}
                                    >
                                      <Eye className="w-4 h-4 me-2" />
                                      معاينة
                                    </button>
                                  </li>
                                  <li><hr className="dropdown-divider" /></li>
                                  <li>
                                    <button
                                      className="dropdown-item"
                                      onClick={() => toggleCourseStatus(course._id, course.isActive)}
                                    >
                                      {course.isActive ? 'إلغاء التفعيل' : 'تفعيل'}
                                    </button>
                                  </li>
                                  <li>
                                    <button
                                      className="dropdown-item"
                                      onClick={() => toggleFeatured(course._id, course.isFeatured)}
                                    >
                                      {course.isFeatured ? 'إلغاء التميز' : 'جعل مميزة'}
                                    </button>
                                  </li>
                                </ul>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div className="text-center py-5">
                    <BookOpen className="w-12 h-12 text-muted mx-auto mb-3" />
                    <h5 className="text-muted">لا توجد دورات</h5>
                    <p className="text-muted">ابدأ بإنشاء دورة جديدة</p>
                    <button
                      className="btn btn-primary"
                      onClick={() => router.push('/admin/add-course')}
                    >
                      <Plus className="w-4 h-4 me-1" />
                      إضافة دورة جديدة
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
