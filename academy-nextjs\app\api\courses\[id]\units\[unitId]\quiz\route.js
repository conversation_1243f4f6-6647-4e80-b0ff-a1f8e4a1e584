import { NextResponse } from 'next/server';
import { verifyToken } from '../../../../../../../lib/auth';
import { connectDB } from '../../../../../../../lib/mongodb';
import Course from '../../../../../../../models/Course';
import Enrollment from '../../../../../../../models/Enrollment';

// GET - جلب اختبار الوحدة
export async function GET(request, { params }) {
  try {
    await connectDB();
    
    const { id: courseId, unitId } = params;
    
    const course = await Course.findById(courseId);
    if (!course) {
      return NextResponse.json(
        { message: 'الدورة غير موجودة' },
        { status: 404 }
      );
    }

    const unit = course.units.id(unitId);
    if (!unit) {
      return NextResponse.json(
        { message: 'الوحدة غير موجودة' },
        { status: 404 }
      );
    }

    // إخفاء الإجابات الصحيحة للطلاب
    const authResult = await verifyToken(request);
    let quiz = unit.quiz;
    
    if (authResult.success && !['admin', 'super-admin'].includes(authResult.user.role)) {
      quiz = {
        ...quiz.toObject(),
        questions: quiz.questions.map(q => ({
          _id: q._id,
          question: q.question,
          options: q.options
          // إخفاء correctAnswer و explanation
        }))
      };
    }

    return NextResponse.json({
      success: true,
      quiz,
      unitTitle: unit.title
    });

  } catch (error) {
    console.error('Error fetching quiz:', error);
    return NextResponse.json(
      { message: 'خطأ في جلب الاختبار' },
      { status: 500 }
    );
  }
}

// POST - إرسال إجابات الاختبار
export async function POST(request, { params }) {
  try {
    await connectDB();
    
    // التحقق من صحة التوكن
    const authResult = await verifyToken(request);
    if (!authResult.success) {
      return NextResponse.json(
        { message: authResult.message },
        { status: 401 }
      );
    }

    const { user } = authResult;
    const { id: courseId, unitId } = params;
    const { answers } = await request.json();

    if (!answers || !Array.isArray(answers)) {
      return NextResponse.json(
        { message: 'الإجابات مطلوبة' },
        { status: 400 }
      );
    }

    const course = await Course.findById(courseId);
    if (!course) {
      return NextResponse.json(
        { message: 'الدورة غير موجودة' },
        { status: 404 }
      );
    }

    const unit = course.units.id(unitId);
    if (!unit) {
      return NextResponse.json(
        { message: 'الوحدة غير موجودة' },
        { status: 404 }
      );
    }

    // التحقق من التسجيل في الدورة
    const enrollment = await Enrollment.findOne({
      student: user._id,
      course: courseId
    });

    if (!enrollment) {
      return NextResponse.json(
        { message: 'يجب التسجيل في الدورة أولاً' },
        { status: 403 }
      );
    }

    // حساب النتيجة
    const quiz = unit.quiz;
    let correctAnswers = 0;
    const results = [];

    quiz.questions.forEach((question, index) => {
      const userAnswer = answers[index];
      const isCorrect = userAnswer === question.correctAnswer;
      
      if (isCorrect) {
        correctAnswers++;
      }

      results.push({
        questionId: question._id,
        question: question.question,
        userAnswer,
        correctAnswer: question.correctAnswer,
        isCorrect,
        explanation: question.explanation
      });
    });

    const score = Math.round((correctAnswers / quiz.questions.length) * 100);
    const passed = score >= quiz.passingScore;

    // العثور على الوحدة في تقدم الطالب
    const unitIndex = course.units.findIndex(u => u._id.toString() === unitId);
    
    // تحديث تقدم الطالب
    if (enrollment.unitsProgress.length <= unitIndex) {
      // إنشاء تقدم جديد للوحدة إذا لم يكن موجوداً
      for (let i = enrollment.unitsProgress.length; i <= unitIndex; i++) {
        enrollment.unitsProgress.push({
          unitId: course.units[i]._id,
          unitIndex: i,
          isUnlocked: i === 0 || i <= enrollment.currentUnit,
          lessons: course.units[i].lessons.map((lesson, lessonIndex) => ({
            lessonId: lesson._id,
            lessonIndex,
            completed: false
          })),
          quizCompleted: false,
          quizAttempts: 0,
          unitCompleted: false
        });
      }
    }

    // تحديث نتيجة الاختبار
    await enrollment.completeUnitQuiz(unitIndex, score);

    return NextResponse.json({
      success: true,
      score,
      passed,
      correctAnswers,
      totalQuestions: quiz.questions.length,
      passingScore: quiz.passingScore,
      results,
      message: passed ? 'تهانينا! لقد نجحت في الاختبار' : 'لم تحصل على الدرجة المطلوبة للنجاح'
    });

  } catch (error) {
    console.error('Error submitting quiz:', error);
    return NextResponse.json(
      { message: 'خطأ في إرسال الاختبار' },
      { status: 500 }
    );
  }
}

// PUT - تحديث اختبار الوحدة (للمدراء فقط)
export async function PUT(request, { params }) {
  try {
    await connectDB();
    
    // التحقق من صحة التوكن والصلاحيات
    const authResult = await verifyToken(request);
    if (!authResult.success) {
      return NextResponse.json(
        { message: authResult.message },
        { status: 401 }
      );
    }

    const { user } = authResult;
    if (!['admin', 'super-admin'].includes(user.role)) {
      return NextResponse.json(
        { message: 'غير مصرح لك بهذا الإجراء' },
        { status: 403 }
      );
    }

    const { id: courseId, unitId } = params;
    const { title, questions, passingScore, timeLimit } = await request.json();

    const course = await Course.findById(courseId);
    if (!course) {
      return NextResponse.json(
        { message: 'الدورة غير موجودة' },
        { status: 404 }
      );
    }

    const unit = course.units.id(unitId);
    if (!unit) {
      return NextResponse.json(
        { message: 'الوحدة غير موجودة' },
        { status: 404 }
      );
    }

    // تحديث الاختبار
    unit.quiz = {
      title: title || unit.quiz.title,
      questions: questions || unit.quiz.questions,
      passingScore: passingScore || unit.quiz.passingScore,
      timeLimit: timeLimit || unit.quiz.timeLimit
    };

    course.updatedBy = user._id;
    await course.save();

    return NextResponse.json({
      success: true,
      message: 'تم تحديث الاختبار بنجاح',
      quiz: unit.quiz
    });

  } catch (error) {
    console.error('Error updating quiz:', error);
    return NextResponse.json(
      { message: 'خطأ في تحديث الاختبار' },
      { status: 500 }
    );
  }
}
