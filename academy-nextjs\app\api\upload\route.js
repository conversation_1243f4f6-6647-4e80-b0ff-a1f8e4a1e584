import { NextResponse } from 'next/server';
import { verifyToken } from '../../../lib/auth';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';

export async function POST(request) {
  try {
    // التحقق من صحة التوكن والصلاحيات
    const authResult = await verifyToken(request);
    if (!authResult.success) {
      return NextResponse.json(
        { message: authResult.message },
        { status: 401 }
      );
    }

    const { user } = authResult;
    if (!['admin', 'super-admin'].includes(user.role)) {
      return NextResponse.json(
        { message: 'غير مصرح لك برفع الملفات' },
        { status: 403 }
      );
    }

    const formData = await request.formData();
    const file = formData.get('file');
    const type = formData.get('type'); // 'image' or 'video'

    if (!file) {
      return NextResponse.json(
        { message: 'لم يتم اختيار ملف' },
        { status: 400 }
      );
    }

    // التحقق من نوع الملف
    if (type === 'image' && !file.type.startsWith('image/')) {
      return NextResponse.json(
        { message: 'يجب أن يكون الملف صورة' },
        { status: 400 }
      );
    }

    if (type === 'video' && !file.type.startsWith('video/')) {
      return NextResponse.json(
        { message: 'يجب أن يكون الملف فيديو' },
        { status: 400 }
      );
    }

    // التحقق من حجم الملف
    const maxSize = type === 'image' ? 5 * 1024 * 1024 : 100 * 1024 * 1024; // 5MB للصور، 100MB للفيديو
    if (file.size > maxSize) {
      const maxSizeText = type === 'image' ? '5 ميجابايت' : '100 ميجابايت';
      return NextResponse.json(
        { message: `حجم الملف يجب أن يكون أقل من ${maxSizeText}` },
        { status: 400 }
      );
    }

    // إنشاء اسم ملف فريد
    const timestamp = Date.now();
    const extension = file.name.split('.').pop();
    const fileName = `${timestamp}-${Math.random().toString(36).substring(2)}.${extension}`;
    
    // تحديد مجلد الحفظ
    const uploadDir = join(process.cwd(), 'uploads', type === 'image' ? 'images' : 'videos');
    
    // إنشاء المجلد إذا لم يكن موجوداً
    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true });
    }

    // حفظ الملف
    const filePath = join(uploadDir, fileName);
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    
    await writeFile(filePath, buffer);

    // إنشاء URL للملف
    const fileUrl = `/uploads/${type === 'image' ? 'images' : 'videos'}/${fileName}`;

    return NextResponse.json({
      success: true,
      message: 'تم رفع الملف بنجاح',
      url: fileUrl,
      fileName,
      size: file.size,
      type: file.type
    });

  } catch (error) {
    console.error('Error uploading file:', error);
    return NextResponse.json(
      { message: 'خطأ في رفع الملف' },
      { status: 500 }
    );
  }
}

// GET - جلب قائمة الملفات المرفوعة
export async function GET(request) {
  try {
    // التحقق من صحة التوكن والصلاحيات
    const authResult = await verifyToken(request);
    if (!authResult.success) {
      return NextResponse.json(
        { message: authResult.message },
        { status: 401 }
      );
    }

    const { user } = authResult;
    if (!['admin', 'super-admin'].includes(user.role)) {
      return NextResponse.json(
        { message: 'غير مصرح لك بعرض الملفات' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type'); // 'image' or 'video'

    // هنا يمكن إضافة منطق لجلب قائمة الملفات من قاعدة البيانات
    // أو من نظام الملفات

    return NextResponse.json({
      success: true,
      files: [] // قائمة فارغة حالياً
    });

  } catch (error) {
    console.error('Error fetching files:', error);
    return NextResponse.json(
      { message: 'خطأ في جلب الملفات' },
      { status: 500 }
    );
  }
}
