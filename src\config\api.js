import { ENV_CONFIG } from './environment.js';

// API Configuration - حل بسيط وآمن
const getBaseUrl = () => {
  // استخدام التكوين الجديد
  const baseUrl = ENV_CONFIG.getApiUrl();

  // طباعة معلومات التشخيص في وضع التطوير
  if (ENV_CONFIG.isDevelopment) {
    console.log('API Configuration:', {
      currentUrl: baseUrl,
      environment: ENV_CONFIG.debug
    });
  }

  return baseUrl;
};

// تصدير التكوين
export const API_CONFIG = {
  BASE_URL: getBaseUrl(),

  // API endpoints
  ENDPOINTS: {
    API: '/api',
    AUTH: '/api/auth',
    COURSES: '/api/courses',
    USERS: '/api/user',
    ADMIN: '/api/admin',
    SUPER_ADMIN: '/api/super-admin',
    CHATBOT: '/api/chatbot',
    HONOR_BOARD: '/api/honor-board',
    USER_PROGRESS: '/api/user-progress',
    PUBLIC: '/api/public'
  }
};

// Helper functions
export const getApiUrl = (endpoint = '') => {
  return `${API_CONFIG.BASE_URL}${endpoint}`;
};

export const getEndpointUrl = (endpointKey) => {
  const endpoint = API_CONFIG.ENDPOINTS[endpointKey];
  return endpoint ? `${API_CONFIG.BASE_URL}${endpoint}` : API_CONFIG.BASE_URL;
};

export default API_CONFIG;
