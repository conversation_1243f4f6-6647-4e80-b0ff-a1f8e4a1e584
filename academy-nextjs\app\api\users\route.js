import { NextResponse } from 'next/server';
import { withAuth } from '../../../lib/auth';
import connectDB from '../../../lib/mongodb';
import User from '../../../models/User';
import Enrollment from '../../../models/Enrollment';

// GET - جلب جميع المستخدمين (للمدراء فقط)
export const GET = withAuth(async (request) => {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 10;
    const search = searchParams.get('search') || '';
    const role = searchParams.get('role') || '';
    const status = searchParams.get('status') || '';
    
    // بناء الاستعلام
    const query = {};
    
    // البحث النصي
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }
    
    // فلترة حسب الدور
    if (role) {
      query.role = role;
    }
    
    // فلترة حسب الحالة
    if (status === 'active') {
      query.isActive = true;
    } else if (status === 'inactive') {
      query.isActive = false;
    }
    
    // حساب التخطي
    const skip = (page - 1) * limit;
    
    // جلب المستخدمين
    const users = await User.find(query)
      .select('-password')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();
    
    // إضافة إحصائيات لكل مستخدم
    const usersWithStats = await Promise.all(
      users.map(async (user) => {
        if (user.role === 'student') {
          const enrollments = await Enrollment.find({ student: user._id });
          const completedCourses = enrollments.filter(e => e.isCompleted).length;
          const totalPoints = enrollments.reduce((sum, e) => sum + (e.finalScore || 0), 0);
          
          return {
            ...user,
            stats: {
              enrolledCourses: enrollments.length,
              completedCourses,
              totalPoints,
              averageScore: enrollments.length > 0 ? totalPoints / enrollments.length : 0
            }
          };
        }
        return user;
      })
    );
    
    // عدد المستخدمين الإجمالي
    const total = await User.countDocuments(query);
    
    return NextResponse.json({
      users: usersWithStats,
      pagination: {
        current: page,
        total: Math.ceil(total / limit),
        count: users.length,
        totalUsers: total
      }
    }, { status: 200 });

  } catch (error) {
    console.error('Users fetch error:', error);
    return NextResponse.json(
      { message: 'خطأ في جلب المستخدمين' },
      { status: 500 }
    );
  }
}, { roles: ['admin', 'super-admin'] });

// POST - إنشاء مستخدم جديد (للمدراء فقط)
export const POST = withAuth(async (request) => {
  try {
    await connectDB();
    
    const data = await request.json();
    const { name, email, password, role = 'student' } = data;

    // التحقق من البيانات المطلوبة
    if (!name || !email || !password) {
      return NextResponse.json(
        { message: 'الاسم والبريد الإلكتروني وكلمة المرور مطلوبة' },
        { status: 400 }
      );
    }

    // التحقق من وجود المستخدم
    const existingUser = await User.findOne({ email: email.toLowerCase() });
    if (existingUser) {
      return NextResponse.json(
        { message: 'البريد الإلكتروني مستخدم بالفعل' },
        { status: 400 }
      );
    }

    // إنشاء المستخدم الجديد
    const user = new User({
      name: name.trim(),
      email: email.toLowerCase().trim(),
      password,
      role
    });

    await user.save();

    return NextResponse.json({
      message: 'تم إنشاء المستخدم بنجاح',
      user: user.toJSON()
    }, { status: 201 });

  } catch (error) {
    console.error('User creation error:', error);
    
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(err => err.message);
      return NextResponse.json(
        { message: messages.join(', ') },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: 'خطأ في إنشاء المستخدم' },
      { status: 500 }
    );
  }
}, { roles: ['admin', 'super-admin'] });
