'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '../../../contexts/AuthContext';
import ProtectedRoute from '../../../components/ProtectedRoute';
import api from '../../../lib/api';
import { toast } from 'react-toastify';
import { 
  HelpCircle, 
  Plus, 
  Edit3, 
  Trash2, 
  Eye,
  Users,
  Clock,
  CheckCircle,
  XCircle,
  Search,
  Filter,
  BarChart3
} from 'lucide-react';

export default function QuizzesManagement() {
  const router = useRouter();
  const { user } = useAuth();
  
  const [quizzes, setQuizzes] = useState([]);
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCourse, setSelectedCourse] = useState('');
  const [selectedType, setSelectedType] = useState('');

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      
      const [quizzesResponse, coursesResponse] = await Promise.all([
        api.get('/quizzes'),
        api.get('/courses')
      ]);
      
      setQuizzes(quizzesResponse.data.quizzes || []);
      setCourses(coursesResponse.data.courses || []);
      
    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('خطأ في جلب البيانات');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteQuiz = async (quizId) => {
    if (!confirm('هل أنت متأكد من حذف هذا الاختبار؟')) return;

    try {
      await api.delete(`/quizzes/${quizId}`);
      setQuizzes(prev => prev.filter(quiz => quiz._id !== quizId));
      toast.success('تم حذف الاختبار بنجاح');
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'خطأ في حذف الاختبار';
      toast.error(errorMessage);
    }
  };

  const toggleQuizStatus = async (quizId, currentStatus) => {
    try {
      await api.put(`/quizzes/${quizId}`, {
        isPublished: !currentStatus
      });
      
      setQuizzes(prev => prev.map(quiz => 
        quiz._id === quizId 
          ? { ...quiz, isPublished: !currentStatus }
          : quiz
      ));
      
      toast.success(`تم ${!currentStatus ? 'نشر' : 'إلغاء نشر'} الاختبار`);
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'خطأ في تحديث حالة الاختبار';
      toast.error(errorMessage);
    }
  };

  const getQuizTypeLabel = (type) => {
    switch (type) {
      case 'practice': return 'تدريبي';
      case 'graded': return 'مقيم';
      case 'final': return 'نهائي';
      default: return 'تدريبي';
    }
  };

  const getQuizTypeBadge = (type) => {
    switch (type) {
      case 'practice': return 'bg-info';
      case 'graded': return 'bg-warning';
      case 'final': return 'bg-danger';
      default: return 'bg-info';
    }
  };

  // فلترة الاختبارات
  const filteredQuizzes = quizzes.filter(quiz => {
    const matchesSearch = quiz.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         quiz.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCourse = !selectedCourse || quiz.course._id === selectedCourse;
    const matchesType = !selectedType || quiz.type === selectedType;
    
    return matchesSearch && matchesCourse && matchesType;
  });

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">جاري التحميل...</span>
        </div>
      </div>
    );
  }

  return (
    <ProtectedRoute allowedRoles={['admin', 'super-admin']}>
      <div className="container-fluid py-4">
        {/* Header */}
        <div className="row mb-4">
          <div className="col-12">
            <div className="d-flex justify-content-between align-items-center">
              <div>
                <h2 className="mb-1">إدارة الاختبارات</h2>
                <p className="text-muted mb-0">
                  إنشاء وإدارة اختبارات الدورات
                </p>
              </div>
              <button
                className="btn btn-primary"
                onClick={() => router.push('/admin/quizzes/create')}
              >
                <Plus className="w-4 h-4 me-1" />
                إنشاء اختبار جديد
              </button>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="row mb-4">
          <div className="col-md-3">
            <div className="card bg-primary text-white">
              <div className="card-body">
                <div className="d-flex align-items-center">
                  <HelpCircle className="w-8 h-8 me-3" />
                  <div>
                    <h5 className="mb-0">{quizzes.length}</h5>
                    <small>إجمالي الاختبارات</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="col-md-3">
            <div className="card bg-success text-white">
              <div className="card-body">
                <div className="d-flex align-items-center">
                  <CheckCircle className="w-8 h-8 me-3" />
                  <div>
                    <h5 className="mb-0">
                      {quizzes.filter(q => q.isPublished).length}
                    </h5>
                    <small>منشورة</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="col-md-3">
            <div className="card bg-warning text-white">
              <div className="card-body">
                <div className="d-flex align-items-center">
                  <XCircle className="w-8 h-8 me-3" />
                  <div>
                    <h5 className="mb-0">
                      {quizzes.filter(q => !q.isPublished).length}
                    </h5>
                    <small>مسودات</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="col-md-3">
            <div className="card bg-info text-white">
              <div className="card-body">
                <div className="d-flex align-items-center">
                  <BarChart3 className="w-8 h-8 me-3" />
                  <div>
                    <h5 className="mb-0">
                      {quizzes.reduce((sum, q) => sum + (q.stats?.totalAttempts || 0), 0)}
                    </h5>
                    <small>إجمالي المحاولات</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="card mb-4">
          <div className="card-body">
            <div className="row">
              <div className="col-md-4">
                <div className="input-group">
                  <span className="input-group-text">
                    <Search className="w-4 h-4" />
                  </span>
                  <input
                    type="text"
                    className="form-control"
                    placeholder="البحث في الاختبارات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
              <div className="col-md-4">
                <select
                  className="form-select"
                  value={selectedCourse}
                  onChange={(e) => setSelectedCourse(e.target.value)}
                >
                  <option value="">جميع الدورات</option>
                  {courses.map(course => (
                    <option key={course._id} value={course._id}>
                      {course.title}
                    </option>
                  ))}
                </select>
              </div>
              <div className="col-md-4">
                <select
                  className="form-select"
                  value={selectedType}
                  onChange={(e) => setSelectedType(e.target.value)}
                >
                  <option value="">جميع الأنواع</option>
                  <option value="practice">تدريبي</option>
                  <option value="graded">مقيم</option>
                  <option value="final">نهائي</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Quizzes List */}
        <div className="card">
          <div className="card-body">
            {filteredQuizzes.length === 0 ? (
              <div className="text-center py-5">
                <HelpCircle className="w-16 h-16 text-muted mx-auto mb-3" />
                <h5 className="text-muted">لا توجد اختبارات</h5>
                <p className="text-muted">ابدأ بإنشاء اختبار جديد</p>
                <button
                  className="btn btn-primary"
                  onClick={() => router.push('/admin/quizzes/create')}
                >
                  <Plus className="w-4 h-4 me-1" />
                  إنشاء اختبار جديد
                </button>
              </div>
            ) : (
              <div className="table-responsive">
                <table className="table table-hover">
                  <thead>
                    <tr>
                      <th>الاختبار</th>
                      <th>الدورة</th>
                      <th>النوع</th>
                      <th>الأسئلة</th>
                      <th>المحاولات</th>
                      <th>الحالة</th>
                      <th>الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredQuizzes.map(quiz => (
                      <tr key={quiz._id}>
                        <td>
                          <div>
                            <h6 className="mb-0">{quiz.title}</h6>
                            <small className="text-muted">{quiz.description}</small>
                          </div>
                        </td>
                        <td>
                          <span className="badge bg-light text-dark">
                            {quiz.course.title}
                          </span>
                        </td>
                        <td>
                          <span className={`badge ${getQuizTypeBadge(quiz.type)}`}>
                            {getQuizTypeLabel(quiz.type)}
                          </span>
                        </td>
                        <td>
                          <div className="d-flex align-items-center">
                            <HelpCircle className="w-4 h-4 me-1" />
                            {quiz.questionsCount || 0}
                          </div>
                        </td>
                        <td>
                          <div className="d-flex align-items-center">
                            <Users className="w-4 h-4 me-1" />
                            {quiz.stats?.totalAttempts || 0}
                          </div>
                        </td>
                        <td>
                          <button
                            className={`btn btn-sm ${quiz.isPublished ? 'btn-success' : 'btn-warning'}`}
                            onClick={() => toggleQuizStatus(quiz._id, quiz.isPublished)}
                          >
                            {quiz.isPublished ? (
                              <>
                                <CheckCircle className="w-3 h-3 me-1" />
                                منشور
                              </>
                            ) : (
                              <>
                                <XCircle className="w-3 h-3 me-1" />
                                مسودة
                              </>
                            )}
                          </button>
                        </td>
                        <td>
                          <div className="d-flex gap-1">
                            <button
                              className="btn btn-outline-primary btn-sm"
                              onClick={() => router.push(`/admin/quizzes/${quiz._id}`)}
                              title="عرض"
                            >
                              <Eye className="w-3 h-3" />
                            </button>
                            <button
                              className="btn btn-outline-secondary btn-sm"
                              onClick={() => router.push(`/admin/quizzes/${quiz._id}/edit`)}
                              title="تعديل"
                            >
                              <Edit3 className="w-3 h-3" />
                            </button>
                            <button
                              className="btn btn-outline-danger btn-sm"
                              onClick={() => handleDeleteQuiz(quiz._id)}
                              title="حذف"
                            >
                              <Trash2 className="w-3 h-3" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
