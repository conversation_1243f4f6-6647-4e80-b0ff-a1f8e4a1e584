'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useAuth } from '../../../../../../../contexts/AuthContext';
import ProtectedRoute from '../../../../../../../components/ProtectedRoute';
import api from '../../../../../../../lib/api';
import { toast } from 'react-toastify';
import {
  BookOpen,
  Plus,
  Edit,
  Trash2,
  Play,
  FileText,
  Video,
  ArrowLeft,
  Save,
  HelpCircle,
  Clock,
  Target
} from 'lucide-react';

export default function ManageUnit() {
  const { user } = useAuth();
  const router = useRouter();
  const params = useParams();
  const { id: courseId, unitId } = params;

  const [course, setCourse] = useState(null);
  const [unit, setUnit] = useState(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('lessons');
  const [showAddLesson, setShowAddLesson] = useState(false);
  const [showEditQuiz, setShowEditQuiz] = useState(false);
  const [quizForm, setQuizForm] = useState({
    title: '',
    questions: [],
    passingScore: 70,
    timeLimit: 30
  });
  const [newQuestion, setNewQuestion] = useState({
    question: '',
    options: ['', '', '', ''],
    correctAnswer: '',
    explanation: ''
  });

  const [lessonForm, setLessonForm] = useState({
    title: '',
    type: 'video',
    content: '',
    videoUrl: '',
    textContent: '',
    duration: ''
  });

  useEffect(() => {
    fetchCourseAndUnit();
  }, [courseId, unitId]);

  const fetchCourseAndUnit = async () => {
    try {
      setLoading(true);
      const response = await api.get(`/courses/${courseId}`);
      const courseData = response.data.course;
      setCourse(courseData);
      
      const foundUnit = courseData.units.find(u => u._id === unitId);
      setUnit(foundUnit);

      // تحديث نموذج الاختبار
      if (foundUnit && foundUnit.quiz) {
        setQuizForm({
          title: foundUnit.quiz.title || 'اختبار الوحدة',
          questions: foundUnit.quiz.questions || [],
          passingScore: foundUnit.quiz.passingScore || 70,
          timeLimit: foundUnit.quiz.timeLimit || 30
        });
      }
    } catch (error) {
      console.error('Error fetching course:', error);
      toast.error('خطأ في جلب بيانات الدورة');
    } finally {
      setLoading(false);
    }
  };

  const handleAddLesson = async (e) => {
    e.preventDefault();
    
    if (!lessonForm.title) {
      toast.error('عنوان الدرس مطلوب');
      return;
    }

    try {
      await api.post(`/courses/${courseId}/units/${unitId}/lessons`, lessonForm);
      toast.success('تم إضافة الدرس بنجاح');
      setShowAddLesson(false);
      setLessonForm({
        title: '',
        type: 'video',
        content: '',
        videoUrl: '',
        textContent: '',
        duration: ''
      });
      fetchCourseAndUnit();
    } catch (error) {
      console.error('Error adding lesson:', error);
      toast.error('خطأ في إضافة الدرس');
    }
  };

  const addQuestion = () => {
    if (!newQuestion.question || !newQuestion.options.every(opt => opt.trim()) || !newQuestion.correctAnswer) {
      toast.error('يرجى ملء جميع حقول السؤال');
      return;
    }

    setQuizForm(prev => ({
      ...prev,
      questions: [...prev.questions, { ...newQuestion }]
    }));

    setNewQuestion({
      question: '',
      options: ['', '', '', ''],
      correctAnswer: '',
      explanation: ''
    });
  };

  const removeQuestion = (index) => {
    setQuizForm(prev => ({
      ...prev,
      questions: prev.questions.filter((_, i) => i !== index)
    }));
  };

  const updateQuiz = async () => {
    try {
      await api.put(`/courses/${courseId}/units/${unitId}/quiz`, quizForm);
      toast.success('تم تحديث الاختبار بنجاح');
      setShowEditQuiz(false);
      fetchCourseAndUnit();
    } catch (error) {
      console.error('Error updating quiz:', error);
      toast.error('خطأ في تحديث الاختبار');
    }
  };

  if (loading) {
    return (
      <ProtectedRoute allowedRoles={['admin', 'super-admin']}>
        <div className="container-fluid py-4">
          <div className="text-center">
            <div className="spinner-border" role="status">
              <span className="visually-hidden">جاري التحميل...</span>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  if (!course || !unit) {
    return (
      <ProtectedRoute allowedRoles={['admin', 'super-admin']}>
        <div className="container-fluid py-4">
          <div className="alert alert-danger">
            الدورة أو الوحدة غير موجودة
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute allowedRoles={['admin', 'super-admin']}>
      <div className="container-fluid py-4">
        {/* Header */}
        <div className="row mb-4">
          <div className="col-12">
            <button
              className="btn btn-outline-secondary btn-sm mb-2"
              onClick={() => router.push(`/admin/courses/${courseId}/manage`)}
            >
              <ArrowLeft className="w-4 h-4 me-1" />
              العودة للدورة
            </button>
            <h2 className="mb-1">{unit.title}</h2>
            <p className="text-muted mb-0">
              إدارة دروس الوحدة والاختبار
            </p>
          </div>
        </div>

        {/* Tabs */}
        <div className="row mb-4">
          <div className="col-12">
            <ul className="nav nav-tabs">
              <li className="nav-item">
                <button
                  className={`nav-link ${activeTab === 'lessons' ? 'active' : ''}`}
                  onClick={() => setActiveTab('lessons')}
                >
                  <Play className="w-4 h-4 me-1" />
                  الدروس ({unit.lessons?.length || 0})
                </button>
              </li>
              <li className="nav-item">
                <button
                  className={`nav-link ${activeTab === 'quiz' ? 'active' : ''}`}
                  onClick={() => setActiveTab('quiz')}
                >
                  <HelpCircle className="w-4 h-4 me-1" />
                  الاختبار ({unit.quiz?.questions?.length || 0} سؤال)
                </button>
              </li>
            </ul>
          </div>
        </div>

        {/* Lessons Tab */}
        {activeTab === 'lessons' && (
          <div className="row">
            <div className="col-12">
              <div className="card">
                <div className="card-header d-flex justify-content-between align-items-center">
                  <h5 className="mb-0">دروس الوحدة</h5>
                  <button
                    className="btn btn-primary"
                    onClick={() => setShowAddLesson(true)}
                  >
                    <Plus className="w-4 h-4 me-1" />
                    إضافة درس
                  </button>
                </div>
                <div className="card-body">
                  {unit.lessons && unit.lessons.length > 0 ? (
                    <div className="list-group">
                      {unit.lessons.map((lesson, index) => (
                        <div key={lesson._id} className="list-group-item">
                          <div className="d-flex justify-content-between align-items-center">
                            <div>
                              <h6 className="mb-1">
                                {lesson.type === 'video' && <Video className="w-4 h-4 me-1" />}
                                {lesson.type === 'reading' && <FileText className="w-4 h-4 me-1" />}
                                {lesson.type === 'exercise' && <Edit className="w-4 h-4 me-1" />}
                                الدرس {index + 1}: {lesson.title}
                              </h6>
                              <small className="text-muted">
                                النوع: {lesson.type === 'video' ? 'فيديو' : lesson.type === 'reading' ? 'قراءة' : 'تمرين'}
                                {lesson.duration && ` • المدة: ${lesson.duration}`}
                              </small>
                            </div>
                            <div className="d-flex gap-2">
                              <button
                                className="btn btn-outline-primary btn-sm"
                                onClick={() => router.push(`/admin/courses/${courseId}/units/${unitId}/lessons/${lesson._id}/edit`)}
                              >
                                <Edit className="w-4 h-4" />
                                تعديل
                              </button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-5">
                      <Play className="w-12 h-12 text-muted mx-auto mb-3" />
                      <h5 className="text-muted">لا توجد دروس بعد</h5>
                      <p className="text-muted">ابدأ بإضافة درس جديد للوحدة</p>
                      <button
                        className="btn btn-primary"
                        onClick={() => setShowAddLesson(true)}
                      >
                        <Plus className="w-4 h-4 me-1" />
                        إضافة أول درس
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Quiz Tab */}
        {activeTab === 'quiz' && (
          <div className="row">
            <div className="col-12">
              <div className="card">
                <div className="card-header d-flex justify-content-between align-items-center">
                  <h5 className="mb-0">اختبار الوحدة</h5>
                  <button
                    className="btn btn-primary"
                    onClick={() => setShowEditQuiz(true)}
                  >
                    <Edit className="w-4 h-4 me-1" />
                    تعديل الاختبار
                  </button>
                </div>
                <div className="card-body">
                  <div className="row mb-3">
                    <div className="col-md-4">
                      <div className="d-flex align-items-center">
                        <Target className="w-5 h-5 text-primary me-2" />
                        <div>
                          <small className="text-muted">درجة النجاح</small>
                          <div className="fw-bold">{unit.quiz?.passingScore || 70}%</div>
                        </div>
                      </div>
                    </div>
                    <div className="col-md-4">
                      <div className="d-flex align-items-center">
                        <Clock className="w-5 h-5 text-warning me-2" />
                        <div>
                          <small className="text-muted">الوقت المحدد</small>
                          <div className="fw-bold">{unit.quiz?.timeLimit || 30} دقيقة</div>
                        </div>
                      </div>
                    </div>
                    <div className="col-md-4">
                      <div className="d-flex align-items-center">
                        <HelpCircle className="w-5 h-5 text-info me-2" />
                        <div>
                          <small className="text-muted">عدد الأسئلة</small>
                          <div className="fw-bold">{unit.quiz?.questions?.length || 0}</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {unit.quiz?.questions && unit.quiz.questions.length > 0 ? (
                    <div className="list-group">
                      {unit.quiz.questions.map((question, index) => (
                        <div key={question._id} className="list-group-item">
                          <h6 className="mb-2">السؤال {index + 1}: {question.question}</h6>
                          <div className="row">
                            {question.options.map((option, optIndex) => (
                              <div key={optIndex} className="col-md-6 mb-1">
                                <span className={`badge ${option === question.correctAnswer ? 'bg-success' : 'bg-light text-dark'} me-1`}>
                                  {String.fromCharCode(65 + optIndex)}
                                </span>
                                {option}
                              </div>
                            ))}
                          </div>
                          {question.explanation && (
                            <small className="text-muted mt-2 d-block">
                              <strong>التفسير:</strong> {question.explanation}
                            </small>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-5">
                      <HelpCircle className="w-12 h-12 text-muted mx-auto mb-3" />
                      <h5 className="text-muted">لا توجد أسئلة بعد</h5>
                      <p className="text-muted">أضف أسئلة للاختبار لتقييم فهم الطلاب</p>
                      <button
                        className="btn btn-primary"
                        onClick={() => setShowEditQuiz(true)}
                      >
                        <Plus className="w-4 h-4 me-1" />
                        إضافة أسئلة
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Add Lesson Modal */}
        {showAddLesson && (
          <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
            <div className="modal-dialog modal-lg">
              <div className="modal-content">
                <div className="modal-header">
                  <h5 className="modal-title">إضافة درس جديد</h5>
                  <button
                    type="button"
                    className="btn-close"
                    onClick={() => setShowAddLesson(false)}
                  ></button>
                </div>
                <form onSubmit={handleAddLesson}>
                  <div className="modal-body">
                    <div className="row">
                      <div className="col-12 mb-3">
                        <label className="form-label">عنوان الدرس *</label>
                        <input
                          type="text"
                          className="form-control"
                          value={lessonForm.title}
                          onChange={(e) => setLessonForm(prev => ({
                            ...prev,
                            title: e.target.value
                          }))}
                          required
                        />
                      </div>

                      <div className="col-md-6 mb-3">
                        <label className="form-label">نوع الدرس</label>
                        <select
                          className="form-select"
                          value={lessonForm.type}
                          onChange={(e) => setLessonForm(prev => ({
                            ...prev,
                            type: e.target.value
                          }))}
                        >
                          <option value="video">فيديو</option>
                          <option value="reading">قراءة</option>
                          <option value="exercise">تمرين</option>
                        </select>
                      </div>

                      <div className="col-md-6 mb-3">
                        <label className="form-label">المدة</label>
                        <input
                          type="text"
                          className="form-control"
                          value={lessonForm.duration}
                          onChange={(e) => setLessonForm(prev => ({
                            ...prev,
                            duration: e.target.value
                          }))}
                          placeholder="مثال: 15 دقيقة"
                        />
                      </div>

                      {lessonForm.type === 'video' && (
                        <div className="col-12 mb-3">
                          <label className="form-label">رابط الفيديو</label>
                          <input
                            type="url"
                            className="form-control"
                            value={lessonForm.videoUrl}
                            onChange={(e) => setLessonForm(prev => ({
                              ...prev,
                              videoUrl: e.target.value
                            }))}
                            placeholder="https://example.com/video.mp4"
                          />
                        </div>
                      )}

                      <div className="col-12 mb-3">
                        <label className="form-label">المحتوى النصي</label>
                        <textarea
                          className="form-control"
                          value={lessonForm.textContent}
                          onChange={(e) => setLessonForm(prev => ({
                            ...prev,
                            textContent: e.target.value
                          }))}
                          rows="6"
                          placeholder="أدخل محتوى الدرس النصي هنا..."
                        ></textarea>
                      </div>
                    </div>
                  </div>
                  <div className="modal-footer">
                    <button
                      type="button"
                      className="btn btn-secondary"
                      onClick={() => setShowAddLesson(false)}
                    >
                      إلغاء
                    </button>
                    <button
                      type="submit"
                      className="btn btn-primary"
                    >
                      <Save className="w-4 h-4 me-1" />
                      إضافة الدرس
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        )}

        {/* Edit Quiz Modal */}
        {showEditQuiz && (
          <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
            <div className="modal-dialog modal-xl">
              <div className="modal-content">
                <div className="modal-header">
                  <h5 className="modal-title">تحرير اختبار الوحدة</h5>
                  <button
                    type="button"
                    className="btn-close"
                    onClick={() => setShowEditQuiz(false)}
                  ></button>
                </div>
                <div className="modal-body">
                  {/* Quiz Settings */}
                  <div className="row mb-4">
                    <div className="col-md-4">
                      <label className="form-label">عنوان الاختبار</label>
                      <input
                        type="text"
                        className="form-control"
                        value={quizForm.title}
                        onChange={(e) => setQuizForm(prev => ({
                          ...prev,
                          title: e.target.value
                        }))}
                      />
                    </div>
                    <div className="col-md-4">
                      <label className="form-label">درجة النجاح (%)</label>
                      <input
                        type="number"
                        className="form-control"
                        value={quizForm.passingScore}
                        onChange={(e) => setQuizForm(prev => ({
                          ...prev,
                          passingScore: parseInt(e.target.value) || 70
                        }))}
                        min="0"
                        max="100"
                      />
                    </div>
                    <div className="col-md-4">
                      <label className="form-label">الوقت المحدد (دقيقة)</label>
                      <input
                        type="number"
                        className="form-control"
                        value={quizForm.timeLimit}
                        onChange={(e) => setQuizForm(prev => ({
                          ...prev,
                          timeLimit: parseInt(e.target.value) || 30
                        }))}
                        min="1"
                      />
                    </div>
                  </div>

                  {/* Existing Questions */}
                  {quizForm.questions.length > 0 && (
                    <div className="mb-4">
                      <h6>الأسئلة الموجودة</h6>
                      <div className="list-group">
                        {quizForm.questions.map((question, index) => (
                          <div key={index} className="list-group-item">
                            <div className="d-flex justify-content-between align-items-start">
                              <div className="flex-grow-1">
                                <h6 className="mb-2">السؤال {index + 1}: {question.question}</h6>
                                <div className="row">
                                  {question.options.map((option, optIndex) => (
                                    <div key={optIndex} className="col-md-6 mb-1">
                                      <span className={`badge ${option === question.correctAnswer ? 'bg-success' : 'bg-light text-dark'} me-1`}>
                                        {String.fromCharCode(65 + optIndex)}
                                      </span>
                                      {option}
                                    </div>
                                  ))}
                                </div>
                              </div>
                              <button
                                type="button"
                                className="btn btn-outline-danger btn-sm"
                                onClick={() => removeQuestion(index)}
                              >
                                <Trash2 className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Add New Question */}
                  <div className="card">
                    <div className="card-header">
                      <h6 className="mb-0">إضافة سؤال جديد</h6>
                    </div>
                    <div className="card-body">
                      <div className="row">
                        <div className="col-12 mb-3">
                          <label className="form-label">السؤال</label>
                          <input
                            type="text"
                            className="form-control"
                            value={newQuestion.question}
                            onChange={(e) => setNewQuestion(prev => ({
                              ...prev,
                              question: e.target.value
                            }))}
                            placeholder="أدخل نص السؤال"
                          />
                        </div>

                        {newQuestion.options.map((option, index) => (
                          <div key={index} className="col-md-6 mb-3">
                            <label className="form-label">الخيار {String.fromCharCode(65 + index)}</label>
                            <input
                              type="text"
                              className="form-control"
                              value={option}
                              onChange={(e) => {
                                const newOptions = [...newQuestion.options];
                                newOptions[index] = e.target.value;
                                setNewQuestion(prev => ({
                                  ...prev,
                                  options: newOptions
                                }));
                              }}
                              placeholder={`الخيار ${String.fromCharCode(65 + index)}`}
                            />
                          </div>
                        ))}

                        <div className="col-md-6 mb-3">
                          <label className="form-label">الإجابة الصحيحة</label>
                          <select
                            className="form-select"
                            value={newQuestion.correctAnswer}
                            onChange={(e) => setNewQuestion(prev => ({
                              ...prev,
                              correctAnswer: e.target.value
                            }))}
                          >
                            <option value="">اختر الإجابة الصحيحة</option>
                            {newQuestion.options.map((option, index) => (
                              option && (
                                <option key={index} value={option}>
                                  {String.fromCharCode(65 + index)}: {option}
                                </option>
                              )
                            ))}
                          </select>
                        </div>

                        <div className="col-md-6 mb-3">
                          <label className="form-label">التفسير (اختياري)</label>
                          <input
                            type="text"
                            className="form-control"
                            value={newQuestion.explanation}
                            onChange={(e) => setNewQuestion(prev => ({
                              ...prev,
                              explanation: e.target.value
                            }))}
                            placeholder="تفسير الإجابة الصحيحة"
                          />
                        </div>

                        <div className="col-12">
                          <button
                            type="button"
                            className="btn btn-outline-primary"
                            onClick={addQuestion}
                          >
                            <Plus className="w-4 h-4 me-1" />
                            إضافة السؤال
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="modal-footer">
                  <button
                    type="button"
                    className="btn btn-secondary"
                    onClick={() => setShowEditQuiz(false)}
                  >
                    إلغاء
                  </button>
                  <button
                    type="button"
                    className="btn btn-primary"
                    onClick={updateQuiz}
                  >
                    <Save className="w-4 h-4 me-1" />
                    حفظ الاختبار
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </ProtectedRoute>
  );
}
