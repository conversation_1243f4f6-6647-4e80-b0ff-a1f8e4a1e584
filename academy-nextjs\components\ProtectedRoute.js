'use client';

import { useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { Spinner } from 'react-bootstrap';

export default function ProtectedRoute({ 
  children, 
  requireAuth = true, 
  allowedRoles = [], 
  redirectTo = '/login' 
}) {
  const { user, isAuthenticated, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (loading) return; // انتظار تحميل بيانات المصادقة

    // إذا كان المسار يتطلب مصادقة والمستخدم غير مسجل دخوله
    if (requireAuth && !isAuthenticated) {
      router.push(redirectTo);
      return;
    }

    // إذا كان هناك أدوار محددة والمستخدم لا يملك الصلاحية
    if (allowedRoles.length > 0 && user && !allowedRoles.includes(user.role)) {
      router.push('/unauthorized');
      return;
    }

    // إذا كان المستخدم مسجل دخوله ويحاول الوصول لصفحات المصادقة
    if (!requireAuth && isAuthenticated && ['/login', '/register'].includes(window.location.pathname)) {
      router.push('/');
      return;
    }
  }, [user, isAuthenticated, loading, requireAuth, allowedRoles, redirectTo, router]);

  // عرض loading أثناء التحقق من المصادقة
  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center min-vh-100">
        <div className="text-center">
          <Spinner animation="border" variant="primary" />
          <p className="mt-3 text-muted">جاري التحقق من الصلاحيات...</p>
        </div>
      </div>
    );
  }

  // إذا كان المسار يتطلب مصادقة والمستخدم غير مسجل دخوله
  if (requireAuth && !isAuthenticated) {
    return null; // سيتم إعادة التوجيه
  }

  // إذا كان هناك أدوار محددة والمستخدم لا يملك الصلاحية
  if (allowedRoles.length > 0 && user && !allowedRoles.includes(user.role)) {
    return null; // سيتم إعادة التوجيه
  }

  return children;
}

// مكونات مساعدة للأدوار المختلفة
export function AdminRoute({ children }) {
  return (
    <ProtectedRoute 
      requireAuth={true} 
      allowedRoles={['admin', 'super-admin']}
      redirectTo="/unauthorized"
    >
      {children}
    </ProtectedRoute>
  );
}

export function SuperAdminRoute({ children }) {
  return (
    <ProtectedRoute 
      requireAuth={true} 
      allowedRoles={['super-admin']}
      redirectTo="/unauthorized"
    >
      {children}
    </ProtectedRoute>
  );
}

export function StudentRoute({ children }) {
  return (
    <ProtectedRoute 
      requireAuth={true} 
      allowedRoles={['student']}
      redirectTo="/unauthorized"
    >
      {children}
    </ProtectedRoute>
  );
}

export function GuestRoute({ children }) {
  return (
    <ProtectedRoute 
      requireAuth={false}
    >
      {children}
    </ProtectedRoute>
  );
}
