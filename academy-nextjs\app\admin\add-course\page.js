'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '../../../contexts/AuthContext';
import ProtectedRoute from '../../../components/ProtectedRoute';
import api from '../../../lib/api';
import { toast } from 'react-toastify';
import {
  BookOpen,
  Save,
  X,
  Eye,
  Star,
  FileText
} from 'lucide-react';

export default function AddCourse() {
  const { user } = useAuth();
  const router = useRouter();

  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    instructor: '',
    category: '',
    level: 'beginner',
    duration: '',
    price: 0,
    image: '',
    video: '', // فيديو تعريفي
    isActive: true,
    isFeatured: false,
    prerequisites: '',
    learningOutcomes: '',
    language: 'ar',
    tags: ''
  });

  const [imageFile, setImageFile] = useState(null);
  const [videoFile, setVideoFile] = useState(null);
  const [uploadingImage, setUploadingImage] = useState(false);
  const [uploadingVideo, setUploadingVideo] = useState(false);

  const categories = [
    'البرمجة',
    'التصميم',
    'التسويق الرقمي',
    'إدارة الأعمال',
    'الذكاء الاصطناعي',
    'الأمن السيبراني',
    'تطوير المواقع',
    'تطوير التطبيقات',
    'قواعد البيانات',
    'الشبكات',
    'أخرى'
  ];

  const levels = [
    { value: 'beginner', label: 'مبتدئ' },
    { value: 'intermediate', label: 'متوسط' },
    { value: 'advanced', label: 'متقدم' }
  ];

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  // رفع الصورة
  const handleImageUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    if (!file.type.startsWith('image/')) {
      toast.error('يرجى اختيار ملف صورة صحيح');
      return;
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB
      toast.error('حجم الصورة يجب أن يكون أقل من 5 ميجابايت');
      return;
    }

    setUploadingImage(true);
    setImageFile(file);

    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', 'image');

    try {
      const response = await api.post('/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      setFormData(prev => ({
        ...prev,
        image: response.data.url
      }));

      toast.success('تم رفع الصورة بنجاح');
    } catch (error) {
      console.error('Error uploading image:', error);
      toast.error('خطأ في رفع الصورة');
    } finally {
      setUploadingImage(false);
    }
  };

  // رفع الفيديو
  const handleVideoUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    if (!file.type.startsWith('video/')) {
      toast.error('يرجى اختيار ملف فيديو صحيح');
      return;
    }

    if (file.size > 100 * 1024 * 1024) { // 100MB
      toast.error('حجم الفيديو يجب أن يكون أقل من 100 ميجابايت');
      return;
    }

    setUploadingVideo(true);
    setVideoFile(file);

    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', 'video');

    try {
      const response = await api.post('/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      setFormData(prev => ({
        ...prev,
        video: response.data.url
      }));

      toast.success('تم رفع الفيديو بنجاح');
    } catch (error) {
      console.error('Error uploading video:', error);
      toast.error('خطأ في رفع الفيديو');
    } finally {
      setUploadingVideo(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.title.trim() || !formData.description.trim()) {
      toast.error('العنوان والوصف مطلوبان');
      return;
    }

    try {
      setLoading(true);

      // تحضير البيانات للإرسال
      const courseData = {
        ...formData,
        instructor: formData.instructor || user.name,
        price: parseFloat(formData.price) || 0,
        duration: parseInt(formData.duration) || 0,
        prerequisites: formData.prerequisites.split('\n').filter(p => p.trim()),
        learningOutcomes: formData.learningOutcomes.split('\n').filter(o => o.trim()),
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag)
      };

      const response = await api.post('/courses', courseData);

      toast.success('تم إنشاء الدورة بنجاح!');
      router.push(`/admin/courses/${response.data.course._id}/manage`);

    } catch (error) {
      console.error('Error creating course:', error);
      const errorMessage = error.response?.data?.message || 'خطأ في إنشاء الدورة';
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <ProtectedRoute allowedRoles={['admin', 'super-admin']}>
      <div className="container-fluid py-4">
        {/* Header */}
        <div className="row mb-4">
          <div className="col-12">
            <div className="d-flex justify-content-between align-items-center">
              <div>
                <h2 className="mb-1">إضافة دورة جديدة</h2>
                <p className="text-muted mb-0">
                  أنشئ دورة تدريبية جديدة وأضف المحتوى التعليمي
                </p>
              </div>
              <button
                type="button"
                className="btn btn-outline-secondary"
                onClick={() => router.back()}
              >
                <X className="w-4 h-4 me-1" />
                إلغاء
              </button>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="row">
            {/* Main Form */}
            <div className="col-lg-8">
              {/* Basic Information */}
              <div className="card mb-4">
                <div className="card-header">
                  <h5 className="mb-0">
                    <BookOpen className="w-5 h-5 me-2" />
                    المعلومات الأساسية
                  </h5>
                </div>
                <div className="card-body">
                  <div className="row">
                    <div className="col-12 mb-3">
                      <label className="form-label">عنوان الدورة *</label>
                      <input
                        type="text"
                        className="form-control"
                        name="title"
                        value={formData.title}
                        onChange={handleInputChange}
                        placeholder="أدخل عنوان الدورة"
                        required
                      />
                    </div>

                    <div className="col-12 mb-3">
                      <label className="form-label">وصف الدورة *</label>
                      <textarea
                        className="form-control"
                        name="description"
                        value={formData.description}
                        onChange={handleInputChange}
                        rows="4"
                        placeholder="وصف مفصل للدورة وما ستتعلمه"
                        required
                      ></textarea>
                    </div>
                    <div className="col-md-6 mb-3">
                      <label className="form-label">المدرب</label>
                      <input
                        type="text"
                        className="form-control"
                        name="instructor"
                        value={formData.instructor}
                        onChange={handleInputChange}
                        placeholder={user?.name || 'اسم المدرب'}
                      />
                    </div>

                    <div className="col-md-6 mb-3">
                      <label className="form-label">الفئة</label>
                      <select
                        className="form-select"
                        name="category"
                        value={formData.category}
                        onChange={handleInputChange}
                      >
                        <option value="">اختر الفئة</option>
                        {categories.map(category => (
                          <option key={category} value={category}>
                            {category}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div className="col-md-4 mb-3">
                      <label className="form-label">المستوى</label>
                      <select
                        className="form-select"
                        name="level"
                        value={formData.level}
                        onChange={handleInputChange}
                      >
                        {levels.map(level => (
                          <option key={level.value} value={level.value}>
                            {level.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div className="col-md-4 mb-3">
                      <label className="form-label">المدة (ساعة)</label>
                      <input
                        type="number"
                        className="form-control"
                        name="duration"
                        value={formData.duration}
                        onChange={handleInputChange}
                        min="0"
                        placeholder="0"
                      />
                    </div>

                    <div className="col-md-4 mb-3">
                      <label className="form-label">السعر (ريال)</label>
                      <input
                        type="number"
                        className="form-control"
                        name="price"
                        value={formData.price}
                        onChange={handleInputChange}
                        min="0"
                        step="0.01"
                        placeholder="0.00"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Additional Information */}
              <div className="card mb-4">
                <div className="card-header">
                  <h5 className="mb-0">
                    <FileText className="w-5 h-5 me-2" />
                    معلومات إضافية
                  </h5>
                </div>
                <div className="card-body">
                  <div className="row">
                    <div className="col-12 mb-3">
                      <label className="form-label">صورة الدورة</label>
                      <div className="row">
                        <div className="col-md-6">
                          <input
                            type="file"
                            className="form-control"
                            accept="image/*"
                            onChange={handleImageUpload}
                            disabled={uploadingImage}
                          />
                          <small className="text-muted">أو</small>
                        </div>
                        <div className="col-md-6">
                          <input
                            type="url"
                            className="form-control"
                            name="image"
                            value={formData.image}
                            onChange={handleInputChange}
                            placeholder="https://example.com/image.jpg"
                            disabled={uploadingImage}
                          />
                        </div>
                      </div>
                      {uploadingImage && (
                        <div className="mt-2">
                          <div className="spinner-border spinner-border-sm me-2" role="status"></div>
                          جاري رفع الصورة...
                        </div>
                      )}
                    </div>

                    <div className="col-12 mb-3">
                      <label className="form-label">فيديو تعريفي للدورة</label>
                      <div className="row">
                        <div className="col-md-6">
                          <input
                            type="file"
                            className="form-control"
                            accept="video/*"
                            onChange={handleVideoUpload}
                            disabled={uploadingVideo}
                          />
                          <small className="text-muted">أو</small>
                        </div>
                        <div className="col-md-6">
                          <input
                            type="url"
                            className="form-control"
                            name="video"
                            value={formData.video}
                            onChange={handleInputChange}
                            placeholder="https://example.com/video.mp4"
                            disabled={uploadingVideo}
                          />
                        </div>
                      </div>
                      {uploadingVideo && (
                        <div className="mt-2">
                          <div className="spinner-border spinner-border-sm me-2" role="status"></div>
                          جاري رفع الفيديو...
                        </div>
                      )}
                    </div>

                    <div className="col-12 mb-3">
                      <label className="form-label">المتطلبات المسبقة</label>
                      <textarea
                        className="form-control"
                        name="prerequisites"
                        value={formData.prerequisites}
                        onChange={handleInputChange}
                        rows="3"
                        placeholder="أدخل كل متطلب في سطر منفصل"
                      ></textarea>
                      <small className="text-muted">أدخل كل متطلب في سطر منفصل</small>
                    </div>

                    <div className="col-12 mb-3">
                      <label className="form-label">مخرجات التعلم</label>
                      <textarea
                        className="form-control"
                        name="learningOutcomes"
                        value={formData.learningOutcomes}
                        onChange={handleInputChange}
                        rows="4"
                        placeholder="أدخل كل مخرج تعليمي في سطر منفصل"
                      ></textarea>
                      <small className="text-muted">أدخل كل مخرج تعليمي في سطر منفصل</small>
                    </div>

                    <div className="col-md-6 mb-3">
                      <label className="form-label">اللغة</label>
                      <select
                        className="form-select"
                        name="language"
                        value={formData.language}
                        onChange={handleInputChange}
                      >
                        <option value="ar">العربية</option>
                        <option value="en">الإنجليزية</option>
                        <option value="fr">الفرنسية</option>
                      </select>
                    </div>

                    <div className="col-md-6 mb-3">
                      <label className="form-label">الكلمات المفتاحية</label>
                      <input
                        type="text"
                        className="form-control"
                        name="tags"
                        value={formData.tags}
                        onChange={handleInputChange}
                        placeholder="برمجة, تطوير, ويب"
                      />
                      <small className="text-muted">افصل بين الكلمات بفاصلة</small>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="col-lg-4">
              {/* Settings */}
              <div className="card mb-4">
                <div className="card-header">
                  <h5 className="mb-0">الإعدادات</h5>
                </div>
                <div className="card-body">
                  <div className="mb-3">
                    <div className="form-check">
                      <input
                        className="form-check-input"
                        type="checkbox"
                        name="isActive"
                        checked={formData.isActive}
                        onChange={handleInputChange}
                      />
                      <label className="form-check-label">
                        <Eye className="w-4 h-4 me-1" />
                        دورة نشطة
                      </label>
                    </div>
                    <small className="text-muted">الدورات النشطة تظهر للطلاب</small>
                  </div>

                  <div className="mb-3">
                    <div className="form-check">
                      <input
                        className="form-check-input"
                        type="checkbox"
                        name="isFeatured"
                        checked={formData.isFeatured}
                        onChange={handleInputChange}
                      />
                      <label className="form-check-label">
                        <Star className="w-4 h-4 me-1" />
                        دورة مميزة
                      </label>
                    </div>
                    <small className="text-muted">الدورات المميزة تظهر في المقدمة</small>
                  </div>
                </div>
              </div>

              {/* Preview */}
              {formData.image && (
                <div className="card mb-4">
                  <div className="card-header">
                    <h5 className="mb-0">معاينة الصورة</h5>
                  </div>
                  <div className="card-body p-0">
                    <img
                      src={formData.image}
                      alt="معاينة"
                      className="img-fluid w-100"
                      style={{ maxHeight: '200px', objectFit: 'cover' }}
                      onError={(e) => {
                        e.target.style.display = 'none';
                      }}
                    />
                  </div>
                </div>
              )}

              {/* Video Preview */}
              {formData.video && (
                <div className="card mb-4">
                  <div className="card-header">
                    <h5 className="mb-0">معاينة الفيديو التعريفي</h5>
                  </div>
                  <div className="card-body p-0">
                    <video
                      src={formData.video}
                      controls
                      className="w-100"
                      style={{ maxHeight: '300px' }}
                      onError={(e) => {
                        e.target.style.display = 'none';
                      }}
                    >
                      متصفحك لا يدعم تشغيل الفيديو
                    </video>
                  </div>
                </div>
              )}

              {/* Actions */}
              <div className="card">
                <div className="card-body">
                  <div className="d-grid gap-2">
                    <button
                      type="submit"
                      className="btn btn-primary"
                      disabled={loading}
                    >
                      {loading ? (
                        <>
                          <div className="spinner-border spinner-border-sm me-2" role="status"></div>
                          جاري الإنشاء...
                        </>
                      ) : (
                        <>
                          <Save className="w-4 h-4 me-1" />
                          إنشاء الدورة
                        </>
                      )}
                    </button>

                    <button
                      type="button"
                      className="btn btn-outline-secondary"
                      onClick={() => router.back()}
                    >
                      <X className="w-4 h-4 me-1" />
                      إلغاء
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </ProtectedRoute>
  );
}
