# حل سريع لمشكلة PostCSS

## 🚨 المشكلة
```
Error: Your custom PostCSS configuration must export a `plugins` key.
```

## ✅ الحل

### 1. تثبيت المتطلبات المفقودة
```bash
cd academy-nextjs
npm install tailwindcss postcss autoprefixer
```

### 2. تشغيل الأوامر التالية
```bash
# تنظيف cache
rm -rf .next
rm -rf node_modules
rm package-lock.json

# إعادة تثبيت
npm install

# تشغيل المشروع
npm run dev
```

### 3. إذا استمرت المشكلة
احذف الملفات التالية وأعد إنشاءها:
- `postcss.config.js`
- `tailwind.config.js`

## 🔧 الملفات المطلوبة

### postcss.config.js
```javascript
module.exports = {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
}
```

### tailwind.config.js
```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {},
  },
  plugins: [],
}
```

## 🚀 بديل بدون Tailwind

إذا كنت لا تريد استخدام Tailwind CSS، يمكنك:

### 1. حذف الملفات
```bash
rm postcss.config.js
rm tailwind.config.js
```

### 2. تحديث globals.css
احذف هذه الأسطر من `app/globals.css`:
```css
@tailwind base;
@tailwind components;
@tailwind utilities;
```

### 3. تحديث package.json
احذف من devDependencies:
```json
"autoprefixer": "^10.4.16",
"postcss": "^8.4.32",
"tailwindcss": "^3.4.0",
```

## 📝 ملاحظات
- المشروع يستخدم React Bootstrap بشكل أساسي
- Tailwind CSS اختياري ويمكن إزالته
- تأكد من تشغيل `npm install` بعد أي تغييرات
