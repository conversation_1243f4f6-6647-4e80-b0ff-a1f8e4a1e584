import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

const ProtectedRoute = ({
  children,
  adminOnly = false,
  superAdminOnly = false,
  adminOrSuperAdmin = false
}) => {
  const { isAuthenticated, isAdmin, isSuperAdmin, loading, user } = useAuth();

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '50vh' }}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  // Super Admin Only - فقط المدير العام
  if (superAdminOnly && !isSuperAdmin) {
    return <Navigate to="/" replace />;
  }

  // Admin Only - فقط المدير العادي (لا يشمل Super Admin)
  if (adminOnly && !superAdminOnly && user?.role !== 'admin') {
    return <Navigate to="/" replace />;
  }

  // Admin or Super Admin - المدير العادي أو المدير العام
  if (adminOrSuperAdmin && !isAdmin && !isSuperAdmin) {
    return <Navigate to="/" replace />;
  }

  return children;
};

export default ProtectedRoute;
