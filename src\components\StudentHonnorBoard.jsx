import React, { useEffect, useState  } from 'react';
import { useNavigate } from 'react-router-dom';
import { Table } from 'react-bootstrap';
import { userAPI } from '../services/api';


const StudentHonnorBoard = ({ honorBoard = [], isDark = false }) => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const response = await userAPI.getUsers();
        setUsers(response.data);
      } catch (err) {
        setError('فشل في تحميل المستخدمين');
      } finally {
        setLoading(false);
      }
    };
    fetchUsers();
  }, []);

  return (
    <div className="mt-5">
      <h4 className="fw-bold mb-3 text-center">جميع الطلاب المسجلين</h4>
      {error && (
        <div className="text-danger text-center mb-3">{error}</div>
      )}
      <Table responsive hover className={`mb-0 ${isDark ? 'table-dark' : ''}`}>
        <thead className={isDark ? 'table-secondary' : 'table-light'}>
          <tr>
            <th>#</th>
            <th>اسم الطالب</th>
            <th>النقاط</th>
            <th>آخر دورة حضرها</th>
          </tr>
        </thead>
        <tbody>
          {loading ? (
            <tr>
              <td colSpan={4} className="text-center">جاري التحميل...</td>
            </tr>
          ) : users.length > 0 ? (
            users.map((user, idx) => {
              const student = honorBoard.find(s => s.userId._id === user._id);
              const totalPoints = student ? student.totalPoints : 0;
              const completedCourses = student ? student.completedCourses : [];
              return (
                <tr key={user._id}>
                  <td>{idx + 1}</td>
                  <td>
                    <div className="fw-semibold">
                      <span
                        className="text-primary cursor-pointer"
                        onClick={() => navigate(`/profile/${user._id}`)}
                      >
                        {user.name}
                      </span>
                    </div>
                    <small className="text-muted">{user.email}</small>
                  </td>
                  <td>
                    <span className={`fw-bold ${isDark ? 'text-info' : 'text-primary'}`}>
                      {totalPoints}
                    </span>
                  </td>
                  <td>
                    {completedCourses.length > 0 ? (
                      <span>
                        {
                          completedCourses
                            .sort((a, b) => new Date(b.completedAt) - new Date(a.completedAt))[0]
                            .courseTitle || 'غير متوفر'
                        }
                        <br />
                        <small className="text-muted">
                          {new Date(
                            completedCourses
                              .sort((a, b) => new Date(b.completedAt) - new Date(a.completedAt))[0]
                              .completedAt
                          ).toLocaleDateString('ar-SA')}
                        </small>
                      </span>
                    ) : (
                      <span className="text-muted">لم يحضر أي دورة بعد</span>
                    )}
                  </td>
                </tr>
              );
            })
          ) : (
            <tr>
              <td colSpan={4} className="text-center text-muted">
                لا يوجد طلاب مسجلين بعد.
              </td>
            </tr>
          )}
        </tbody>
      </Table>
    </div>
  );
};

export default StudentHonnorBoard;