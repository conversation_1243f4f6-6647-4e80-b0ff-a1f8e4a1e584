import { NextResponse } from 'next/server';
import { withAuth } from '../../../../lib/auth';
import connectDB from '../../../../lib/mongodb';
import Quiz from '../../../../models/Quiz';
import Question from '../../../../models/Question';
import QuizAttempt from '../../../../models/QuizAttempt';
import Enrollment from '../../../../models/Enrollment';

// GET - جلب اختبار محدد
export const GET = withAuth(async (request, { params }) => {
  try {
    await connectDB();
    
    const { id } = params;
    const { searchParams } = new URL(request.url);
    const includeAnswers = searchParams.get('includeAnswers') === 'true';
    
    const quiz = await Quiz.findById(id)
      .populate('course', 'title instructor')
      .populate('lesson', 'title')
      .populate('questions')
      .populate('createdBy', 'name')
      .lean();
    
    if (!quiz) {
      return NextResponse.json(
        { message: 'الاختبار غير موجود' },
        { status: 404 }
      );
    }

    // التحقق من إمكانية الوصول
    let canAccess = false;
    let enrollment = null;
    let userAttempts = [];

    if (['admin', 'super-admin'].includes(request.user.role)) {
      canAccess = true;
      // للمدراء: إظهار الإجابات الصحيحة
      if (includeAnswers) {
        // الأسئلة مع الإجابات الصحيحة
      }
    } else {
      // للطلاب: التحقق من التسجيل والنشر
      enrollment = await Enrollment.findOne({
        student: request.user._id,
        course: quiz.course._id,
        isActive: true
      });
      
      canAccess = enrollment && quiz.isPublished && quiz.isActive;
      
      if (canAccess) {
        // جلب محاولات المستخدم السابقة
        userAttempts = await QuizAttempt.find({
          quiz: id,
          student: request.user._id
        }).sort({ attemptNumber: -1 }).lean();
        
        // إخفاء الإجابات الصحيحة للطلاب (حسب إعدادات الاختبار)
        if (!quiz.settings.showCorrectAnswers) {
          quiz.questions = quiz.questions.map(q => ({
            ...q,
            correctAnswer: undefined,
            options: q.options?.map(opt => ({
              ...opt,
              isCorrect: undefined
            }))
          }));
        }
      }
    }

    if (!canAccess) {
      return NextResponse.json(
        { message: 'ليس لديك صلاحية لعرض هذا الاختبار' },
        { status: 403 }
      );
    }

    // حساب النقاط الإجمالية
    const totalPoints = quiz.questions.reduce(
      (sum, question) => sum + (question.points || 1), 0
    );

    // التحقق من إمكانية أداء الاختبار
    const canTakeQuiz = quiz.isPublished && 
                       quiz.isActive && 
                       (!quiz.availableFrom || new Date() >= quiz.availableFrom) &&
                       (!quiz.availableUntil || new Date() <= quiz.availableUntil) &&
                       (userAttempts.length < quiz.settings.attempts);

    return NextResponse.json({
      quiz: {
        ...quiz,
        totalPoints,
        questionsCount: quiz.questions.length
      },
      userAttempts: userAttempts.map(attempt => ({
        _id: attempt._id,
        attemptNumber: attempt.attemptNumber,
        score: attempt.score,
        percentage: attempt.percentage,
        passed: attempt.passed,
        status: attempt.status,
        submittedAt: attempt.submittedAt,
        timeSpent: attempt.timeSpent
      })),
      canTakeQuiz,
      remainingAttempts: Math.max(0, quiz.settings.attempts - userAttempts.length)
    }, { status: 200 });

  } catch (error) {
    console.error('Quiz fetch error:', error);
    return NextResponse.json(
      { message: 'خطأ في جلب الاختبار' },
      { status: 500 }
    );
  }
});

// PUT - تحديث اختبار
export const PUT = withAuth(async (request, { params }) => {
  try {
    await connectDB();
    
    const { id } = params;
    const data = await request.json();
    
    const quiz = await Quiz.findById(id);
    if (!quiz) {
      return NextResponse.json(
        { message: 'الاختبار غير موجود' },
        { status: 404 }
      );
    }

    // التحقق من الصلاحية
    if (!['admin', 'super-admin'].includes(request.user.role)) {
      return NextResponse.json(
        { message: 'ليس لديك صلاحية لتحديث الاختبارات' },
        { status: 403 }
      );
    }

    // الحقول المسموح بتحديثها
    const allowedFields = [
      'title', 'description', 'type', 'settings', 
      'availableFrom', 'availableUntil', 'isActive', 'isPublished'
    ];
    
    allowedFields.forEach(field => {
      if (data[field] !== undefined) {
        if (field === 'availableFrom' || field === 'availableUntil') {
          quiz[field] = data[field] ? new Date(data[field]) : undefined;
        } else {
          quiz[field] = data[field];
        }
      }
    });

    quiz.updatedBy = request.user._id;
    await quiz.save();

    const updatedQuiz = await Quiz.findById(id)
      .populate('course', 'title')
      .populate('lesson', 'title')
      .populate('questions')
      .populate('updatedBy', 'name');

    return NextResponse.json({
      message: 'تم تحديث الاختبار بنجاح',
      quiz: updatedQuiz
    }, { status: 200 });

  } catch (error) {
    console.error('Quiz update error:', error);
    return NextResponse.json(
      { message: 'خطأ في تحديث الاختبار' },
      { status: 500 }
    );
  }
}, { roles: ['admin', 'super-admin'] });

// DELETE - حذف اختبار
export const DELETE = withAuth(async (request, { params }) => {
  try {
    await connectDB();
    
    const { id } = params;
    
    const quiz = await Quiz.findById(id);
    if (!quiz) {
      return NextResponse.json(
        { message: 'الاختبار غير موجود' },
        { status: 404 }
      );
    }

    // التحقق من الصلاحية
    if (!['admin', 'super-admin'].includes(request.user.role)) {
      return NextResponse.json(
        { message: 'ليس لديك صلاحية لحذف الاختبارات' },
        { status: 403 }
      );
    }

    // حذف الاختبار والأسئلة المرتبطة (soft delete)
    quiz.isActive = false;
    quiz.updatedBy = request.user._id;
    await quiz.save();

    // إلغاء تفعيل الأسئلة المرتبطة
    await Question.updateMany(
      { quiz: id },
      { isActive: false }
    );

    return NextResponse.json({
      message: 'تم حذف الاختبار بنجاح'
    }, { status: 200 });

  } catch (error) {
    console.error('Quiz deletion error:', error);
    return NextResponse.json(
      { message: 'خطأ في حذف الاختبار' },
      { status: 500 }
    );
  }
}, { roles: ['admin', 'super-admin'] });
