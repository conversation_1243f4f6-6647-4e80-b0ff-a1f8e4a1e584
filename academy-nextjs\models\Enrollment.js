import mongoose from 'mongoose';

const enrollmentSchema = new mongoose.Schema({
  student: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  course: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Course',
    required: true
  },
  enrolledAt: {
    type: Date,
    default: Date.now
  },
  progress: {
    type: Number,
    default: 0,
    min: 0,
    max: 100
  },
  // تقدم الوحدات والدروس
  unitsProgress: [{
    unitId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true
    },
    unitIndex: Number,
    isUnlocked: {
      type: Boolean,
      default: false
    },
    lessons: [{
      lessonId: {
        type: mongoose.Schema.Types.ObjectId,
        required: true
      },
      lessonIndex: Number,
      completed: {
        type: Boolean,
        default: false
      },
      completedAt: Date,
      timeSpent: {
        type: Number,
        default: 0 // بالثواني
      }
    }],
    quizCompleted: {
      type: Boolean,
      default: false
    },
    quizScore: Number,
    quizAttempts: {
      type: Number,
      default: 0
    },
    quizCompletedAt: Date,
    unitCompleted: {
      type: Boolean,
      default: false
    },
    unitCompletedAt: Date
  }],
  currentUnit: {
    type: Number,
    default: 0
  },
  currentLesson: {
    type: Number,
    default: 0
  },
  lastAccessedAt: {
    type: Date,
    default: Date.now
  },
  isCompleted: {
    type: Boolean,
    default: false
  },
  completedAt: Date,
  finalScore: {
    type: Number,
    min: 0,
    max: 100
  },
  certificateIssued: {
    type: Boolean,
    default: false
  },
  certificateId: String,
  // إحصائيات التعلم
  studyTime: {
    totalMinutes: { type: Number, default: 0 },
    sessionsCount: { type: Number, default: 0 },
    averageSessionTime: { type: Number, default: 0 }
  },
  // تقييم الدورة
  rating: {
    score: { type: Number, min: 1, max: 5 },
    review: String,
    ratedAt: Date
  },
  // ملاحظات المدرب
  instructorNotes: [{
    note: String,
    addedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    addedAt: { type: Date, default: Date.now }
  }]
}, {
  timestamps: true
});

// فهارس للأداء
enrollmentSchema.index({ student: 1, course: 1 }, { unique: true });
enrollmentSchema.index({ student: 1, isCompleted: 1 });
enrollmentSchema.index({ course: 1, isCompleted: 1 });
enrollmentSchema.index({ enrolledAt: -1 });

// حساب التقدم تلقائياً
enrollmentSchema.methods.calculateProgress = function() {
  if (!this.unitsProgress || this.unitsProgress.length === 0) {
    return 0;
  }

  let totalLessons = 0;
  let completedLessons = 0;

  this.unitsProgress.forEach(unit => {
    totalLessons += unit.lessons.length;
    completedLessons += unit.lessons.filter(lesson => lesson.completed).length;
  });

  if (totalLessons === 0) return 0;

  this.progress = Math.round((completedLessons / totalLessons) * 100);
  return this.progress;
};

// تحديث وقت الوصول الأخير
enrollmentSchema.methods.updateLastAccessed = function() {
  this.lastAccessedAt = new Date();
  return this.save();
};

// إكمال درس
enrollmentSchema.methods.completeLesson = function(unitIndex, lessonIndex, timeSpent = 0) {
  const unit = this.unitsProgress[unitIndex];
  if (!unit) return false;

  const lesson = unit.lessons[lessonIndex];
  if (!lesson) return false;

  if (!lesson.completed) {
    lesson.completed = true;
    lesson.completedAt = new Date();
    lesson.timeSpent = timeSpent;

    // تحديث إجمالي وقت الدراسة
    this.studyTime.totalMinutes += Math.round(timeSpent / 60);
    this.studyTime.sessionsCount += 1;
    this.studyTime.averageSessionTime = this.studyTime.totalMinutes / this.studyTime.sessionsCount;
  }

  this.calculateProgress();
  this.updateLastAccessed();

  return this.save();
};

// إكمال اختبار الوحدة
enrollmentSchema.methods.completeUnitQuiz = function(unitIndex, score) {
  const unit = this.unitsProgress[unitIndex];
  if (!unit) return false;

  unit.quizAttempts += 1;
  unit.quizScore = score;
  unit.quizCompletedAt = new Date();

  // إذا نجح في الاختبار (70% أو أكثر)
  if (score >= 70) {
    unit.quizCompleted = true;
    unit.unitCompleted = true;
    unit.unitCompletedAt = new Date();

    // فتح الوحدة التالية
    if (unitIndex + 1 < this.unitsProgress.length) {
      this.unitsProgress[unitIndex + 1].isUnlocked = true;
      this.currentUnit = unitIndex + 1;
    }
  }

  this.calculateProgress();
  return this.save();
};

// إكمال الدورة
enrollmentSchema.methods.completeCourse = function(finalScore = null) {
  this.isCompleted = true;
  this.completedAt = new Date();
  this.progress = 100;
  
  if (finalScore !== null) {
    this.finalScore = finalScore;
  }
  
  return this.save();
};

export default mongoose.models.Enrollment || mongoose.model('Enrollment', enrollmentSchema);
