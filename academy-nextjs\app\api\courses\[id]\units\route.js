import { NextResponse } from 'next/server';
import { verifyToken } from '../../../../../lib/auth';
import { connectDB } from '../../../../../lib/mongodb';
import Course from '../../../../../models/Course';

// GET - جلب وحدات الدورة
export async function GET(request, { params }) {
  try {
    await connectDB();

    const { id: courseId } = params;

    // التحقق من وجود الدورة
    const course = await Course.findById(courseId).select('units title');
    if (!course) {
      return NextResponse.json(
        { message: 'الدورة غير موجودة' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      units: course.units || [],
      courseTitle: course.title
    }, { status: 200 });

  } catch (error) {
    console.error('Units fetch error:', error);
    return NextResponse.json(
      { message: 'خطأ في جلب وحدات الدورة' },
      { status: 500 }
    );
  }
}

// POST - إضافة وحدة جديدة
export async function POST(request, { params }) {
  try {
    await connectDB();

    // التحقق من صحة التوكن والصلاحيات
    const authResult = await verifyToken(request);
    if (!authResult.success) {
      return NextResponse.json(
        { message: authResult.message },
        { status: 401 }
      );
    }

    const { user } = authResult;
    if (!['admin', 'super-admin'].includes(user.role)) {
      return NextResponse.json(
        { message: 'غير مصرح لك بهذا الإجراء' },
        { status: 403 }
      );
    }

    const { id: courseId } = params;
    const { title, description } = await request.json();

    if (!title) {
      return NextResponse.json(
        { message: 'عنوان الوحدة مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من وجود الدورة
    const course = await Course.findById(courseId);
    if (!course) {
      return NextResponse.json(
        { message: 'الدورة غير موجودة' },
        { status: 404 }
      );
    }

    // إنشاء الوحدة الجديدة
    const newUnit = {
      title,
      description: description || '',
      lessons: [],
      quiz: {
        title: 'اختبار الوحدة',
        questions: [],
        passingScore: 70,
        timeLimit: 30
      },
      order: course.units.length
    };

    course.units.push(newUnit);
    course.updatedBy = user._id;

    await course.save();

    const addedUnit = course.units[course.units.length - 1];

    return NextResponse.json({
      success: true,
      message: 'تم إضافة الوحدة بنجاح',
      unit: addedUnit
    }, { status: 201 });

  } catch (error) {
    console.error('Unit creation error:', error);
    return NextResponse.json(
      { message: 'خطأ في إضافة الوحدة' },
      { status: 500 }
    );
  }
}
