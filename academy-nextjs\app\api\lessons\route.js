import { NextResponse } from 'next/server';
import { withAuth } from '../../../lib/auth';
import connectDB from '../../../lib/mongodb';
import Lesson from '../../../models/Lesson';
import Course from '../../../models/Course';

// GET - جلب الدروس
export const GET = withAuth(async (request) => {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const courseId = searchParams.get('courseId');
    const unitId = searchParams.get('unitId');
    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 20;
    
    // بناء الاستعلام
    const query = { isActive: true };
    if (courseId) query.course = courseId;
    if (unitId) query.unit = unitId;
    
    const skip = (page - 1) * limit;
    
    const lessons = await Lesson.find(query)
      .populate('course', 'title')
      .populate('quiz', 'title type')
      .populate('createdBy', 'name')
      .sort({ order: 1 })
      .skip(skip)
      .limit(limit)
      .lean();
    
    const total = await Lesson.countDocuments(query);
    
    return NextResponse.json({
      lessons,
      pagination: {
        current: page,
        total: Math.ceil(total / limit),
        count: lessons.length,
        totalLessons: total
      }
    }, { status: 200 });

  } catch (error) {
    console.error('Lessons fetch error:', error);
    return NextResponse.json(
      { message: 'خطأ في جلب الدروس' },
      { status: 500 }
    );
  }
});

// POST - إنشاء درس جديد
export const POST = withAuth(async (request) => {
  try {
    await connectDB();
    
    const data = await request.json();
    const {
      title,
      description,
      courseId,
      unitId,
      type = 'text',
      content = {},
      duration = 0,
      isPreview = false,
      resources = [],
      completionCriteria = 'view',
      minViewTime = 0
    } = data;

    // التحقق من البيانات المطلوبة
    if (!title || !description || !courseId) {
      return NextResponse.json(
        { message: 'العنوان والوصف ومعرف الدورة مطلوبة' },
        { status: 400 }
      );
    }

    // التحقق من وجود الدورة
    const course = await Course.findById(courseId);
    if (!course) {
      return NextResponse.json(
        { message: 'الدورة غير موجودة' },
        { status: 404 }
      );
    }

    // التحقق من الصلاحية
    if (!['admin', 'super-admin'].includes(request.user.role)) {
      return NextResponse.json(
        { message: 'ليس لديك صلاحية لإنشاء دروس' },
        { status: 403 }
      );
    }

    // حساب الترتيب التالي
    const lastLesson = await Lesson.findOne({ 
      course: courseId, 
      unit: unitId 
    }).sort({ order: -1 });
    
    const order = lastLesson ? lastLesson.order + 1 : 0;

    // إنشاء الدرس
    const lesson = new Lesson({
      title,
      description,
      course: courseId,
      unit: unitId,
      order,
      type,
      content,
      duration,
      isPreview,
      resources,
      completionCriteria,
      minViewTime,
      createdBy: request.user._id
    });

    await lesson.save();

    // جلب الدرس مع البيانات المرتبطة
    const populatedLesson = await Lesson.findById(lesson._id)
      .populate('course', 'title')
      .populate('createdBy', 'name');

    return NextResponse.json({
      message: 'تم إنشاء الدرس بنجاح',
      lesson: populatedLesson
    }, { status: 201 });

  } catch (error) {
    console.error('Lesson creation error:', error);
    
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(err => err.message);
      return NextResponse.json(
        { message: messages.join(', ') },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: 'خطأ في إنشاء الدرس' },
      { status: 500 }
    );
  }
}, { roles: ['admin', 'super-admin'] });
