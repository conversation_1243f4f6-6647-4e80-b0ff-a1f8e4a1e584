# تكوين API - Academy Project

## التغييرات المطبقة

### 1. تحديث عناوين API

تم تحديث التطبيق ليستخدم:
- **التطوير**: `http://localhost:5000`
- **الإنتاج**: `https://academy-project-backend.onrender.com`

### 2. الملفات المحدثة

#### `src/services/api.js`
- تحديث دالة `getApiUrl()` لاستخدام الرابط الجديد
- إضافة كشف تلقائي لبيئة التطوير باستخدام `import.meta.env.DEV`

#### `src/config/api.js`
- تحديث دالة `getBaseUrl()` 
- إضافة دعم للتكوين الجديد

#### `src/config/environment.js` (جديد)
- ملف تكوين شامل للبيئات
- دوال مساعدة لتحديد البيئة الحالية
- معلومات تشخيصية

#### `.env`
- تحديث متغير `VITE_API_URL` للإنتاج

#### `src/pages/Courses.jsx`
- إزالة التعريف الخاطئ لـ `API_URI`
- استخدام `API_CONFIG` بدلاً من ذلك

#### `src/pages/Admin/ManageCourses.jsx`
- تحسين معالجة استجابة API
- إضافة معلومات تشخيصية في وضع التطوير
- تحسين رسائل الخطأ

### 3. كيفية العمل

#### في بيئة التطوير:
```bash
npm run dev
```
سيستخدم التطبيق تلقائياً `http://localhost:5000`

#### في بيئة الإنتاج:
سيستخدم التطبيق تلقائياً `https://academy-project-backend.onrender.com`

### 4. التحقق من الاتصال

تم اختبار الخادم الجديد:
- ✅ Health check: `https://academy-project-backend.onrender.com/api/health`
- ✅ Courses API: `https://academy-project-backend.onrender.com/api/courses`

### 5. معلومات إضافية

- الخادم يدعم CORS بشكل صحيح
- تم العثور على 4 دورات في قاعدة البيانات
- API يعمل بشكل طبيعي

### 6. استكشاف الأخطاء

إذا لم تظهر الدورات:
1. تحقق من وحدة تحكم المتصفح للأخطاء
2. تأكد من تسجيل الدخول بصلاحيات مدير
3. تحقق من معلومات التشخيص في وضع التطوير
