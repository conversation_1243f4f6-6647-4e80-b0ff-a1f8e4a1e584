import { NextResponse } from 'next/server';
import { verifyToken } from '../../../../../lib/auth';
import { connectDB } from '../../../../../lib/mongodb';
import Course from '../../../../../models/Course';
import Enrollment from '../../../../../models/Enrollment';

// GET - جلب تقدم الطالب في الدورة
export async function GET(request, { params }) {
  try {
    await connectDB();
    
    // التحقق من صحة التوكن
    const authResult = await verifyToken(request);
    if (!authResult.success) {
      return NextResponse.json(
        { message: authResult.message },
        { status: 401 }
      );
    }

    const { user } = authResult;
    const { id: courseId } = params;

    // جلب الدورة
    const course = await Course.findById(courseId);
    if (!course) {
      return NextResponse.json(
        { message: 'الدورة غير موجودة' },
        { status: 404 }
      );
    }

    // جلب تقدم الطالب
    let enrollment = await Enrollment.findOne({
      student: user._id,
      course: courseId
    });

    // إذا لم يكن مسجلاً، إنشاء تسجيل جديد
    if (!enrollment) {
      enrollment = new Enrollment({
        student: user._id,
        course: courseId,
        unitsProgress: course.units.map((unit, unitIndex) => ({
          unitId: unit._id,
          unitIndex,
          isUnlocked: unitIndex === 0, // فقط الوحدة الأولى مفتوحة
          lessons: unit.lessons.map((lesson, lessonIndex) => ({
            lessonId: lesson._id,
            lessonIndex,
            completed: false
          })),
          quizCompleted: false,
          quizAttempts: 0,
          unitCompleted: false
        }))
      });
      
      await enrollment.save();
    }

    // التأكد من تطابق الوحدات مع الدورة (في حالة إضافة وحدات جديدة)
    if (enrollment.unitsProgress.length < course.units.length) {
      for (let i = enrollment.unitsProgress.length; i < course.units.length; i++) {
        const unit = course.units[i];
        enrollment.unitsProgress.push({
          unitId: unit._id,
          unitIndex: i,
          isUnlocked: false,
          lessons: unit.lessons.map((lesson, lessonIndex) => ({
            lessonId: lesson._id,
            lessonIndex,
            completed: false
          })),
          quizCompleted: false,
          quizAttempts: 0,
          unitCompleted: false
        });
      }
      await enrollment.save();
    }

    return NextResponse.json({
      success: true,
      progress: enrollment.progress,
      currentUnit: enrollment.currentUnit,
      unitsProgress: enrollment.unitsProgress,
      totalTimeSpent: enrollment.studyTime?.totalMinutes || 0,
      isCompleted: enrollment.isCompleted
    });

  } catch (error) {
    console.error('Error fetching progress:', error);
    return NextResponse.json(
      { message: 'خطأ في جلب التقدم' },
      { status: 500 }
    );
  }
}

// POST - تحديث تقدم الطالب (إكمال درس)
export async function POST(request, { params }) {
  try {
    await connectDB();
    
    // التحقق من صحة التوكن
    const authResult = await verifyToken(request);
    if (!authResult.success) {
      return NextResponse.json(
        { message: authResult.message },
        { status: 401 }
      );
    }

    const { user } = authResult;
    const { id: courseId } = params;
    const { unitIndex, lessonIndex, timeSpent } = await request.json();

    // جلب التسجيل
    const enrollment = await Enrollment.findOne({
      student: user._id,
      course: courseId
    });

    if (!enrollment) {
      return NextResponse.json(
        { message: 'يجب التسجيل في الدورة أولاً' },
        { status: 403 }
      );
    }

    // التحقق من أن الوحدة مفتوحة
    const unitProgress = enrollment.unitsProgress[unitIndex];
    if (!unitProgress || !unitProgress.isUnlocked) {
      return NextResponse.json(
        { message: 'هذه الوحدة غير متاحة بعد' },
        { status: 403 }
      );
    }

    // إكمال الدرس
    await enrollment.completeLesson(unitIndex, lessonIndex, timeSpent || 0);

    return NextResponse.json({
      success: true,
      message: 'تم إكمال الدرس بنجاح',
      progress: enrollment.progress
    });

  } catch (error) {
    console.error('Error updating progress:', error);
    return NextResponse.json(
      { message: 'خطأ في تحديث التقدم' },
      { status: 500 }
    );
  }
}
