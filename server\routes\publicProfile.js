import express from 'express';
import User from '../models/User.js';
import Course from '../models/Course.js';
import { verifyToken } from '../middleware/verifyToken.js';

const router = express.Router();

// Get public profile of a user
router.get('/profile/:id', verifyToken , async (req, res) => {
  // جلب بيانات المستخدم بدون الحاجة للتحقق من التوكن
  const user = await User.findById(req.params.id)
    .select('-password')
    .populate('completedCourses.courseId')
    .populate('certificates.courseId');
  if (!user) return res.status(404).json({ message: 'المستخدم غير موجود' });

  res.json({
    name: user.name,
    email: user.email,
    achievements: user.achievements || [],
    courses: user.completedCourses.map(c => ({
      title: c.courseId?.title || 'دورة غير معروفة'
    })),
    certificates: user.certificates.map(cert => ({
      name: cert.name,
      date: cert.date
    }))
  });
});
export default router;