import { NextResponse } from 'next/server';
import { withAuth } from '../../../../lib/auth';
import connectDB from '../../../../lib/mongodb';
import Lesson from '../../../../models/Lesson';
import Enrollment from '../../../../models/Enrollment';

// GET - جلب درس محدد
export const GET = withAuth(async (request, { params }) => {
  try {
    await connectDB();
    
    const { id } = params;
    
    const lesson = await Lesson.findById(id)
      .populate('course', 'title instructor')
      .populate('quiz')
      .populate('createdBy', 'name')
      .lean();
    
    if (!lesson) {
      return NextResponse.json(
        { message: 'الدرس غير موجود' },
        { status: 404 }
      );
    }

    // التحقق من إمكانية الوصول
    let canAccess = false;
    let enrollment = null;

    if (['admin', 'super-admin'].includes(request.user.role)) {
      canAccess = true;
    } else if (lesson.isPreview) {
      canAccess = true;
    } else {
      // التحقق من التسجيل في الدورة
      enrollment = await Enrollment.findOne({
        student: request.user._id,
        course: lesson.course._id,
        isActive: true
      });
      canAccess = !!enrollment;
    }

    if (!canAccess) {
      return NextResponse.json(
        { message: 'ليس لديك صلاحية لعرض هذا الدرس' },
        { status: 403 }
      );
    }

    // تحديث عدد المشاهدات
    await Lesson.findByIdAndUpdate(id, {
      $inc: { 'stats.views': 1 }
    });

    // جلب الدرس التالي والسابق
    const [nextLesson, previousLesson] = await Promise.all([
      Lesson.findOne({
        course: lesson.course._id,
        unit: lesson.unit,
        order: { $gt: lesson.order },
        isActive: true
      }).select('_id title order').sort({ order: 1 }),
      
      Lesson.findOne({
        course: lesson.course._id,
        unit: lesson.unit,
        order: { $lt: lesson.order },
        isActive: true
      }).select('_id title order').sort({ order: -1 })
    ]);

    return NextResponse.json({
      lesson,
      navigation: {
        next: nextLesson,
        previous: previousLesson
      },
      enrollment: enrollment ? {
        progress: enrollment.progress,
        isCompleted: enrollment.completedLessons.some(
          cl => cl.lessonId === id
        )
      } : null
    }, { status: 200 });

  } catch (error) {
    console.error('Lesson fetch error:', error);
    return NextResponse.json(
      { message: 'خطأ في جلب الدرس' },
      { status: 500 }
    );
  }
});

// PUT - تحديث درس
export const PUT = withAuth(async (request, { params }) => {
  try {
    await connectDB();
    
    const { id } = params;
    const data = await request.json();
    
    const lesson = await Lesson.findById(id);
    if (!lesson) {
      return NextResponse.json(
        { message: 'الدرس غير موجود' },
        { status: 404 }
      );
    }

    // التحقق من الصلاحية
    if (!['admin', 'super-admin'].includes(request.user.role)) {
      return NextResponse.json(
        { message: 'ليس لديك صلاحية لتحديث الدروس' },
        { status: 403 }
      );
    }

    // الحقول المسموح بتحديثها
    const allowedFields = [
      'title', 'description', 'type', 'content', 'duration',
      'isPreview', 'resources', 'completionCriteria', 'minViewTime',
      'isActive'
    ];
    
    allowedFields.forEach(field => {
      if (data[field] !== undefined) {
        lesson[field] = data[field];
      }
    });

    lesson.updatedBy = request.user._id;
    await lesson.save();

    const updatedLesson = await Lesson.findById(id)
      .populate('course', 'title')
      .populate('quiz', 'title type')
      .populate('updatedBy', 'name');

    return NextResponse.json({
      message: 'تم تحديث الدرس بنجاح',
      lesson: updatedLesson
    }, { status: 200 });

  } catch (error) {
    console.error('Lesson update error:', error);
    return NextResponse.json(
      { message: 'خطأ في تحديث الدرس' },
      { status: 500 }
    );
  }
}, { roles: ['admin', 'super-admin'] });

// DELETE - حذف درس
export const DELETE = withAuth(async (request, { params }) => {
  try {
    await connectDB();
    
    const { id } = params;
    
    const lesson = await Lesson.findById(id);
    if (!lesson) {
      return NextResponse.json(
        { message: 'الدرس غير موجود' },
        { status: 404 }
      );
    }

    // التحقق من الصلاحية
    if (!['admin', 'super-admin'].includes(request.user.role)) {
      return NextResponse.json(
        { message: 'ليس لديك صلاحية لحذف الدروس' },
        { status: 403 }
      );
    }

    // حذف الدرس (soft delete)
    lesson.isActive = false;
    lesson.updatedBy = request.user._id;
    await lesson.save();

    return NextResponse.json({
      message: 'تم حذف الدرس بنجاح'
    }, { status: 200 });

  } catch (error) {
    console.error('Lesson deletion error:', error);
    return NextResponse.json(
      { message: 'خطأ في حذف الدرس' },
      { status: 500 }
    );
  }
}, { roles: ['admin', 'super-admin'] });
