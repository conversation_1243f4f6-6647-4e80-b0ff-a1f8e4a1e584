import mongoose from 'mongoose';

const honorBoardSchema = new mongoose.Schema({
  userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  completedCourses: [{
    courseId: { type: mongoose.Schema.Types.ObjectId, ref: 'Course' },
    completedAt: Date,
    level: String
  }],
  totalPoints: { type: Number, default: 0 }
}, { timestamps: true });

export default mongoose.model('HonorBoard', honorBoardSchema); 