import { NextResponse } from 'next/server';
import { withAuth } from '../../../../lib/auth';
import connectDB from '../../../../lib/mongodb';

// GET - الحصول على بيانات المستخدم
export const GET = withAuth(async (request) => {
  try {
    await connectDB();
    
    const user = request.user;
    
    return NextResponse.json({
      message: 'تم جلب البيانات بنجاح',
      user
    }, { status: 200 });

  } catch (error) {
    console.error('Profile fetch error:', error);
    return NextResponse.json(
      { message: 'خطأ في جلب البيانات' },
      { status: 500 }
    );
  }
});

// PUT - تحديث بيانات المستخدم
export const PUT = withAuth(async (request) => {
  try {
    await connectDB();
    
    const { name, preferences } = await request.json();
    const user = request.user;

    // تحديث البيانات المسموح بها فقط
    if (name) user.name = name.trim();
    if (preferences) {
      user.preferences = {
        ...user.preferences,
        ...preferences
      };
    }

    await user.save();

    return NextResponse.json({
      message: 'تم تحديث البيانات بنجاح',
      user: user.toJSON()
    }, { status: 200 });

  } catch (error) {
    console.error('Profile update error:', error);
    return NextResponse.json(
      { message: 'خطأ في تحديث البيانات' },
      { status: 500 }
    );
  }
});
