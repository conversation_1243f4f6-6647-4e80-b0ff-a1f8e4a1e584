import jwt from 'jsonwebtoken';
import { NextResponse } from 'next/server';
import connectDB from './mongodb';
import User from '../models/User';

const JWT_SECRET = process.env.JWT_SECRET;

// إنشاء JWT Token
export function generateToken(payload) {
  return jwt.sign(payload, JWT_SECRET, { expiresIn: '7d' });
}

// التحقق من JWT Token
export function verifyToken(token) {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    return null;
  }
}

// Middleware للتحقق من المصادقة
export async function authenticateUser(request) {
  try {
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return { error: 'Token مطلوب', status: 401 };
    }

    const decoded = verifyToken(token);
    if (!decoded) {
      return { error: 'Token غير صالح', status: 401 };
    }

    await connectDB();
    const user = await User.findById(decoded.id).select('-password');
    
    if (!user || !user.isActive) {
      return { error: 'المستخدم غير موجود أو غير نشط', status: 401 };
    }

    return { user, status: 200 };
  } catch (error) {
    console.error('Authentication error:', error);
    return { error: 'خطأ في المصادقة', status: 500 };
  }
}

// التحقق من الصلاحيات
export function checkRole(user, allowedRoles) {
  if (!user || !allowedRoles.includes(user.role)) {
    return false;
  }
  return true;
}

// Middleware wrapper للـ API routes
export function withAuth(handler, options = {}) {
  return async (request, context) => {
    const authResult = await authenticateUser(request);
    
    if (authResult.error) {
      return NextResponse.json(
        { message: authResult.error },
        { status: authResult.status }
      );
    }

    // التحقق من الصلاحيات إذا كانت محددة
    if (options.roles && !checkRole(authResult.user, options.roles)) {
      return NextResponse.json(
        { message: 'ليس لديك صلاحية للوصول لهذا المورد' },
        { status: 403 }
      );
    }

    // إضافة المستخدم إلى السياق
    request.user = authResult.user;
    
    return handler(request, context);
  };
}

// Helper functions للأدوار
export const roleCheckers = {
  isStudent: (user) => user?.role === 'student',
  isAdmin: (user) => user?.role === 'admin',
  isSuperAdmin: (user) => user?.role === 'super-admin',
  isAdminOrSuperAdmin: (user) => ['admin', 'super-admin'].includes(user?.role),
};
