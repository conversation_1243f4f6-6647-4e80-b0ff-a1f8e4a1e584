import React from 'react';
import { Container, Row, Col, Card, Badge } from 'react-bootstrap';
import { Trophy, Star } from 'lucide-react';

const TestimonialsSection = ({ isDark }) => {
  const testimonials = [
    {
      name: "أحم<PERSON> محمد",
      role: "مطور برمجيات",
      image: "https://via.placeholder.com/80x80/007bff/ffffff?text=أ.م",
      quote: "تعلمت البرمجة من الصفر وحصلت على وظيفة أحلامي في شركة تقنية كبرى",
      rating: 5
    },
    {
      name: "سارة علي",
      role: "مصممة UX/UI",
      image: "https://via.placeholder.com/80x80/28a745/ffffff?text=س.ع",
      quote: "الدورات التفاعلية والذكاء الاصطناعي ساعدني في فهم المفاهيم المعقدة بسهولة",
      rating: 5
    },
    {
      name: "محم<PERSON> حسن",
      role: "مدير مشاريع تقنية",
      image: "https://via.placeholder.com/80x80/ffc107/ffffff?text=م.ح",
      quote: "بفضل الشهادات المعتمدة تمكنت من الحصول على ترقية في عملي",
      rating: 5
    }
  ];

  return (
    <div className={`${isDark ? 'bg-dark' : 'bg-light'} section-spacing py-5`}>
      <Container>
        <Row className="text-center mb-5 animate-fadeInUp">
          <Col>
            <Badge bg="success" className="mb-3 px-3 py-2">
              <Trophy size={16} className="me-2" />
              قصص نجاح
            </Badge>
            <h2 className="display-5 fw-bold mb-3">
              طلابنا يحققون 
              <span className="text-success"> النجاح</span>
            </h2>
            <p className="lead text-muted">
              اكتشف كيف غيرت منصتنا حياة آلاف المتعلمين حول العالم
            </p>
          </Col>
        </Row>

        <Row className="g-4">
          {testimonials.map((testimonial, index) => (
            <Col 
              md={4} 
              key={index}
              className="animate-fadeInUp" 
              style={{ animationDelay: `${0.1 * (index + 1)}s` }}
            >
              <Card className="testimonial-card h-100 text-center shadow-lg">
                <Card.Body className="p-4">
                  <div className="mb-3">
                    <img 
                      src={testimonial.image}
                      alt={testimonial.name}
                      className="rounded-circle mb-3"
                      style={{ width: '80px', height: '80px' }}
                    />
                  </div>
                  <blockquote className="mb-3">
                    <p className="text-muted fst-italic">
                      "{testimonial.quote}"
                    </p>
                  </blockquote>
                  <div>
                    <h6 className="fw-bold mb-1">{testimonial.name}</h6>
                    <small className="text-muted">{testimonial.role}</small>
                  </div>
                  <div className="mt-3">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} size={16} className="text-warning" fill="currentColor" />
                    ))}
                  </div>
                </Card.Body>
              </Card>
            </Col>
          ))}
        </Row>
      </Container>
    </div>
  );
};

export default TestimonialsSection;
