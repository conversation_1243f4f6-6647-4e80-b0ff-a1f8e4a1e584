import { NextResponse } from 'next/server';
import { withAuth } from '../../../lib/auth';
import connectDB from '../../../lib/mongodb';
import Quiz from '../../../models/Quiz';
import Question from '../../../models/Question';
import Course from '../../../models/Course';

// GET - جلب الاختبارات
export const GET = withAuth(async (request) => {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const courseId = searchParams.get('courseId');
    const lessonId = searchParams.get('lessonId');
    const type = searchParams.get('type');
    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 20;
    
    // بناء الاستعلام
    const query = { isActive: true };
    if (courseId) query.course = courseId;
    if (lessonId) query.lesson = lessonId;
    if (type) query.type = type;
    
    // للطلاب: عرض الاختبارات المنشورة فقط
    if (request.user.role === 'student') {
      query.isPublished = true;
    }
    
    const skip = (page - 1) * limit;
    
    const quizzes = await Quiz.find(query)
      .populate('course', 'title')
      .populate('lesson', 'title')
      .populate('createdBy', 'name')
      .populate('questions')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();
    
    // حساب النقاط الإجمالية لكل اختبار
    const quizzesWithPoints = await Promise.all(
      quizzes.map(async (quiz) => {
        const totalPoints = quiz.questions.reduce(
          (sum, question) => sum + (question.points || 1), 0
        );
        return {
          ...quiz,
          totalPoints,
          questionsCount: quiz.questions.length
        };
      })
    );
    
    const total = await Quiz.countDocuments(query);
    
    return NextResponse.json({
      quizzes: quizzesWithPoints,
      pagination: {
        current: page,
        total: Math.ceil(total / limit),
        count: quizzes.length,
        totalQuizzes: total
      }
    }, { status: 200 });

  } catch (error) {
    console.error('Quizzes fetch error:', error);
    return NextResponse.json(
      { message: 'خطأ في جلب الاختبارات' },
      { status: 500 }
    );
  }
});

// POST - إنشاء اختبار جديد
export const POST = withAuth(async (request) => {
  try {
    await connectDB();
    
    const data = await request.json();
    const {
      title,
      description,
      courseId,
      lessonId,
      type = 'practice',
      settings = {},
      questions = [],
      availableFrom,
      availableUntil
    } = data;

    // التحقق من البيانات المطلوبة
    if (!title || !description || !courseId) {
      return NextResponse.json(
        { message: 'العنوان والوصف ومعرف الدورة مطلوبة' },
        { status: 400 }
      );
    }

    // التحقق من وجود الدورة
    const course = await Course.findById(courseId);
    if (!course) {
      return NextResponse.json(
        { message: 'الدورة غير موجودة' },
        { status: 404 }
      );
    }

    // التحقق من الصلاحية
    if (!['admin', 'super-admin'].includes(request.user.role)) {
      return NextResponse.json(
        { message: 'ليس لديك صلاحية لإنشاء اختبارات' },
        { status: 403 }
      );
    }

    // إعدادات افتراضية للاختبار
    const defaultSettings = {
      timeLimit: 0,
      attempts: 1,
      passingScore: 70,
      showResults: 'immediately',
      showCorrectAnswers: true,
      randomizeQuestions: false,
      randomizeAnswers: false,
      ...settings
    };

    // إنشاء الاختبار
    const quiz = new Quiz({
      title,
      description,
      course: courseId,
      lesson: lessonId,
      type,
      settings: defaultSettings,
      availableFrom: availableFrom ? new Date(availableFrom) : undefined,
      availableUntil: availableUntil ? new Date(availableUntil) : undefined,
      createdBy: request.user._id
    });

    await quiz.save();

    // إنشاء الأسئلة
    const createdQuestions = [];
    for (let i = 0; i < questions.length; i++) {
      const questionData = questions[i];
      const question = new Question({
        quiz: quiz._id,
        type: questionData.type,
        question: questionData.question,
        options: questionData.options || [],
        correctAnswer: questionData.correctAnswer,
        points: questionData.points || 1,
        order: i,
        difficulty: questionData.difficulty || 'medium',
        hints: questionData.hints || [],
        explanation: questionData.explanation,
        createdBy: request.user._id
      });
      
      await question.save();
      createdQuestions.push(question);
    }

    // تحديث الاختبار بالأسئلة
    quiz.questions = createdQuestions.map(q => q._id);
    await quiz.save();

    // جلب الاختبار مع البيانات المرتبطة
    const populatedQuiz = await Quiz.findById(quiz._id)
      .populate('course', 'title')
      .populate('lesson', 'title')
      .populate('questions')
      .populate('createdBy', 'name');

    return NextResponse.json({
      message: 'تم إنشاء الاختبار بنجاح',
      quiz: populatedQuiz
    }, { status: 201 });

  } catch (error) {
    console.error('Quiz creation error:', error);
    
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(err => err.message);
      return NextResponse.json(
        { message: messages.join(', ') },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: 'خطأ في إنشاء الاختبار' },
      { status: 500 }
    );
  }
}, { roles: ['admin', 'super-admin'] });
