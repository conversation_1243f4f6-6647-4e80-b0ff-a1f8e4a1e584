import Course from '../models/Course.js';
import express from "express";
import { verifyToken } from "../middleware/verifyToken.js";
import UserProgress from "../models/UserProgress.js";
import { allowRoles } from "../middleware/adminOnly.js";


const router = express.Router();

// Get user progress for a specific course// routes/userProgress.js
router.get("/:userId/:courseId", verifyToken, async (req, res) => {
  try {
    const { userId, courseId } = req.params;

   if (req.user.id !== userId && req.user.role !== 'admin' && req.user.role !== 'super-admin') {
   return res.status(403).json({ message: "Unauthorized access" });
  }


    const progress = await UserProgress.findOne({
      user: userId,
      course: courseId
    });
    

    if (!progress) {
      return res.status(200).json({}); // لا يوجد تقدم بعد
    }

    res.json(progress);
  } catch (error) {
    res.status(500).json({ message: "Server error", error: error.message });
  }
});

// Update user progress
router.put("/:userId/:courseId", verifyToken, async (req, res) => {
  try {
    const { userId, courseId } = req.params;

    if (req.user.id !== userId) {
      return res.status(403).json({ message: "Unauthorized access" });
    }

    const { currentUnitIndex, completedLessons, passedQuizzes } = req.body;

    const progress = await UserProgress.findOneAndUpdate(
      { user: userId, course: courseId },
      {
        $set: {
          currentUnitIndex,
          completedLessons,
          passedQuizzes
        }
      },
      { new: true, upsert: true }
    );

    res.json(progress);
  } catch (error) {
    res.status(500).json({ message: "Server error", error: error.message });
  }
});

export default router;
