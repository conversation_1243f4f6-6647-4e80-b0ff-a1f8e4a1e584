import React, { useState, useEffect } from 'react';
import { 
  Card, 
  <PERSON>, 
  <PERSON><PERSON>, 
  Badge, 
  Modal, 
  Form, 
  Alert, 
  Spinner,
  InputGroup,
  Dropdown
} from 'react-bootstrap';
import { useTheme } from '../../context/ThemeContext';
import { superAdminAPI } from '../../services/api';
import { 
  Users, 
  Edit, 
  Trash2, 
  Search, 
  Filter,
  UserPlus,
  Shield,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';

const SuperAdminUsers = () => {
  const { isDark } = useTheme();
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showRoleModal, setShowRoleModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [newRole, setNewRole] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState('all');

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await superAdminAPI.getUsers();
      setUsers(response.data);
    } catch (err) {
      setError('فشل في تحميل المستخدمين');
      console.error('Error fetching users:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleRoleChange = async () => {
    try {
      await superAdminAPI.updateUserRole(selectedUser._id, newRole);
      setUsers(users.map(user => 
        user._id === selectedUser._id 
          ? { ...user, role: newRole }
          : user
      ));
      setShowRoleModal(false);
      setSelectedUser(null);
      setNewRole('');
    } catch (err) {
      setError('فشل في تحديث الصلاحية');
      console.error('Error updating role:', err);
    }
  };

  const handleDeleteUser = async () => {
    try {
      await supedAdminAPI.deleteUser(selectedUser._id);
      setUsers(users.filter(user => user._id !== selectedUser._id));
      setShowDeleteModal(false);
      setSelectedUser(null);
    } catch (err) {
      setError('فشل في حذف المستخدم');
      console.error('Error deleting user:', err);
    }
  };

  const getRoleBadge = (role) => {
    const roleConfig = {
      'super-admin': { variant: 'danger', icon: Shield, label: 'مدير عام' },
      'admin': { variant: 'warning', icon: Shield, label: 'مدير' },
      'student': { variant: 'primary', icon: Users, label: 'طالب' }
    };

    const config = roleConfig[role] || roleConfig.student;
    const IconComponent = config.icon;

    return (
      <Badge bg={config.variant} className="d-flex align-items-center">
        <IconComponent size={12} className="me-1" />
        {config.label}
      </Badge>
    );
  };

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesRole = roleFilter === 'all' || user.role === roleFilter;
    return matchesSearch && matchesRole;
  });

  if (loading) {
    return (
      <div className="text-center py-5">
        <Spinner animation="border" variant="primary" />
        <p className="mt-3">جاري تحميل المستخدمين...</p>
      </div>
    );
  }

  return (
    <div>
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h4 className="mb-1">إدارة المستخدمين</h4>
          <p className="text-muted mb-0">التحكم الكامل في المستخدمين والصلاحيات</p>
        </div>
        <Button variant="primary" className="d-flex align-items-center">
          <UserPlus size={18} className="me-2" />
          إضافة مستخدم
        </Button>
      </div>

      {error && (
        <Alert variant="danger" className="mb-4">
          <AlertTriangle size={18} className="me-2" />
          {error}
        </Alert>
      )}

      {/* Filters */}
      <Card className={`${isDark ? 'bg-dark text-light' : 'bg-white'} border-0 shadow-sm mb-4`}>
        <Card.Body>
          <div className="row g-3">
            <div className="col-md-6">
              <InputGroup>
                <InputGroup.Text>
                  <Search size={16} />
                </InputGroup.Text>
                <Form.Control
                  type="text"
                  placeholder="البحث بالاسم أو البريد الإلكتروني..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </InputGroup>
            </div>
            <div className="col-md-3">
              <Dropdown>
                <Dropdown.Toggle variant="outline-secondary" className="w-100 d-flex align-items-center justify-content-between">
                  <span className="d-flex align-items-center">
                    <Filter size={16} className="me-2" />
                    {roleFilter === 'all' ? 'جميع الأدوار' : 
                     roleFilter === 'super-admin' ? 'مدير عام' :
                     roleFilter === 'admin' ? 'مدير' : 'طالب'}
                  </span>
                </Dropdown.Toggle>
                <Dropdown.Menu>
                  <Dropdown.Item onClick={() => setRoleFilter('all')}>جميع الأدوار</Dropdown.Item>
                  <Dropdown.Item onClick={() => setRoleFilter('super-admin')}>مدير عام</Dropdown.Item>
                  <Dropdown.Item onClick={() => setRoleFilter('admin')}>مدير</Dropdown.Item>
                  <Dropdown.Item onClick={() => setRoleFilter('student')}>طالب</Dropdown.Item>
                </Dropdown.Menu>
              </Dropdown>
            </div>
            <div className="col-md-3">
              <div className="text-muted small">
                إجمالي: {filteredUsers.length} مستخدم
              </div>
            </div>
          </div>
        </Card.Body>
      </Card>

      {/* Users Table */}
      <Card className={`${isDark ? 'bg-dark text-light' : 'bg-white'} border-0 shadow-sm`}>
        <Card.Body className="p-0">
          <Table responsive className={`mb-0 ${isDark ? 'table-dark' : ''}`}>
            <thead className={isDark ? 'table-secondary' : 'table-light'}>
              <tr>
                <th>المستخدم</th>
                <th>البريد الإلكتروني</th>
                <th>الدور</th>
                <th>تاريخ التسجيل</th>
                <th>آخر نشاط</th>
                <th>الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              {filteredUsers.map((user) => (
                <tr key={user._id}>
                  <td>
                    <div className="d-flex align-items-center">
                      <div className="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" 
                           style={{ width: '40px', height: '40px' }}>
                        <span className="text-white fw-bold">
                          {user.name.charAt(0).toUpperCase()}
                        </span>
                      </div>
                      <div>
                        <div className="fw-bold">{user.name}</div>
                        <small className="text-muted">ID: {user._id.slice(-6)}</small>
                      </div>
                    </div>
                  </td>
                  <td>{user.email}</td>
                  <td>{getRoleBadge(user.role)}</td>
                  <td>
                    <small>
                      {new Date(user.createdAt).toLocaleDateString('ar-SA')}
                    </small>
                  </td>
                  <td>
                    <small className="text-muted">
                      {user.lastActive ? 
                        new Date(user.lastActive).toLocaleDateString('ar-SA') : 
                        'لم يسجل دخول'
                      }
                    </small>
                  </td>
                  <td>
                    <div className="d-flex gap-2">
                      <Button
                        variant="outline-primary"
                        size="sm"
                        onClick={() => {
                          setSelectedUser(user);
                          setNewRole(user.role);
                          setShowRoleModal(true);
                        }}
                      >
                        <Edit size={14} />
                      </Button>
                      <Button
                        variant="outline-danger"
                        size="sm"
                        onClick={() => {
                          setSelectedUser(user);
                          setShowDeleteModal(true);
                        }}
                        disabled={user.role === 'super-admin'}
                      >
                        <Trash2 size={14} />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </Table>
        </Card.Body>
      </Card>

      {/* Role Change Modal */}
      <Modal show={showRoleModal} onHide={() => setShowRoleModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>تغيير صلاحية المستخدم</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p>تغيير صلاحية المستخدم: <strong>{selectedUser?.name}</strong></p>
          <Form.Group>
            <Form.Label>الصلاحية الجديدة</Form.Label>
            <Form.Select
              value={newRole}
              onChange={(e) => setNewRole(e.target.value)}
            >
              <option value="student">طالب</option>
              <option value="admin">مدير</option>
              <option value="super-admin">مدير عام</option>
            </Form.Select>
          </Form.Group>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowRoleModal(false)}>
            إلغاء
          </Button>
          <Button variant="primary" onClick={handleRoleChange}>
            تحديث الصلاحية
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title className="text-danger">تأكيد الحذف</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="text-center">
            <AlertTriangle size={48} className="text-danger mb-3" />
            <p>هل أنت متأكد من حذف المستخدم: <strong>{selectedUser?.name}</strong>؟</p>
            <p className="text-muted">هذا الإجراء لا يمكن التراجع عنه.</p>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
            إلغاء
          </Button>
          <Button variant="danger" onClick={handleDeleteUser}>
            حذف المستخدم
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default SuperAdminUsers;
