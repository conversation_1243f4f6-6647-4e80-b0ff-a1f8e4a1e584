import { NextResponse } from 'next/server';
import { verifyToken } from '../../../../../../../lib/auth';
import { connectDB } from '../../../../../../../lib/mongodb';
import Course from '../../../../../../../models/Course';

// GET - جلب دروس الوحدة
export async function GET(request, { params }) {
  try {
    await connectDB();
    
    const { id: courseId, unitId } = params;
    
    const course = await Course.findById(courseId);
    if (!course) {
      return NextResponse.json(
        { message: 'الدورة غير موجودة' },
        { status: 404 }
      );
    }

    const unit = course.units.id(unitId);
    if (!unit) {
      return NextResponse.json(
        { message: 'الوحدة غير موجودة' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      lessons: unit.lessons || [],
      unitTitle: unit.title
    });

  } catch (error) {
    console.error('Error fetching lessons:', error);
    return NextResponse.json(
      { message: 'خطأ في جلب الدروس' },
      { status: 500 }
    );
  }
}

// POST - إضافة درس جديد
export async function POST(request, { params }) {
  try {
    await connectDB();
    
    // التحقق من صحة التوكن والصلاحيات
    const authResult = await verifyToken(request);
    if (!authResult.success) {
      return NextResponse.json(
        { message: authResult.message },
        { status: 401 }
      );
    }

    const { user } = authResult;
    if (!['admin', 'super-admin'].includes(user.role)) {
      return NextResponse.json(
        { message: 'غير مصرح لك بهذا الإجراء' },
        { status: 403 }
      );
    }

    const { id: courseId, unitId } = params;
    const { title, type, content, videoUrl, textContent, duration } = await request.json();

    if (!title || !type) {
      return NextResponse.json(
        { message: 'عنوان الدرس ونوعه مطلوبان' },
        { status: 400 }
      );
    }

    const course = await Course.findById(courseId);
    if (!course) {
      return NextResponse.json(
        { message: 'الدورة غير موجودة' },
        { status: 404 }
      );
    }

    const unit = course.units.id(unitId);
    if (!unit) {
      return NextResponse.json(
        { message: 'الوحدة غير موجودة' },
        { status: 404 }
      );
    }

    // إنشاء الدرس الجديد
    const newLesson = {
      title,
      type,
      content: content || '',
      videoUrl: videoUrl || '',
      textContent: textContent || '',
      duration: duration || '',
      order: unit.lessons.length
    };

    unit.lessons.push(newLesson);
    course.updatedBy = user._id;
    
    await course.save();

    const addedLesson = unit.lessons[unit.lessons.length - 1];

    return NextResponse.json({
      success: true,
      message: 'تم إضافة الدرس بنجاح',
      lesson: addedLesson
    });

  } catch (error) {
    console.error('Error adding lesson:', error);
    return NextResponse.json(
      { message: 'خطأ في إضافة الدرس' },
      { status: 500 }
    );
  }
}

// PUT - تحديث ترتيب الدروس
export async function PUT(request, { params }) {
  try {
    await connectDB();
    
    // التحقق من صحة التوكن والصلاحيات
    const authResult = await verifyToken(request);
    if (!authResult.success) {
      return NextResponse.json(
        { message: authResult.message },
        { status: 401 }
      );
    }

    const { user } = authResult;
    if (!['admin', 'super-admin'].includes(user.role)) {
      return NextResponse.json(
        { message: 'غير مصرح لك بهذا الإجراء' },
        { status: 403 }
      );
    }

    const { id: courseId, unitId } = params;
    const { lessons } = await request.json();

    const course = await Course.findById(courseId);
    if (!course) {
      return NextResponse.json(
        { message: 'الدورة غير موجودة' },
        { status: 404 }
      );
    }

    const unit = course.units.id(unitId);
    if (!unit) {
      return NextResponse.json(
        { message: 'الوحدة غير موجودة' },
        { status: 404 }
      );
    }

    // تحديث ترتيب الدروس
    unit.lessons = lessons;
    course.updatedBy = user._id;
    
    await course.save();

    return NextResponse.json({
      success: true,
      message: 'تم تحديث ترتيب الدروس بنجاح'
    });

  } catch (error) {
    console.error('Error updating lessons order:', error);
    return NextResponse.json(
      { message: 'خطأ في تحديث ترتيب الدروس' },
      { status: 500 }
    );
  }
}
