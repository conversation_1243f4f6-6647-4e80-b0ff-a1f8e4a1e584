import React, { useState } from 'react';
import { Card, Form, Button, Alert } from 'react-bootstrap';
import { MessageCircle, Send, X } from 'lucide-react';
import { chatbotAPI } from '../services/api';
import { useAuth } from '../context/AuthContext';

const Chatbot = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState([
    { type: 'bot', text: 'مرحباً! كيف يمكنني مساعدتك في دوراتك اليوم؟' }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const { isAuthenticated } = useAuth();

  const sendMessage = async (e) => {
    e.preventDefault();
    if (!inputMessage.trim() || isLoading) return;

    const userMessage = inputMessage.trim();
    setInputMessage('');
    setError('');
    
    // Add user message
    setMessages(prev => [...prev, { type: 'user', text: userMessage }]);
    setIsLoading(true);

    try {
      const response = await chatbotAPI.sendMessage(userMessage);
      setMessages(prev => [...prev, { type: 'bot', text: response.data.response }]);
    } catch (error) {
      setError(error.response?.data?.message || 'فشل في إرسال الرسالة');
      setMessages(prev => [...prev, { 
        type: 'bot', 
        text: 'عذراً، أواجه مشكلة في الرد الآن. يرجى المحاولة مرة أخرى لاحقاً.' 
      }]);
    } finally {
      setIsLoading(false);
    }
  };

  if (!isAuthenticated) return null;

  return (
    <div dir="rtl">
      {/* Chat Widget */}
      <div 
        className={`position-fixed bottom-0 end-0 m-3 ${isOpen ? 'd-none' : ''}`}
        style={{ zIndex: 1000 }}
      >
        <Button
          variant="primary"
          size="lg"
          className="rounded-circle shadow"
          onClick={() => setIsOpen(true)}
          style={{ width: '60px', height: '60px' }}
        >
          <MessageCircle size={24} />
        </Button>
      </div>

      {/* Chat Window */}
      {isOpen && (
        <div 
          className="position-fixed bottom-0 end-0 m-3"
          style={{ zIndex: 1001, width: '350px', maxWidth: '90vw' }}
        >
          <Card className="shadow-lg">
            <Card.Header className="bg-primary text-white d-flex justify-content-between align-items-center">
              <div className="d-flex align-items-center">
                <MessageCircle size={20} className="me-2" />
                <span className="fw-semibold">مساعد الدراسة</span>
              </div>
              <Button
                variant="link"
                size="sm"
                className="text-white p-0"
                onClick={() => setIsOpen(false)}
              >
                <X size={20} />
              </Button>
            </Card.Header>
            
            <Card.Body style={{ height: '300px', overflowY: 'auto' }} className="p-3">
              {error && (
                <Alert variant="danger" dismissible onClose={() => setError('')}>
                  {error}
                </Alert>
              )}
              
              {messages.map((message, index) => (
                <div
                  key={index}
                  className={`mb-3 d-flex ${message.type === 'user' ? 'justify-content-end' : ''}`}
                >
                  <div
                    className={`p-2 rounded ${
                      message.type === 'user'
                        ? 'bg-primary text-white'
                        : 'bg-light text-dark'
                    }`}
                    style={{ maxWidth: '80%' }}
                  >
                    <small>{message.text}</small>
                  </div>
                </div>
              ))}
              
              {isLoading && (
                <div className="d-flex justify-content-start mb-3">
                  <div className="bg-light p-2 rounded">
                    <div className="spinner-border spinner-border-sm" role="status">
                      <span className="visually-hidden">جاري التحميل...</span>
                    </div>
                  </div>
                </div>
              )}
            </Card.Body>
            
            <Card.Footer>
              <Form onSubmit={sendMessage}>
                <div className="input-group">
                  <Form.Control
                    type="text"
                    placeholder="اسألني أي شيء..."
                    value={inputMessage}
                    onChange={(e) => setInputMessage(e.target.value)}
                    disabled={isLoading}
                  />
                  <Button 
                    type="submit" 
                    variant="primary" 
                    disabled={isLoading || !inputMessage.trim()}
                  >
                    <Send size={16} />
                  </Button>
                </div>
              </Form>
            </Card.Footer>
          </Card>
        </div>
      )}
    </div>
  );
};

export default Chatbot;