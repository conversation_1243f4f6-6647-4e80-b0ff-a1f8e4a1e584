import React from 'react';
import { Container, Row, Col, Nav, Card } from 'react-bootstrap';
import { Link, useLocation } from 'react-router-dom';
import { useTheme } from '../../context/ThemeContext';
import { useAuth } from '../../context/AuthContext';
import {
  LayoutDashboard,
  Users,
  BookOpen,
  Plus,
  Settings,
  Shield,
  BarChart3
} from 'lucide-react';

const SuperAdminLayout = ({ children }) => {
  const { isDark } = useTheme();
  const { user } = useAuth();
  const location = useLocation();

  const sidebarItems = [
    {
      path: '/super-admin',
      icon: LayoutDashboard,
      label: 'لوحة التحكم',
      exact: true
    },
    {
      path: '/super-admin/users',
      icon: Users,
      label: 'إدارة المستخدمين'
    },
    {
      path: '/super-admin/courses',
      icon: BookOpen,
      label: 'إدارة الدورات'
    },
    {
      path: '/super-admin/add-course',
      icon: Plus,
      label: 'إضافة دورة'
    },
    {
      path: '/super-admin/analytics',
      icon: BarChart3,
      label: 'التحليلات المتقدمة'
    },
    {
      path: '/super-admin/system',
      icon: Settings,
      label: 'إعدادات النظام'
    }
  ];

  const isActive = (path, exact = false) => {
    if (exact) {
      return location.pathname === path;
    }
    return location.pathname.startsWith(path);
  };

  return (
    <Container fluid className="px-0">
      <Row className="g-0">
        {/* Sidebar */}
        <Col md={3} lg={2} className={`${isDark ? 'bg-dark' : 'bg-light'} min-vh-100 border-end`}>
          <div className="p-3">
            {/* Super Admin Header */}
            <Card className={`${isDark ? 'bg-secondary text-light' : 'bg-primary text-white'} mb-4`}>
              <Card.Body className="text-center py-3">
                <Shield size={32} className="mb-2" />
                <h6 className="mb-1">المدير العام</h6>
                <small className="opacity-75">{user?.name}</small>
              </Card.Body>
            </Card>

            {/* Navigation */}
            <Nav className="flex-column">
              {sidebarItems.map((item) => {
                const IconComponent = item.icon;
                const active = isActive(item.path, item.exact);
                
                return (
                  <Nav.Link
                    key={item.path}
                    as={Link}
                    to={item.path}
                    className={`d-flex align-items-center py-3 px-3 mb-1 rounded ${
                      active 
                        ? (isDark ? 'bg-primary text-white' : 'bg-primary text-white')
                        : (isDark ? 'text-light hover-bg-secondary' : 'text-dark hover-bg-light')
                    }`}
                    style={{
                      textDecoration: 'none',
                      transition: 'all 0.2s ease'
                    }}
                  >
                    <IconComponent size={18} className="me-3" />
                    <span>{item.label}</span>
                  </Nav.Link>
                );
              })}
            </Nav>
          </div>
        </Col>

        {/* Main Content */}
        <Col md={9} lg={10}>
          <div className="p-4">
            {/* Page Header */}
            <div className="d-flex justify-content-between align-items-center mb-4">
              <div>
                <h4 className={`mb-1 ${isDark ? 'text-light' : 'text-dark'}`}>
                  لوحة تحكم المدير العام
                </h4>
                <p className={`mb-0 ${isDark ? 'text-muted' : 'text-secondary'}`}>
                  التحكم الكامل في النظام والصلاحيات
                </p>
              </div>
              <div className="d-flex align-items-center">
                <Shield size={20} className={`me-2 ${isDark ? 'text-warning' : 'text-primary'}`} />
                <span className={`small ${isDark ? 'text-light' : 'text-dark'}`}>
                  صلاحيات كاملة
                </span>
              </div>
            </div>

            {/* Page Content */}
            <div className={`${isDark ? 'text-light' : 'text-dark'}`}>
              {children}
            </div>
          </div>
        </Col>
      </Row>

      <style jsx>{`
        .hover-bg-secondary:hover {
          background-color: rgba(108, 117, 125, 0.1) !important;
        }
        .hover-bg-light:hover {
          background-color: rgba(0, 0, 0, 0.05) !important;
        }
      `}</style>
    </Container>
  );
};

export default SuperAdminLayout;
