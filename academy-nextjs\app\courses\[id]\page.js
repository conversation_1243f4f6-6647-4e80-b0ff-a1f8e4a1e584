'use client';

import { useState, useEffect } from 'react';
import { Con<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Button, Badge, Accordion, ProgressBar, Tab, Tabs } from 'react-bootstrap';
import { useAuth } from '../../../contexts/AuthContext';
import { useTheme } from '../../../contexts/ThemeContext';
import { useRouter } from 'next/navigation';
import { 
  BookOpen, 
  User, 
  Clock, 
  Star, 
  Play, 
  CheckCircle,
  Lock,
  Users,
  Calendar,
  Award,
  ArrowLeft,
  Share2,
  Heart,
  Download
} from 'lucide-react';
import { toast } from 'react-toastify';

export default function CourseDetails({ params }) {
  const { user, isAuthenticated, api } = useAuth();
  const { isDark } = useTheme();
  const router = useRouter();
  
  const [course, setCourse] = useState(null);
  const [loading, setLoading] = useState(true);
  const [enrolled, setEnrolled] = useState(false);
  const [progress, setProgress] = useState(0);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    if (params.id) {
      fetchCourseDetails();
    }
  }, [params.id]);

  const fetchCourseDetails = async () => {
    try {
      setLoading(true);

      // جلب تفاصيل الدورة من API
      const courseResponse = await api.get(`/courses/${params.id}`);
      const courseData = courseResponse.data.course;

      // جلب تقدم الطالب إذا كان مسجلاً
      if (isAuthenticated) {
        try {
          const progressResponse = await api.get(`/courses/${params.id}/progress`);
          setProgress(progressResponse.data.progress);
          setEnrolled(true);
        } catch (error) {
          if (error.response?.status !== 403) {
            console.error('Error fetching progress:', error);
          }
        }
      }

      setCourse(courseData);

      // التحقق من التسجيل في الدورة إذا كان المستخدم مسجل دخوله
      if (isAuthenticated) {
        try {
          const enrollmentResponse = await api.get(`/enrollments?courseId=${params.id}`);
          const userEnrollment = enrollmentResponse.data.enrollments.find(
            e => e.student === user._id
          );

          if (userEnrollment) {
            setEnrolled(true);
            setProgress(userEnrollment.progress);
          }
        } catch (error) {
          // المستخدم غير مسجل في الدورة
          console.log('User not enrolled in course');
        }
      }

      // بيانات اختبارية للمقارنة
      const mockCourse = {
        _id: params.id,
        title: 'تطوير تطبيقات React المتقدمة',
        description: 'دورة شاملة لتعلم تطوير تطبيقات الويب الحديثة باستخدام React.js مع أحدث التقنيات والممارسات الأفضل في المجال.',
        instructor: 'أحمد المطور',
        duration: '25 ساعة',
        level: 'متوسط',
        category: 'البرمجة',
        price: 299,
        rating: 4.8,
        ratingCount: 156,
        enrollmentCount: 1250,
        image: '/api/placeholder/800/400',
        tags: ['React', 'JavaScript', 'Frontend', 'Web Development'],
        createdAt: new Date(),
        isActive: true,
        units: [
          {
            _id: '1',
            title: 'مقدمة في React',
            description: 'تعلم أساسيات React وإعداد البيئة',
            lessons: [
              {
                _id: '1-1',
                title: 'ما هو React؟',
                type: 'video',
                duration: '15 دقيقة',
                completed: true
              },
              {
                _id: '1-2',
                title: 'إعداد بيئة التطوير',
                type: 'video',
                duration: '20 دقيقة',
                completed: true
              },
              {
                _id: '1-3',
                title: 'أول تطبيق React',
                type: 'exercise',
                duration: '30 دقيقة',
                completed: false
              }
            ],
            order: 0
          },
          {
            _id: '2',
            title: 'المكونات والخصائص',
            description: 'فهم المكونات وكيفية استخدام الخصائص',
            lessons: [
              {
                _id: '2-1',
                title: 'إنشاء المكونات',
                type: 'video',
                duration: '25 دقيقة',
                completed: false
              },
              {
                _id: '2-2',
                title: 'تمرير الخصائص',
                type: 'video',
                duration: '20 دقيقة',
                completed: false
              },
              {
                _id: '2-3',
                title: 'تطبيق عملي',
                type: 'exercise',
                duration: '45 دقيقة',
                completed: false
              }
            ],
            order: 1
          },
          {
            _id: '3',
            title: 'إدارة الحالة',
            description: 'تعلم كيفية إدارة حالة التطبيق',
            lessons: [
              {
                _id: '3-1',
                title: 'useState Hook',
                type: 'video',
                duration: '30 دقيقة',
                completed: false
              },
              {
                _id: '3-2',
                title: 'useEffect Hook',
                type: 'video',
                duration: '35 دقيقة',
                completed: false
              }
            ],
            order: 2
          }
        ]
      };

      setCourse(mockCourse);
      
      // محاكاة حالة التسجيل والتقدم
      if (isAuthenticated) {
        setEnrolled(true);
        setProgress(35); // 35% مكتمل
      }

    } catch (error) {
      console.error('Error fetching course:', error);
      toast.error('فشل في تحميل تفاصيل الدورة');
    } finally {
      setLoading(false);
    }
  };

  const handleEnroll = async () => {
    if (!isAuthenticated) {
      router.push('/login');
      return;
    }

    try {
      const response = await api.post('/enrollments', {
        courseId: params.id
      });

      setEnrolled(true);
      setProgress(0);
      toast.success(response.data.message);
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'فشل في التسجيل في الدورة';
      toast.error(errorMessage);
    }
  };

  const calculateTotalLessons = () => {
    if (!course?.units) return 0;
    return course.units.reduce((total, unit) => total + unit.lessons.length, 0);
  };

  const calculateCompletedLessons = () => {
    if (!course?.units) return 0;
    return course.units.reduce((total, unit) => 
      total + unit.lessons.filter(lesson => lesson.completed).length, 0
    );
  };

  const getLessonIcon = (lesson) => {
    if (lesson.completed) {
      return <CheckCircle size={16} className="text-success" />;
    }
    if (lesson.type === 'video') {
      return <Play size={16} className="text-primary" />;
    }
    if (lesson.type === 'exercise') {
      return <BookOpen size={16} className="text-warning" />;
    }
    return <Lock size={16} className="text-muted" />;
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center min-vh-100">
        <div className="spinner-border text-primary" />
      </div>
    );
  }

  if (!course) {
    return (
      <Container className="py-5 text-center">
        <h3>الدورة غير موجودة</h3>
        <Button variant="primary" onClick={() => router.push('/courses')}>
          العودة للدورات
        </Button>
      </Container>
    );
  }

  return (
    <div className={`min-vh-100 ${isDark ? 'bg-dark text-light' : 'bg-light'}`}>
      <Container className="py-5">
        {/* Header */}
        <Row className="mb-4">
          <Col>
            <Button
              variant="outline-secondary"
              onClick={() => router.back()}
              className="mb-3"
            >
              <ArrowLeft size={16} className="me-1" />
              العودة
            </Button>
          </Col>
        </Row>

        <Row>
          {/* Course Content */}
          <Col lg={8}>
            {/* Course Header */}
            <Card className={`border-0 shadow-sm mb-4 ${isDark ? 'bg-dark text-light' : ''}`}>
              <Card.Body>
                <div className="d-flex justify-content-between align-items-start mb-3">
                  <div>
                    <Badge bg="primary" className="mb-2">{course.category}</Badge>
                    <h1 className="h3 fw-bold mb-2">{course.title}</h1>
                    <p className="text-muted mb-3">{course.description}</p>
                  </div>
                  <div className="d-flex gap-2">
                    <Button variant="outline-secondary" size="sm">
                      <Heart size={16} />
                    </Button>
                    <Button variant="outline-secondary" size="sm">
                      <Share2 size={16} />
                    </Button>
                  </div>
                </div>

                <Row className="align-items-center">
                  <Col md={6}>
                    <div className="d-flex align-items-center gap-3 flex-wrap">
                      <div className="d-flex align-items-center">
                        <User size={16} className="text-muted me-1" />
                        <span>{course.instructor}</span>
                      </div>
                      <div className="d-flex align-items-center">
                        <Clock size={16} className="text-muted me-1" />
                        <span>{course.duration}</span>
                      </div>
                      <div className="d-flex align-items-center">
                        <Star size={16} className="text-warning me-1" />
                        <span>{course.rating}</span>
                        <small className="text-muted ms-1">({course.ratingCount})</small>
                      </div>
                    </div>
                  </Col>
                  <Col md={6} className="text-md-end">
                    <div className="d-flex align-items-center justify-content-md-end gap-2">
                      <Badge bg="success">{course.level}</Badge>
                      <Badge bg="info">
                        <Users size={12} className="me-1" />
                        {course.enrollmentCount} طالب
                      </Badge>
                    </div>
                  </Col>
                </Row>

                {/* Progress Bar for Enrolled Students */}
                {enrolled && (
                  <div className="mt-3">
                    <div className="d-flex justify-content-between align-items-center mb-2">
                      <span className="small">التقدم في الدورة</span>
                      <span className="small">{progress}%</span>
                    </div>
                    <ProgressBar now={progress} variant="success" />
                    <small className="text-muted">
                      {calculateCompletedLessons()} من {calculateTotalLessons()} درس مكتمل
                    </small>
                  </div>
                )}
              </Card.Body>
            </Card>

            {/* Course Tabs */}
            <Card className={`border-0 shadow-sm ${isDark ? 'bg-dark text-light' : ''}`}>
              <Card.Body>
                <Tabs
                  activeKey={activeTab}
                  onSelect={setActiveTab}
                  className="mb-4"
                >
                  <Tab eventKey="overview" title="نظرة عامة">
                    <div className="mb-4">
                      <h5>وصف الدورة</h5>
                      <p>{course.description}</p>
                      
                      <h6 className="mt-4 mb-3">ما ستتعلمه:</h6>
                      <ul>
                        <li>أساسيات React.js والمفاهيم الأساسية</li>
                        <li>إنشاء مكونات قابلة لإعادة الاستخدام</li>
                        <li>إدارة حالة التطبيق باستخدام Hooks</li>
                        <li>التعامل مع APIs والبيانات الخارجية</li>
                        <li>بناء تطبيق كامل من الصفر</li>
                      </ul>

                      <h6 className="mt-4 mb-3">المتطلبات:</h6>
                      <ul>
                        <li>معرفة أساسية بـ HTML و CSS</li>
                        <li>فهم أساسيات JavaScript</li>
                        <li>لا يتطلب خبرة سابقة في React</li>
                      </ul>
                    </div>
                  </Tab>

                  <Tab eventKey="curriculum" title="المنهج">
                    <div className="mb-4">
                      <div className="d-flex justify-content-between align-items-center mb-3">
                        <h5>محتوى الدورة</h5>
                        <small className="text-muted">
                          {course.units.length} وحدة • {calculateTotalLessons()} درس
                        </small>
                      </div>

                      <Accordion>
                        {course.units.map((unit, unitIndex) => (
                          <Accordion.Item key={unit._id} eventKey={unitIndex.toString()}>
                            <Accordion.Header>
                              <div className="d-flex justify-content-between align-items-center w-100 me-3">
                                <span className="fw-bold">{unit.title}</span>
                                <small className="text-muted">
                                  {unit.lessons.length} دروس
                                </small>
                              </div>
                            </Accordion.Header>
                            <Accordion.Body>
                              <p className="text-muted mb-3">{unit.description}</p>
                              <div className="list-group list-group-flush">
                                {unit.lessons.map((lesson) => (
                                  <div key={lesson._id} className="list-group-item bg-transparent border-0 px-0">
                                    <div className="d-flex justify-content-between align-items-center">
                                      <div className="d-flex align-items-center">
                                        {getLessonIcon(lesson)}
                                        <span className="ms-2">{lesson.title}</span>
                                        {lesson.type === 'exercise' && (
                                          <Badge bg="warning" className="ms-2 small">تطبيق</Badge>
                                        )}
                                      </div>
                                      <small className="text-muted">{lesson.duration}</small>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </Accordion.Body>
                          </Accordion.Item>
                        ))}
                      </Accordion>
                    </div>
                  </Tab>

                  <Tab eventKey="instructor" title="المدرب">
                    <div className="mb-4">
                      <Row>
                        <Col md={3}>
                          <div className="bg-primary rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center" 
                               style={{ width: '80px', height: '80px' }}>
                            <User size={40} className="text-white" />
                          </div>
                        </Col>
                        <Col md={9}>
                          <h5>{course.instructor}</h5>
                          <p className="text-muted">مطور Full Stack مع خبرة 8 سنوات</p>
                          <p>
                            أحمد مطور محترف متخصص في تقنيات الويب الحديثة، يمتلك خبرة واسعة في React.js و Node.js.
                            قام بتدريب أكثر من 5000 طالب وساعدهم في بناء مسيرتهم المهنية في مجال البرمجة.
                          </p>
                          <div className="d-flex gap-3">
                            <div>
                              <strong>12</strong>
                              <small className="text-muted d-block">دورة</small>
                            </div>
                            <div>
                              <strong>5,234</strong>
                              <small className="text-muted d-block">طالب</small>
                            </div>
                            <div>
                              <strong>4.9</strong>
                              <small className="text-muted d-block">تقييم</small>
                            </div>
                          </div>
                        </Col>
                      </Row>
                    </div>
                  </Tab>
                </Tabs>
              </Card.Body>
            </Card>
          </Col>

          {/* Sidebar */}
          <Col lg={4}>
            <Card className={`border-0 shadow-sm sticky-top ${isDark ? 'bg-dark text-light' : ''}`} style={{ top: '20px' }}>
              <Card.Body>
                {/* Course Image */}
                <div className="mb-3">
                  <div className="bg-light rounded p-4 text-center">
                    <BookOpen size={64} className="text-muted" />
                  </div>
                </div>

                {/* Price */}
                <div className="text-center mb-4">
                  {course.price === 0 ? (
                    <h3 className="text-success fw-bold">مجاني</h3>
                  ) : (
                    <div>
                      <h3 className="fw-bold">{course.price} ر.س</h3>
                      <small className="text-muted">دفعة واحدة</small>
                    </div>
                  )}
                </div>

                {/* Enroll Button */}
                <div className="d-grid mb-4">
                  {enrolled ? (
                    <Button variant="success" size="lg" disabled>
                      <CheckCircle size={20} className="me-2" />
                      مسجل في الدورة
                    </Button>
                  ) : (
                    <Button variant="primary" size="lg" onClick={handleEnroll}>
                      {course.price === 0 ? 'التسجيل المجاني' : 'شراء الدورة'}
                    </Button>
                  )}
                </div>

                {/* Course Info */}
                <div className="border-top pt-3">
                  <h6 className="mb-3">تفاصيل الدورة</h6>
                  <div className="d-flex justify-content-between mb-2">
                    <span>المستوى:</span>
                    <Badge bg="primary">{course.level}</Badge>
                  </div>
                  <div className="d-flex justify-content-between mb-2">
                    <span>المدة:</span>
                    <span>{course.duration}</span>
                  </div>
                  <div className="d-flex justify-content-between mb-2">
                    <span>الدروس:</span>
                    <span>{calculateTotalLessons()}</span>
                  </div>
                  <div className="d-flex justify-content-between mb-2">
                    <span>الطلاب:</span>
                    <span>{course.enrollmentCount.toLocaleString()}</span>
                  </div>
                  <div className="d-flex justify-content-between mb-2">
                    <span>التقييم:</span>
                    <div>
                      <Star size={14} className="text-warning me-1" />
                      {course.rating} ({course.ratingCount})
                    </div>
                  </div>
                </div>

                {/* Tags */}
                <div className="border-top pt-3 mt-3">
                  <h6 className="mb-3">الكلمات المفتاحية</h6>
                  <div className="d-flex flex-wrap gap-2">
                    {course.tags.map((tag, index) => (
                      <Badge key={index} bg="outline-primary">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Certificate */}
                <div className="border-top pt-3 mt-3 text-center">
                  <Award size={32} className="text-warning mb-2" />
                  <h6>شهادة إتمام</h6>
                  <small className="text-muted">
                    احصل على شهادة معتمدة عند إكمال الدورة
                  </small>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>
    </div>
  );
}
