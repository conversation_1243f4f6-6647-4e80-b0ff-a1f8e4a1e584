export const adminOnly = (req, res, next) => {
  if (req.user && (req.user.role === 'admin' || req.user.role === 'super-admin')) {
    next();
  } else {
    res.status(403).json({ message: 'الوصول مرفوض. صلاحيات المشرف مطلوبة.' });
  }
};

export const instructorOnly = (req, res, next) => {
  if (req.user && (req.user.role === 'instructor' || req.user.role === 'admin' || req.user.role === 'super-admin')) {
    next();
  } else {
    res.status(403).json({ message: 'الوصول مرفوض. صلاحيات المدرب مطلوبة.' });
  }
};

export const superAdminOnly = (req, res, next) => {
  if (req.user && req.user.role === 'super-admin') {
    next();
  } else {
    res.status(403).json({ message: 'الوصول مرفوض. صلاحيات المدير العام مطلوبة.' });
  }
};

export const studentOnly = (req, res, next) => {
  if (req.user && req.user.role === 'student') {
    next();
  } else {
    res.status(403).json({ message: 'الوصول مرفوض. صلاحيات الطالب مطلوبة.' });
  }
};