import express from 'express';
import UserProgress from '../models/UserProgress.js';
import HonorBoard from '../models/HonorBoard.js';
import Course from '../models/Course.js';
import { verifyToken } from '../middleware/verifyToken.js';

const router = express.Router();

// Get user progress for a course
router.get('/course/:courseId', verifyToken, async (req, res) => {
  try {
    const progress = await UserProgress.findOne({
      userId: req.user._id,
      courseId: req.params.courseId
    });

    if (!progress) {
      return res.json({
        currentUnitIndex: 0,
        completedLessons: [],
        passedQuizzes: [],
        progressPercentage: 0
      });
    }

    res.json(progress);
  } catch (error) {
    res.status(500).json({ message: 'خطأ في الخادم', error: error.message });
  }
});

// Mark lesson as completed
router.post('/lesson/complete', verifyToken, async (req, res) => {
  try {
    const { courseId, unitIndex, lessonIndex } = req.body;

    let progress = await UserProgress.findOne({
      userId: req.user._id,
      courseId
    });

    if (!progress) {
      progress = new UserProgress({
        userId: req.user._id,
        courseId,
        currentUnitIndex: 0,
        completedLessons: [],
        passedQuizzes: []
      });
    }

    // Check if lesson already completed
    const existingLesson = progress.completedLessons.find(
      lesson => lesson.unitIndex === unitIndex && lesson.lessonIndex === lessonIndex
    );

    if (!existingLesson) {
      progress.completedLessons.push({
        unitIndex,
        lessonIndex,
        completedAt: new Date()
      });
    }

    // Calculate progress percentage
    const course = await Course.findById(courseId);
    const totalLessons = course.units.reduce((total, unit) => total + unit.lessons.length, 0);
    progress.progressPercentage = Math.round((progress.completedLessons.length / totalLessons) * 100);

    await progress.save();
    res.json(progress);
  } catch (error) {
    res.status(500).json({ message: 'خطأ في الخادم', error: error.message });
  }
});

// Submit quiz
router.post('/quiz/submit', verifyToken, async (req, res) => {
  try {
    const { courseId, unitIndex, answers } = req.body;

    const course = await Course.findById(courseId);
    const unit = course.units[unitIndex];
    const quiz = unit.quiz;

    // Calculate score
    let correctAnswers = 0;
    quiz.questions.forEach((question, index) => {
      if (question.correctAnswer === answers[index]) {
        correctAnswers++;
      }
    });

    const score = Math.round((correctAnswers / quiz.questions.length) * 100);
    const passed = score >= quiz.passingScore;

    let progress = await UserProgress.findOne({
      userId: req.user._id,
      courseId
    });

    if (!progress) {
      progress = new UserProgress({
        userId: req.user._id,
        courseId,
        currentUnitIndex: 0,
        completedLessons: [],
        passedQuizzes: []
      });
    }

    // Update quiz attempts
    const existingQuiz = progress.passedQuizzes.find(q => q.unitIndex === unitIndex);
    if (existingQuiz) {
      existingQuiz.attempts += 1;
      if (passed && existingQuiz.score < score) {
        existingQuiz.score = score;
        existingQuiz.passedAt = new Date();
      }
    } else {
      progress.passedQuizzes.push({
        unitIndex,
        score,
        attempts: 1,
        passedAt: passed ? new Date() : null
      });
    }

    // Update current unit if quiz passed
    if (passed && progress.currentUnitIndex === unitIndex) {
      progress.currentUnitIndex = Math.min(unitIndex + 1, course.units.length - 1);
    }

    // Check if course completed
    const allQuizzesPassed = course.units.every((unit, index) => {
      return progress.passedQuizzes.some(q => q.unitIndex === index && q.score >= quiz.passingScore);
    });

    if (allQuizzesPassed && !progress.isCompleted) {
      progress.isCompleted = true;
      progress.completedAt = new Date();
      progress.progressPercentage = 100;

      // Update honor board
      await updateHonorBoard(req.user._id, courseId, course.points, course.level);
    }

    await progress.save();

    res.json({
      score,
      passed,
      correctAnswers,
      totalQuestions: quiz.questions.length,
      progress
    });
  } catch (error) {
    res.status(500).json({ message: 'خطأ في الخادم', error: error.message });
  }
});

// Helper function to update honor board
async function updateHonorBoard(userId, courseId, points, level) {
  try {
    let honorBoard = await HonorBoard.findOne({ userId });

    if (!honorBoard) {
      honorBoard = new HonorBoard({
        userId,
        completedCourses: [],
        totalPoints: 0
      });
    }

    // Check if course already completed
    const existingCourse = honorBoard.completedCourses.find(
      course => course.courseId.toString() === courseId.toString()
    );

    if (!existingCourse) {
      honorBoard.completedCourses.push({
        courseId,
        completedAt: new Date(),
        points,
        level
      });
      honorBoard.totalPoints += points;
      await honorBoard.save();
    }
  } catch (error) {
    console.error('Error updating honor board:', error);
  }
}

export default router;