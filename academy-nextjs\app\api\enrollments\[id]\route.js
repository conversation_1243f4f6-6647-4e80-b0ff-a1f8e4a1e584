import { NextResponse } from 'next/server';
import { withAuth } from '../../../../lib/auth';
import connectDB from '../../../../lib/mongodb';
import Enrollment from '../../../../models/Enrollment';
import User from '../../../../models/User';

// GET - جلب تفاصيل التسجيل
export const GET = withAuth(async (request, { params }) => {
  try {
    await connectDB();
    
    const { id } = params;
    
    const enrollment = await Enrollment.findById(id)
      .populate('course')
      .populate('student', 'name email');
    
    if (!enrollment) {
      return NextResponse.json(
        { message: 'التسجيل غير موجود' },
        { status: 404 }
      );
    }

    // التحقق من الصلاحية - المستخدم يمكنه رؤية تسجيلاته فقط
    if (enrollment.student._id.toString() !== request.user._id.toString() && 
        !['admin', 'super-admin'].includes(request.user.role)) {
      return NextResponse.json(
        { message: 'ليس لديك صلاحية لعرض هذا التسجيل' },
        { status: 403 }
      );
    }

    return NextResponse.json({
      enrollment
    }, { status: 200 });

  } catch (error) {
    console.error('Enrollment fetch error:', error);
    return NextResponse.json(
      { message: 'خطأ في جلب التسجيل' },
      { status: 500 }
    );
  }
});

// PUT - تحديث التقدم في الدورة
export const PUT = withAuth(async (request, { params }) => {
  try {
    await connectDB();
    
    const { id } = params;
    const data = await request.json();
    
    const enrollment = await Enrollment.findById(id).populate('course');
    
    if (!enrollment) {
      return NextResponse.json(
        { message: 'التسجيل غير موجود' },
        { status: 404 }
      );
    }

    // التحقق من الصلاحية
    if (enrollment.student.toString() !== request.user._id.toString() && 
        !['admin', 'super-admin'].includes(request.user.role)) {
      return NextResponse.json(
        { message: 'ليس لديك صلاحية لتحديث هذا التسجيل' },
        { status: 403 }
      );
    }

    // تحديث الدرس المكتمل
    if (data.completedLesson) {
      const { lessonId, timeSpent, score } = data.completedLesson;
      await enrollment.completeLesson(lessonId, timeSpent, score);
    }

    // تحديث الموقع الحالي
    if (data.currentUnit !== undefined) {
      enrollment.currentUnit = data.currentUnit;
    }
    
    if (data.currentLesson !== undefined) {
      enrollment.currentLesson = data.currentLesson;
    }

    // تحديث التقييم
    if (data.rating) {
      enrollment.rating = {
        score: data.rating.score,
        review: data.rating.review,
        ratedAt: new Date()
      };
      
      // تحديث تقييم الدورة
      const allRatings = await Enrollment.find({ 
        course: enrollment.course._id,
        'rating.score': { $exists: true }
      }).select('rating.score');
      
      if (allRatings.length > 0) {
        const averageRating = allRatings.reduce((sum, e) => sum + e.rating.score, 0) / allRatings.length;
        await enrollment.course.updateOne({
          rating: Math.round(averageRating * 10) / 10,
          ratingCount: allRatings.length
        });
      }
    }

    // إكمال الدورة
    if (data.completeCourse) {
      await enrollment.completeCourse(data.finalScore);
      
      // تحديث إحصائيات المستخدم
      await User.findByIdAndUpdate(enrollment.student, {
        $inc: { 
          'stats.coursesCompleted': 1,
          totalPoints: data.finalScore || 0
        }
      });
    }

    await enrollment.save();

    return NextResponse.json({
      message: 'تم تحديث التقدم بنجاح',
      enrollment
    }, { status: 200 });

  } catch (error) {
    console.error('Enrollment update error:', error);
    return NextResponse.json(
      { message: 'خطأ في تحديث التقدم' },
      { status: 500 }
    );
  }
});

// DELETE - إلغاء التسجيل
export const DELETE = withAuth(async (request, { params }) => {
  try {
    await connectDB();
    
    const { id } = params;
    
    const enrollment = await Enrollment.findById(id);
    
    if (!enrollment) {
      return NextResponse.json(
        { message: 'التسجيل غير موجود' },
        { status: 404 }
      );
    }

    // التحقق من الصلاحية
    if (enrollment.student.toString() !== request.user._id.toString() && 
        !['admin', 'super-admin'].includes(request.user.role)) {
      return NextResponse.json(
        { message: 'ليس لديك صلاحية لحذف هذا التسجيل' },
        { status: 403 }
      );
    }

    // حذف التسجيل
    await Enrollment.findByIdAndDelete(id);

    // تحديث عدد المسجلين في الدورة
    await Course.findByIdAndUpdate(enrollment.course, {
      $inc: { enrollmentCount: -1 }
    });

    return NextResponse.json({
      message: 'تم إلغاء التسجيل بنجاح'
    }, { status: 200 });

  } catch (error) {
    console.error('Enrollment deletion error:', error);
    return NextResponse.json(
      { message: 'خطأ في إلغاء التسجيل' },
      { status: 500 }
    );
  }
});
