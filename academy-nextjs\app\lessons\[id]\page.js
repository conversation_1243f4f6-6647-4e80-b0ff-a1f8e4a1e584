'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useAuth } from '../../../contexts/AuthContext';
import ProtectedRoute from '../../../components/ProtectedRoute';
import api from '../../../lib/api';
import { toast } from 'react-toastify';
import { 
  Play, 
  Pause, 
  SkipForward, 
  SkipBack, 
  CheckCircle, 
  Clock,
  FileText,
  Download,
  BookOpen,
  ArrowLeft,
  ArrowRight,
  Eye,
  Volume2
} from 'lucide-react';

export default function LessonView() {
  const params = useParams();
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  
  const [lesson, setLesson] = useState(null);
  const [enrollment, setEnrollment] = useState(null);
  const [navigation, setNavigation] = useState({ next: null, previous: null });
  const [loading, setLoading] = useState(true);
  const [isCompleted, setIsCompleted] = useState(false);
  const [startTime, setStartTime] = useState(null);
  const [watchTime, setWatchTime] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);

  useEffect(() => {
    if (isAuthenticated) {
      fetchLesson();
      setStartTime(Date.now());
    }
  }, [params.id, isAuthenticated]);

  useEffect(() => {
    // تتبع وقت المشاهدة
    let interval;
    if (isPlaying && startTime) {
      interval = setInterval(() => {
        setWatchTime(prev => prev + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isPlaying, startTime]);

  const fetchLesson = async () => {
    try {
      setLoading(true);
      
      const response = await api.get(`/lessons/${params.id}`);
      const { lesson: lessonData, navigation: navData, enrollment: enrollmentData } = response.data;
      
      setLesson(lessonData);
      setNavigation(navData);
      setEnrollment(enrollmentData);
      setIsCompleted(enrollmentData?.isCompleted || false);
      
    } catch (error) {
      console.error('Error fetching lesson:', error);
      if (error.response?.status === 403) {
        toast.error('ليس لديك صلاحية لعرض هذا الدرس');
        router.push('/courses');
      } else {
        toast.error('خطأ في جلب الدرس');
      }
    } finally {
      setLoading(false);
    }
  };

  const markLessonCompleted = async () => {
    if (!enrollment || isCompleted) return;

    try {
      const timeSpent = Math.floor((Date.now() - startTime) / 1000);
      
      await api.put(`/enrollments/${enrollment._id}`, {
        completedLesson: {
          lessonId: params.id,
          timeSpent,
          score: 100 // للدروس العادية
        }
      });
      
      setIsCompleted(true);
      toast.success('تم إكمال الدرس بنجاح!');
      
    } catch (error) {
      console.error('Error marking lesson completed:', error);
      toast.error('خطأ في تسجيل إكمال الدرس');
    }
  };

  const handleNavigation = (direction) => {
    const targetLesson = direction === 'next' ? navigation.next : navigation.previous;
    if (targetLesson) {
      router.push(`/lessons/${targetLesson._id}`);
    }
  };

  const renderLessonContent = () => {
    if (!lesson) return null;

    switch (lesson.type) {
      case 'video':
        return (
          <div className="video-container mb-4">
            <div className="ratio ratio-16x9">
              {lesson.content.videoProvider === 'youtube' ? (
                <iframe
                  src={`https://www.youtube.com/embed/${lesson.content.videoUrl}`}
                  title={lesson.title}
                  allowFullScreen
                  onLoad={() => setIsPlaying(true)}
                ></iframe>
              ) : (
                <video
                  controls
                  className="w-100"
                  onPlay={() => setIsPlaying(true)}
                  onPause={() => setIsPlaying(false)}
                >
                  <source src={lesson.content.videoUrl} type="video/mp4" />
                  متصفحك لا يدعم تشغيل الفيديو
                </video>
              )}
            </div>
            <div className="mt-2 d-flex justify-content-between align-items-center">
              <div className="d-flex align-items-center text-muted">
                <Clock className="w-4 h-4 me-1" />
                <span>مدة الفيديو: {lesson.content.videoDuration || lesson.duration} دقيقة</span>
              </div>
              <div className="d-flex align-items-center text-muted">
                <Eye className="w-4 h-4 me-1" />
                <span>{lesson.stats?.views || 0} مشاهدة</span>
              </div>
            </div>
          </div>
        );

      case 'text':
        return (
          <div className="lesson-text-content mb-4">
            <div 
              className="content-body"
              dangerouslySetInnerHTML={{ __html: lesson.content.text }}
            />
          </div>
        );

      case 'file':
        return (
          <div className="file-content mb-4">
            <div className="card">
              <div className="card-body text-center">
                <FileText className="w-16 h-16 text-primary mx-auto mb-3" />
                <h5>{lesson.title}</h5>
                <p className="text-muted">{lesson.description}</p>
                <div className="d-flex justify-content-center gap-2">
                  <a
                    href={lesson.content.fileUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="btn btn-primary"
                  >
                    <Eye className="w-4 h-4 me-1" />
                    عرض الملف
                  </a>
                  <a
                    href={lesson.content.fileUrl}
                    download
                    className="btn btn-outline-primary"
                  >
                    <Download className="w-4 h-4 me-1" />
                    تحميل
                  </a>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return (
          <div className="default-content mb-4">
            <div className="card">
              <div className="card-body">
                <p>{lesson.description}</p>
              </div>
            </div>
          </div>
        );
    }
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">جاري التحميل...</span>
        </div>
      </div>
    );
  }

  if (!lesson) {
    return (
      <div className="container py-5">
        <div className="text-center">
          <h3>الدرس غير موجود</h3>
          <button className="btn btn-primary" onClick={() => router.back()}>
            العودة
          </button>
        </div>
      </div>
    );
  }

  return (
    <ProtectedRoute>
      <div className="container-fluid py-4">
        <div className="row">
          {/* Main Content */}
          <div className="col-lg-8">
            {/* Lesson Header */}
            <div className="d-flex justify-content-between align-items-start mb-4">
              <div>
                <nav aria-label="breadcrumb">
                  <ol className="breadcrumb">
                    <li className="breadcrumb-item">
                      <a href="/courses" className="text-decoration-none">الدورات</a>
                    </li>
                    <li className="breadcrumb-item">
                      <a href={`/courses/${lesson.course._id}`} className="text-decoration-none">
                        {lesson.course.title}
                      </a>
                    </li>
                    <li className="breadcrumb-item active">{lesson.title}</li>
                  </ol>
                </nav>
                <h1 className="h3 mb-2">{lesson.title}</h1>
                <p className="text-muted mb-0">{lesson.description}</p>
              </div>
              {!isCompleted && (
                <button
                  className="btn btn-success"
                  onClick={markLessonCompleted}
                  disabled={lesson.completionCriteria === 'time' && watchTime < (lesson.minViewTime || 0)}
                >
                  <CheckCircle className="w-4 h-4 me-1" />
                  إكمال الدرس
                </button>
              )}
            </div>

            {/* Lesson Content */}
            {renderLessonContent()}

            {/* Resources */}
            {lesson.resources && lesson.resources.length > 0 && (
              <div className="card mb-4">
                <div className="card-header">
                  <h5 className="mb-0">المواد المساعدة</h5>
                </div>
                <div className="card-body">
                  <div className="list-group list-group-flush">
                    {lesson.resources.map((resource, index) => (
                      <div key={index} className="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                          <h6 className="mb-1">{resource.title}</h6>
                          <small className="text-muted">{resource.type}</small>
                        </div>
                        <a
                          href={resource.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="btn btn-outline-primary btn-sm"
                        >
                          <Download className="w-3 h-3 me-1" />
                          تحميل
                        </a>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Navigation */}
            <div className="d-flex justify-content-between">
              <button
                className="btn btn-outline-primary"
                onClick={() => handleNavigation('previous')}
                disabled={!navigation.previous}
              >
                <ArrowRight className="w-4 h-4 me-1" />
                الدرس السابق
                {navigation.previous && (
                  <small className="d-block text-muted">
                    {navigation.previous.title}
                  </small>
                )}
              </button>
              
              <button
                className="btn btn-primary"
                onClick={() => handleNavigation('next')}
                disabled={!navigation.next}
              >
                الدرس التالي
                <ArrowLeft className="w-4 h-4 ms-1" />
                {navigation.next && (
                  <small className="d-block text-muted">
                    {navigation.next.title}
                  </small>
                )}
              </button>
            </div>
          </div>

          {/* Sidebar */}
          <div className="col-lg-4">
            <div className="card sticky-top" style={{ top: '20px' }}>
              <div className="card-header">
                <h5 className="mb-0">معلومات الدرس</h5>
              </div>
              <div className="card-body">
                <div className="mb-3">
                  <div className="d-flex justify-content-between align-items-center mb-2">
                    <span>الحالة:</span>
                    <span className={`badge ${isCompleted ? 'bg-success' : 'bg-warning'}`}>
                      {isCompleted ? 'مكتمل' : 'قيد التقدم'}
                    </span>
                  </div>
                  <div className="d-flex justify-content-between align-items-center mb-2">
                    <span>المدة:</span>
                    <span>{lesson.duration} دقيقة</span>
                  </div>
                  <div className="d-flex justify-content-between align-items-center mb-2">
                    <span>وقت المشاهدة:</span>
                    <span>{Math.floor(watchTime / 60)}:{(watchTime % 60).toString().padStart(2, '0')}</span>
                  </div>
                  <div className="d-flex justify-content-between align-items-center">
                    <span>المشاهدات:</span>
                    <span>{lesson.stats?.views || 0}</span>
                  </div>
                </div>

                {enrollment && (
                  <div className="mb-3">
                    <h6>التقدم في الدورة</h6>
                    <div className="progress mb-2">
                      <div
                        className="progress-bar"
                        style={{ width: `${enrollment.progress}%` }}
                      >
                        {enrollment.progress}%
                      </div>
                    </div>
                    <small className="text-muted">
                      {enrollment.progress}% مكتمل
                    </small>
                  </div>
                )}

                <div className="d-grid">
                  <button
                    className="btn btn-outline-primary"
                    onClick={() => router.push(`/courses/${lesson.course._id}`)}
                  >
                    <BookOpen className="w-4 h-4 me-1" />
                    العودة للدورة
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
