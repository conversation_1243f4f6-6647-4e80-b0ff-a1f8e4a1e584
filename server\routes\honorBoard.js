import express from 'express';
import HonorBoard from '../models/HonorBoard.js';
import User from '../models/User.js';

const router = express.Router();

// Get honor board
router.get('/', async (req, res) => {
  try {
    const honorBoard = await HonorBoard.find()
      .populate('userId', 'name email')
      .populate('completedCourses.courseId', 'title level')
      .sort({ totalPoints: -1 })
      .limit(50);

    // Add rank to each entry
    const rankedBoard = honorBoard.map((entry, index) => ({
      ...entry.toObject(),
      rank: index + 1
    }));

    res.json(rankedBoard);
  } catch (error) {
    res.status(500).json({ message: 'خطأ في الخادم', error: error.message });
  }
});

// Get user's honor board entry
router.get('/user/:userId', async (req, res) => {
  try {
    const userHonor = await HonorBoard.findOne({ userId: req.params.userId })
      .populate('userId', 'name email')
      .populate('completedCourses.courseId', 'title level');

    if (!userHonor) {
      return res.status(404).json({ message: 'لم يتم العثور على بيانات الطالب' });
    }

    // Calculate rank
    const higherRanked = await HonorBoard.countDocuments({
      totalPoints: { $gt: userHonor.totalPoints }
    });

    res.json({
      ...userHonor.toObject(),
      rank: higherRanked + 1
    });
  } catch (error) {
    res.status(500).json({ message: 'خطأ في الخادم', error: error.message });
  }
});

export default router;