import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';

const userSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  role: {
    type: String,
    enum: ['student', 'admin', 'instance', 'super-admin'],
    default: 'student'
  },
  lastActive: {
    type: Date,
    default: Date.now
  },
  isActive: {
    type: Boolean,
    default: true
  },
  loginCount: {
    type: Number,
    default: 0
  },
  // لائحة الشرف
  completedCourses: [{
    courseId: { type: mongoose.Schema.Types.ObjectId, ref: 'Course' },
    completedAt: Date,
    level: String // لتسهيل حساب النقاط
  }],
  totalPoints: {
    type: Number,
    default: 0
  },
  achievements: [{
    type: String
  }],
  certificates: [{
    name: String,
    date: {
      type: Date,
      default: Date.now
    },
    courseId: { type: mongoose.Schema.Types.ObjectId, ref: 'Course' }
  }],
  profilePicture: {
    type: String,
    default: 'default-profile.png' // مسار الصورة الافتراضية
  },
  bio: {
    type: String,
    maxlength: 500,
    default: ''
  },
 socialLinks: {
  github: {
    type: String,
    default: ''
  },
  linkedin: {
    type: String,
    default: ''
  }
},
microsoftId: {
  type: String,
  default: ''
},
googleId: {
  type: String,
  default: ''
},
}, {
  timestamps: true
});

userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(10);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

userSchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

export default mongoose.model('User', userSchema);