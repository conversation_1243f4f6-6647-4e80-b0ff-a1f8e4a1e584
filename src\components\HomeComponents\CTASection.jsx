import React from 'react';
import { Container, Row, Col, But<PERSON> } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import { 
  Play, Users, Rocket, Target, CheckCircle 
} from 'lucide-react';

const CTASection = ({ token, animatedStats }) => {
  return (
    <div className="cta-section text-white py-5 position-relative section-spacing">
      <div className="floating-shapes">
        <div className="floating-shape">
          <Rocket size={60} />
        </div>
        <div className="floating-shape">
          <Target size={50} />
        </div>
      </div>

      <Container>
        <Row className="text-center">
          <Col lg={8} className="mx-auto animate-fadeInUp">
            <div className="mb-4">
              <Rocket size={60} className="text-warning animate-float mb-3" />
            </div>
            
            <h2 className="display-4 fw-bold mb-4">
              مستعد لبدء 
              <span className="text-warning"> رحلة التعلم؟</span>
            </h2>
            
            <p className="lead mb-5">
              انضم إلى <strong className="text-warning">{animatedStats.students.toLocaleString()}+</strong> متعلم 
              واتخذ الخطوة التالية في مسيرتك المهنية اليوم
            </p>

            <div className="d-flex justify-content-center gap-3 flex-wrap">
              <Button 
                as={Link} 
                to="/courses" 
                variant="warning" 
                size="lg"
                className="px-5 py-3 fw-bold animate-pulse"
              >
                <Play size={20} className="me-2" />
                استكشف الدورات
              </Button>
              
              {!token && (
                <Button 
                  as={Link} 
                  to="/register" 
                  variant="outline-light" 
                  size="lg"
                  className="px-5 py-3"
                >
                  <Users size={20} className="me-2" />
                  سجل مجاناً الآن
                </Button>
              )}
            </div>

            <div className="mt-5 pt-4 border-top border-light border-opacity-25">
              <Row className="text-center">
                <Col md={3}>
                  <div className="benefit-item mb-3">
                    <CheckCircle size={24} className="text-success mb-2" />
                    <div>
                      <strong>تسجيل مجاني</strong>
                      <br />
                      <small className="text-light opacity-75">بدون رسوم خفية</small>
                    </div>
                  </div>
                </Col>
                <Col md={3}>
                  <div className="benefit-item mb-3">
                    <CheckCircle size={24} className="text-success mb-2" />
                    <div>
                      <strong>شهادات معتمدة</strong>
                      <br />
                      <small className="text-light opacity-75">معترف بها دولياً</small>
                    </div>
                  </div>
                </Col>
                <Col md={3}>
                  <div className="benefit-item mb-3">
                    <CheckCircle size={24} className="text-success mb-2" />
                    <div>
                      <strong>دعم مستمر</strong>
                      <br />
                      <small className="text-light opacity-75">24/7 مساعدة</small>
                    </div>
                  </div>
                </Col>
                <Col md={3}>
                  <div className="benefit-item mb-3">
                    <CheckCircle size={24} className="text-success mb-2" />
                    <div>
                      <strong>تحديثات مجانية</strong>
                      <br />
                      <small className="text-light opacity-75">محتوى جديد دائماً</small>
                    </div>
                  </div>
                </Col>
              </Row>
            </div>
          </Col>
        </Row>
      </Container>
    </div>
  );
};

export default CTASection;
