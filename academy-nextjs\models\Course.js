import mongoose from 'mongoose';

// مخطط الدرس
const lessonSchema = new mongoose.Schema({
  title: { type: String, required: true },
  type: {
    type: String,
    enum: ['video', 'reading', 'exercise'],
    required: true
  },
  content: String,
  videoUrl: String,
  textContent: String,
  duration: String,
  order: { type: Number, default: 0 }
}, { _id: true });

// مخطط السؤال للاختبار
const questionSchema = new mongoose.Schema({
  question: { type: String, required: true },
  options: [{ type: String, required: true }],
  correctAnswer: { type: String, required: true },
  explanation: String
}, { _id: true });

// مخطط الاختبار
const quizSchema = new mongoose.Schema({
  title: { type: String, default: 'اختبار الوحدة' },
  questions: [questionSchema],
  passingScore: { type: Number, default: 70 },
  timeLimit: { type: Number, default: 30 } // بالدقائق
}, { _id: false });

// مخطط الوحدة
const unitSchema = new mongoose.Schema({
  title: { type: String, required: true },
  description: String,
  lessons: [lessonSchema],
  quiz: quizSchema,
  order: { type: Number, default: 0 }
}, { _id: true });

const courseSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'عنوان الدورة مطلوب'],
    trim: true
  },
  description: {
    type: String,
    required: [true, 'وصف الدورة مطلوب']
  },
  instructor: {
    type: String,
    required: [true, 'اسم المدرب مطلوب'],
    default: 'مدرب المنصة'
  },
  duration: {
    type: String,
    default: '0 دقيقة'
  },
  level: {
    type: String,
    enum: ['مبتدئ', 'متوسط', 'متقدم', 'Beginner', 'Intermediate', 'Advanced'],
    default: 'مبتدئ'
  },
  category: {
    type: String,
    default: 'عام'
  },
  tags: [{
    type: String,
    trim: true
  }],
  image: String,
  video: String, // فيديو تعريفي
  prerequisites: [{
    type: String,
    trim: true
  }],
  learningOutcomes: [{
    type: String,
    trim: true
  }],
  language: {
    type: String,
    enum: ['ar', 'en', 'fr'],
    default: 'ar'
  },
  price: {
    type: Number,
    default: 0
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isFeatured: {
    type: Boolean,
    default: false
  },
  // إحصائيات
  enrollmentCount: {
    type: Number,
    default: 0
  },
  views: {
    type: Number,
    default: 0
  },
  rating: {
    type: Number,
    default: 0,
    min: 0,
    max: 5
  },
  ratingCount: {
    type: Number,
    default: 0
  },
  completionRate: {
    type: Number,
    default: 0
  },
  // المحتوى
  units: [unitSchema],
  // المنشئ والمحرر
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// إنشاء فهرس للبحث النصي
courseSchema.index({ 
  title: 'text', 
  description: 'text', 
  tags: 'text',
  instructor: 'text'
});

// فهارس للأداء
courseSchema.index({ isActive: 1, createdAt: -1 });
courseSchema.index({ level: 1, isActive: 1 });
courseSchema.index({ category: 1, isActive: 1 });
courseSchema.index({ rating: -1, isActive: 1 });

export default mongoose.models.Course || mongoose.model('Course', courseSchema);
