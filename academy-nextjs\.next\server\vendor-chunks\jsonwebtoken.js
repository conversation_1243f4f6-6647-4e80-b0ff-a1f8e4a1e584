/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jsonwebtoken";
exports.ids = ["vendor-chunks/jsonwebtoken"];
exports.modules = {

/***/ "(rsc)/./node_modules/jsonwebtoken/decode.js":
/*!*********************************************!*\
  !*** ./node_modules/jsonwebtoken/decode.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var jws = __webpack_require__(/*! jws */ \"(rsc)/./node_modules/jws/index.js\");\nmodule.exports = function(jwt, options) {\n    options = options || {};\n    var decoded = jws.decode(jwt, options);\n    if (!decoded) {\n        return null;\n    }\n    var payload = decoded.payload;\n    //try parse the payload\n    if (typeof payload === \"string\") {\n        try {\n            var obj = JSON.parse(payload);\n            if (obj !== null && typeof obj === \"object\") {\n                payload = obj;\n            }\n        } catch (e) {}\n    }\n    //return header if `complete` option is enabled.  header includes claims\n    //such as `kid` and `alg` used to select the key within a JWKS needed to\n    //verify the signature\n    if (options.complete === true) {\n        return {\n            header: decoded.header,\n            payload: payload,\n            signature: decoded.signature\n        };\n    }\n    return payload;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/decode.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/index.js":
/*!********************************************!*\
  !*** ./node_modules/jsonwebtoken/index.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = {\n    decode: __webpack_require__(/*! ./decode */ \"(rsc)/./node_modules/jsonwebtoken/decode.js\"),\n    verify: __webpack_require__(/*! ./verify */ \"(rsc)/./node_modules/jsonwebtoken/verify.js\"),\n    sign: __webpack_require__(/*! ./sign */ \"(rsc)/./node_modules/jsonwebtoken/sign.js\"),\n    JsonWebTokenError: __webpack_require__(/*! ./lib/JsonWebTokenError */ \"(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\"),\n    NotBeforeError: __webpack_require__(/*! ./lib/NotBeforeError */ \"(rsc)/./node_modules/jsonwebtoken/lib/NotBeforeError.js\"),\n    TokenExpiredError: __webpack_require__(/*! ./lib/TokenExpiredError */ \"(rsc)/./node_modules/jsonwebtoken/lib/TokenExpiredError.js\")\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFBQSxPQUFPQyxPQUFPLEdBQUc7SUFDZkMsUUFBUUMsbUJBQU9BLENBQUM7SUFDaEJDLFFBQVFELG1CQUFPQSxDQUFDO0lBQ2hCRSxNQUFNRixtQkFBT0EsQ0FBQztJQUNkRyxtQkFBbUJILG1CQUFPQSxDQUFDO0lBQzNCSSxnQkFBZ0JKLG1CQUFPQSxDQUFDO0lBQ3hCSyxtQkFBbUJMLG1CQUFPQSxDQUFDO0FBQzdCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWNhZGVteS1uZXh0anMvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2luZGV4LmpzPzliZTgiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSB7XG4gIGRlY29kZTogcmVxdWlyZSgnLi9kZWNvZGUnKSxcbiAgdmVyaWZ5OiByZXF1aXJlKCcuL3ZlcmlmeScpLFxuICBzaWduOiByZXF1aXJlKCcuL3NpZ24nKSxcbiAgSnNvbldlYlRva2VuRXJyb3I6IHJlcXVpcmUoJy4vbGliL0pzb25XZWJUb2tlbkVycm9yJyksXG4gIE5vdEJlZm9yZUVycm9yOiByZXF1aXJlKCcuL2xpYi9Ob3RCZWZvcmVFcnJvcicpLFxuICBUb2tlbkV4cGlyZWRFcnJvcjogcmVxdWlyZSgnLi9saWIvVG9rZW5FeHBpcmVkRXJyb3InKSxcbn07XG4iXSwibmFtZXMiOlsibW9kdWxlIiwiZXhwb3J0cyIsImRlY29kZSIsInJlcXVpcmUiLCJ2ZXJpZnkiLCJzaWduIiwiSnNvbldlYlRva2VuRXJyb3IiLCJOb3RCZWZvcmVFcnJvciIsIlRva2VuRXhwaXJlZEVycm9yIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js":
/*!************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/JsonWebTokenError.js ***!
  \************************************************************/
/***/ ((module) => {

eval("var JsonWebTokenError = function(message, error) {\n    Error.call(this, message);\n    if (Error.captureStackTrace) {\n        Error.captureStackTrace(this, this.constructor);\n    }\n    this.name = \"JsonWebTokenError\";\n    this.message = message;\n    if (error) this.inner = error;\n};\nJsonWebTokenError.prototype = Object.create(Error.prototype);\nJsonWebTokenError.prototype.constructor = JsonWebTokenError;\nmodule.exports = JsonWebTokenError;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hY2FkZW15LW5leHRqcy8uL25vZGVfbW9kdWxlcy9qc29ud2VidG9rZW4vbGliL0pzb25XZWJUb2tlbkVycm9yLmpzPzE2ZjIiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIEpzb25XZWJUb2tlbkVycm9yID0gZnVuY3Rpb24gKG1lc3NhZ2UsIGVycm9yKSB7XG4gIEVycm9yLmNhbGwodGhpcywgbWVzc2FnZSk7XG4gIGlmKEVycm9yLmNhcHR1cmVTdGFja1RyYWNlKSB7XG4gICAgRXJyb3IuY2FwdHVyZVN0YWNrVHJhY2UodGhpcywgdGhpcy5jb25zdHJ1Y3Rvcik7XG4gIH1cbiAgdGhpcy5uYW1lID0gJ0pzb25XZWJUb2tlbkVycm9yJztcbiAgdGhpcy5tZXNzYWdlID0gbWVzc2FnZTtcbiAgaWYgKGVycm9yKSB0aGlzLmlubmVyID0gZXJyb3I7XG59O1xuXG5Kc29uV2ViVG9rZW5FcnJvci5wcm90b3R5cGUgPSBPYmplY3QuY3JlYXRlKEVycm9yLnByb3RvdHlwZSk7XG5Kc29uV2ViVG9rZW5FcnJvci5wcm90b3R5cGUuY29uc3RydWN0b3IgPSBKc29uV2ViVG9rZW5FcnJvcjtcblxubW9kdWxlLmV4cG9ydHMgPSBKc29uV2ViVG9rZW5FcnJvcjtcbiJdLCJuYW1lcyI6WyJKc29uV2ViVG9rZW5FcnJvciIsIm1lc3NhZ2UiLCJlcnJvciIsIkVycm9yIiwiY2FsbCIsImNhcHR1cmVTdGFja1RyYWNlIiwiY29uc3RydWN0b3IiLCJuYW1lIiwiaW5uZXIiLCJwcm90b3R5cGUiLCJPYmplY3QiLCJjcmVhdGUiLCJtb2R1bGUiLCJleHBvcnRzIl0sIm1hcHBpbmdzIjoiQUFBQSxJQUFJQSxvQkFBb0IsU0FBVUMsT0FBTyxFQUFFQyxLQUFLO0lBQzlDQyxNQUFNQyxJQUFJLENBQUMsSUFBSSxFQUFFSDtJQUNqQixJQUFHRSxNQUFNRSxpQkFBaUIsRUFBRTtRQUMxQkYsTUFBTUUsaUJBQWlCLENBQUMsSUFBSSxFQUFFLElBQUksQ0FBQ0MsV0FBVztJQUNoRDtJQUNBLElBQUksQ0FBQ0MsSUFBSSxHQUFHO0lBQ1osSUFBSSxDQUFDTixPQUFPLEdBQUdBO0lBQ2YsSUFBSUMsT0FBTyxJQUFJLENBQUNNLEtBQUssR0FBR047QUFDMUI7QUFFQUYsa0JBQWtCUyxTQUFTLEdBQUdDLE9BQU9DLE1BQU0sQ0FBQ1IsTUFBTU0sU0FBUztBQUMzRFQsa0JBQWtCUyxTQUFTLENBQUNILFdBQVcsR0FBR047QUFFMUNZLE9BQU9DLE9BQU8sR0FBR2IiLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9Kc29uV2ViVG9rZW5FcnJvci5qcyIsInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/NotBeforeError.js":
/*!*********************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/NotBeforeError.js ***!
  \*********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var JsonWebTokenError = __webpack_require__(/*! ./JsonWebTokenError */ \"(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\");\nvar NotBeforeError = function(message, date) {\n    JsonWebTokenError.call(this, message);\n    this.name = \"NotBeforeError\";\n    this.date = date;\n};\nNotBeforeError.prototype = Object.create(JsonWebTokenError.prototype);\nNotBeforeError.prototype.constructor = NotBeforeError;\nmodule.exports = NotBeforeError;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9Ob3RCZWZvcmVFcnJvci5qcyIsIm1hcHBpbmdzIjoiQUFBQSxJQUFJQSxvQkFBb0JDLG1CQUFPQSxDQUFDO0FBRWhDLElBQUlDLGlCQUFpQixTQUFVQyxPQUFPLEVBQUVDLElBQUk7SUFDMUNKLGtCQUFrQkssSUFBSSxDQUFDLElBQUksRUFBRUY7SUFDN0IsSUFBSSxDQUFDRyxJQUFJLEdBQUc7SUFDWixJQUFJLENBQUNGLElBQUksR0FBR0E7QUFDZDtBQUVBRixlQUFlSyxTQUFTLEdBQUdDLE9BQU9DLE1BQU0sQ0FBQ1Qsa0JBQWtCTyxTQUFTO0FBRXBFTCxlQUFlSyxTQUFTLENBQUNHLFdBQVcsR0FBR1I7QUFFdkNTLE9BQU9DLE9BQU8sR0FBR1YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hY2FkZW15LW5leHRqcy8uL25vZGVfbW9kdWxlcy9qc29ud2VidG9rZW4vbGliL05vdEJlZm9yZUVycm9yLmpzPzg2NjgiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIEpzb25XZWJUb2tlbkVycm9yID0gcmVxdWlyZSgnLi9Kc29uV2ViVG9rZW5FcnJvcicpO1xuXG52YXIgTm90QmVmb3JlRXJyb3IgPSBmdW5jdGlvbiAobWVzc2FnZSwgZGF0ZSkge1xuICBKc29uV2ViVG9rZW5FcnJvci5jYWxsKHRoaXMsIG1lc3NhZ2UpO1xuICB0aGlzLm5hbWUgPSAnTm90QmVmb3JlRXJyb3InO1xuICB0aGlzLmRhdGUgPSBkYXRlO1xufTtcblxuTm90QmVmb3JlRXJyb3IucHJvdG90eXBlID0gT2JqZWN0LmNyZWF0ZShKc29uV2ViVG9rZW5FcnJvci5wcm90b3R5cGUpO1xuXG5Ob3RCZWZvcmVFcnJvci5wcm90b3R5cGUuY29uc3RydWN0b3IgPSBOb3RCZWZvcmVFcnJvcjtcblxubW9kdWxlLmV4cG9ydHMgPSBOb3RCZWZvcmVFcnJvcjsiXSwibmFtZXMiOlsiSnNvbldlYlRva2VuRXJyb3IiLCJyZXF1aXJlIiwiTm90QmVmb3JlRXJyb3IiLCJtZXNzYWdlIiwiZGF0ZSIsImNhbGwiLCJuYW1lIiwicHJvdG90eXBlIiwiT2JqZWN0IiwiY3JlYXRlIiwiY29uc3RydWN0b3IiLCJtb2R1bGUiLCJleHBvcnRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/NotBeforeError.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/TokenExpiredError.js":
/*!************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/TokenExpiredError.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var JsonWebTokenError = __webpack_require__(/*! ./JsonWebTokenError */ \"(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\");\nvar TokenExpiredError = function(message, expiredAt) {\n    JsonWebTokenError.call(this, message);\n    this.name = \"TokenExpiredError\";\n    this.expiredAt = expiredAt;\n};\nTokenExpiredError.prototype = Object.create(JsonWebTokenError.prototype);\nTokenExpiredError.prototype.constructor = TokenExpiredError;\nmodule.exports = TokenExpiredError;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9Ub2tlbkV4cGlyZWRFcnJvci5qcyIsIm1hcHBpbmdzIjoiQUFBQSxJQUFJQSxvQkFBb0JDLG1CQUFPQSxDQUFDO0FBRWhDLElBQUlDLG9CQUFvQixTQUFVQyxPQUFPLEVBQUVDLFNBQVM7SUFDbERKLGtCQUFrQkssSUFBSSxDQUFDLElBQUksRUFBRUY7SUFDN0IsSUFBSSxDQUFDRyxJQUFJLEdBQUc7SUFDWixJQUFJLENBQUNGLFNBQVMsR0FBR0E7QUFDbkI7QUFFQUYsa0JBQWtCSyxTQUFTLEdBQUdDLE9BQU9DLE1BQU0sQ0FBQ1Qsa0JBQWtCTyxTQUFTO0FBRXZFTCxrQkFBa0JLLFNBQVMsQ0FBQ0csV0FBVyxHQUFHUjtBQUUxQ1MsT0FBT0MsT0FBTyxHQUFHViIsInNvdXJjZXMiOlsid2VicGFjazovL2FjYWRlbXktbmV4dGpzLy4vbm9kZV9tb2R1bGVzL2pzb253ZWJ0b2tlbi9saWIvVG9rZW5FeHBpcmVkRXJyb3IuanM/OTBlYyJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgSnNvbldlYlRva2VuRXJyb3IgPSByZXF1aXJlKCcuL0pzb25XZWJUb2tlbkVycm9yJyk7XG5cbnZhciBUb2tlbkV4cGlyZWRFcnJvciA9IGZ1bmN0aW9uIChtZXNzYWdlLCBleHBpcmVkQXQpIHtcbiAgSnNvbldlYlRva2VuRXJyb3IuY2FsbCh0aGlzLCBtZXNzYWdlKTtcbiAgdGhpcy5uYW1lID0gJ1Rva2VuRXhwaXJlZEVycm9yJztcbiAgdGhpcy5leHBpcmVkQXQgPSBleHBpcmVkQXQ7XG59O1xuXG5Ub2tlbkV4cGlyZWRFcnJvci5wcm90b3R5cGUgPSBPYmplY3QuY3JlYXRlKEpzb25XZWJUb2tlbkVycm9yLnByb3RvdHlwZSk7XG5cblRva2VuRXhwaXJlZEVycm9yLnByb3RvdHlwZS5jb25zdHJ1Y3RvciA9IFRva2VuRXhwaXJlZEVycm9yO1xuXG5tb2R1bGUuZXhwb3J0cyA9IFRva2VuRXhwaXJlZEVycm9yOyJdLCJuYW1lcyI6WyJKc29uV2ViVG9rZW5FcnJvciIsInJlcXVpcmUiLCJUb2tlbkV4cGlyZWRFcnJvciIsIm1lc3NhZ2UiLCJleHBpcmVkQXQiLCJjYWxsIiwibmFtZSIsInByb3RvdHlwZSIsIk9iamVjdCIsImNyZWF0ZSIsImNvbnN0cnVjdG9yIiwibW9kdWxlIiwiZXhwb3J0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/TokenExpiredError.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/asymmetricKeyDetailsSupported.js":
/*!************************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/asymmetricKeyDetailsSupported.js ***!
  \************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const semver = __webpack_require__(/*! semver */ \"(rsc)/./node_modules/semver/index.js\");\nmodule.exports = semver.satisfies(process.version, \">=15.7.0\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9hc3ltbWV0cmljS2V5RGV0YWlsc1N1cHBvcnRlZC5qcyIsIm1hcHBpbmdzIjoiQUFBQSxNQUFNQSxTQUFTQyxtQkFBT0EsQ0FBQztBQUV2QkMsT0FBT0MsT0FBTyxHQUFHSCxPQUFPSSxTQUFTLENBQUNDLFFBQVFDLE9BQU8sRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL2FjYWRlbXktbmV4dGpzLy4vbm9kZV9tb2R1bGVzL2pzb253ZWJ0b2tlbi9saWIvYXN5bW1ldHJpY0tleURldGFpbHNTdXBwb3J0ZWQuanM/NzNkOSJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBzZW12ZXIgPSByZXF1aXJlKCdzZW12ZXInKTtcblxubW9kdWxlLmV4cG9ydHMgPSBzZW12ZXIuc2F0aXNmaWVzKHByb2Nlc3MudmVyc2lvbiwgJz49MTUuNy4wJyk7XG4iXSwibmFtZXMiOlsic2VtdmVyIiwicmVxdWlyZSIsIm1vZHVsZSIsImV4cG9ydHMiLCJzYXRpc2ZpZXMiLCJwcm9jZXNzIiwidmVyc2lvbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/asymmetricKeyDetailsSupported.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/psSupported.js":
/*!******************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/psSupported.js ***!
  \******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var semver = __webpack_require__(/*! semver */ \"(rsc)/./node_modules/semver/index.js\");\nmodule.exports = semver.satisfies(process.version, \"^6.12.0 || >=8.0.0\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9wc1N1cHBvcnRlZC5qcyIsIm1hcHBpbmdzIjoiQUFBQSxJQUFJQSxTQUFTQyxtQkFBT0EsQ0FBQztBQUVyQkMsT0FBT0MsT0FBTyxHQUFHSCxPQUFPSSxTQUFTLENBQUNDLFFBQVFDLE9BQU8sRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL2FjYWRlbXktbmV4dGpzLy4vbm9kZV9tb2R1bGVzL2pzb253ZWJ0b2tlbi9saWIvcHNTdXBwb3J0ZWQuanM/YzhkNCJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgc2VtdmVyID0gcmVxdWlyZSgnc2VtdmVyJyk7XG5cbm1vZHVsZS5leHBvcnRzID0gc2VtdmVyLnNhdGlzZmllcyhwcm9jZXNzLnZlcnNpb24sICdeNi4xMi4wIHx8ID49OC4wLjAnKTtcbiJdLCJuYW1lcyI6WyJzZW12ZXIiLCJyZXF1aXJlIiwibW9kdWxlIiwiZXhwb3J0cyIsInNhdGlzZmllcyIsInByb2Nlc3MiLCJ2ZXJzaW9uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/psSupported.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/rsaPssKeyDetailsSupported.js":
/*!********************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/rsaPssKeyDetailsSupported.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const semver = __webpack_require__(/*! semver */ \"(rsc)/./node_modules/semver/index.js\");\nmodule.exports = semver.satisfies(process.version, \">=16.9.0\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9yc2FQc3NLZXlEZXRhaWxzU3VwcG9ydGVkLmpzIiwibWFwcGluZ3MiOiJBQUFBLE1BQU1BLFNBQVNDLG1CQUFPQSxDQUFDO0FBRXZCQyxPQUFPQyxPQUFPLEdBQUdILE9BQU9JLFNBQVMsQ0FBQ0MsUUFBUUMsT0FBTyxFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWNhZGVteS1uZXh0anMvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9yc2FQc3NLZXlEZXRhaWxzU3VwcG9ydGVkLmpzP2Y5MDgiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3Qgc2VtdmVyID0gcmVxdWlyZSgnc2VtdmVyJyk7XG5cbm1vZHVsZS5leHBvcnRzID0gc2VtdmVyLnNhdGlzZmllcyhwcm9jZXNzLnZlcnNpb24sICc+PTE2LjkuMCcpO1xuIl0sIm5hbWVzIjpbInNlbXZlciIsInJlcXVpcmUiLCJtb2R1bGUiLCJleHBvcnRzIiwic2F0aXNmaWVzIiwicHJvY2VzcyIsInZlcnNpb24iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/rsaPssKeyDetailsSupported.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/timespan.js":
/*!***************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/timespan.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var ms = __webpack_require__(/*! ms */ \"(rsc)/./node_modules/ms/index.js\");\nmodule.exports = function(time, iat) {\n    var timestamp = iat || Math.floor(Date.now() / 1000);\n    if (typeof time === \"string\") {\n        var milliseconds = ms(time);\n        if (typeof milliseconds === \"undefined\") {\n            return;\n        }\n        return Math.floor(timestamp + milliseconds / 1000);\n    } else if (typeof time === \"number\") {\n        return timestamp + time;\n    } else {\n        return;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi90aW1lc3Bhbi5qcyIsIm1hcHBpbmdzIjoiQUFBQSxJQUFJQSxLQUFLQyxtQkFBT0EsQ0FBQztBQUVqQkMsT0FBT0MsT0FBTyxHQUFHLFNBQVVDLElBQUksRUFBRUMsR0FBRztJQUNsQyxJQUFJQyxZQUFZRCxPQUFPRSxLQUFLQyxLQUFLLENBQUNDLEtBQUtDLEdBQUcsS0FBSztJQUUvQyxJQUFJLE9BQU9OLFNBQVMsVUFBVTtRQUM1QixJQUFJTyxlQUFlWCxHQUFHSTtRQUN0QixJQUFJLE9BQU9PLGlCQUFpQixhQUFhO1lBQ3ZDO1FBQ0Y7UUFDQSxPQUFPSixLQUFLQyxLQUFLLENBQUNGLFlBQVlLLGVBQWU7SUFDL0MsT0FBTyxJQUFJLE9BQU9QLFNBQVMsVUFBVTtRQUNuQyxPQUFPRSxZQUFZRjtJQUNyQixPQUFPO1FBQ0w7SUFDRjtBQUVGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWNhZGVteS1uZXh0anMvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi90aW1lc3Bhbi5qcz9jZWYwIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBtcyA9IHJlcXVpcmUoJ21zJyk7XG5cbm1vZHVsZS5leHBvcnRzID0gZnVuY3Rpb24gKHRpbWUsIGlhdCkge1xuICB2YXIgdGltZXN0YW1wID0gaWF0IHx8IE1hdGguZmxvb3IoRGF0ZS5ub3coKSAvIDEwMDApO1xuXG4gIGlmICh0eXBlb2YgdGltZSA9PT0gJ3N0cmluZycpIHtcbiAgICB2YXIgbWlsbGlzZWNvbmRzID0gbXModGltZSk7XG4gICAgaWYgKHR5cGVvZiBtaWxsaXNlY29uZHMgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuICAgIHJldHVybiBNYXRoLmZsb29yKHRpbWVzdGFtcCArIG1pbGxpc2Vjb25kcyAvIDEwMDApO1xuICB9IGVsc2UgaWYgKHR5cGVvZiB0aW1lID09PSAnbnVtYmVyJykge1xuICAgIHJldHVybiB0aW1lc3RhbXAgKyB0aW1lO1xuICB9IGVsc2Uge1xuICAgIHJldHVybjtcbiAgfVxuXG59OyJdLCJuYW1lcyI6WyJtcyIsInJlcXVpcmUiLCJtb2R1bGUiLCJleHBvcnRzIiwidGltZSIsImlhdCIsInRpbWVzdGFtcCIsIk1hdGgiLCJmbG9vciIsIkRhdGUiLCJub3ciLCJtaWxsaXNlY29uZHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/timespan.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js":
/*!****************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js ***!
  \****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const ASYMMETRIC_KEY_DETAILS_SUPPORTED = __webpack_require__(/*! ./asymmetricKeyDetailsSupported */ \"(rsc)/./node_modules/jsonwebtoken/lib/asymmetricKeyDetailsSupported.js\");\nconst RSA_PSS_KEY_DETAILS_SUPPORTED = __webpack_require__(/*! ./rsaPssKeyDetailsSupported */ \"(rsc)/./node_modules/jsonwebtoken/lib/rsaPssKeyDetailsSupported.js\");\nconst allowedAlgorithmsForKeys = {\n    \"ec\": [\n        \"ES256\",\n        \"ES384\",\n        \"ES512\"\n    ],\n    \"rsa\": [\n        \"RS256\",\n        \"PS256\",\n        \"RS384\",\n        \"PS384\",\n        \"RS512\",\n        \"PS512\"\n    ],\n    \"rsa-pss\": [\n        \"PS256\",\n        \"PS384\",\n        \"PS512\"\n    ]\n};\nconst allowedCurves = {\n    ES256: \"prime256v1\",\n    ES384: \"secp384r1\",\n    ES512: \"secp521r1\"\n};\nmodule.exports = function(algorithm, key) {\n    if (!algorithm || !key) return;\n    const keyType = key.asymmetricKeyType;\n    if (!keyType) return;\n    const allowedAlgorithms = allowedAlgorithmsForKeys[keyType];\n    if (!allowedAlgorithms) {\n        throw new Error(`Unknown key type \"${keyType}\".`);\n    }\n    if (!allowedAlgorithms.includes(algorithm)) {\n        throw new Error(`\"alg\" parameter for \"${keyType}\" key type must be one of: ${allowedAlgorithms.join(\", \")}.`);\n    }\n    /*\n   * Ignore the next block from test coverage because it gets executed\n   * conditionally depending on the Node version. Not ignoring it would\n   * prevent us from reaching the target % of coverage for versions of\n   * Node under 15.7.0.\n   */ /* istanbul ignore next */ if (ASYMMETRIC_KEY_DETAILS_SUPPORTED) {\n        switch(keyType){\n            case \"ec\":\n                const keyCurve = key.asymmetricKeyDetails.namedCurve;\n                const allowedCurve = allowedCurves[algorithm];\n                if (keyCurve !== allowedCurve) {\n                    throw new Error(`\"alg\" parameter \"${algorithm}\" requires curve \"${allowedCurve}\".`);\n                }\n                break;\n            case \"rsa-pss\":\n                if (RSA_PSS_KEY_DETAILS_SUPPORTED) {\n                    const length = parseInt(algorithm.slice(-3), 10);\n                    const { hashAlgorithm, mgf1HashAlgorithm, saltLength } = key.asymmetricKeyDetails;\n                    if (hashAlgorithm !== `sha${length}` || mgf1HashAlgorithm !== hashAlgorithm) {\n                        throw new Error(`Invalid key for this operation, its RSA-PSS parameters do not meet the requirements of \"alg\" ${algorithm}.`);\n                    }\n                    if (saltLength !== undefined && saltLength > length >> 3) {\n                        throw new Error(`Invalid key for this operation, its RSA-PSS parameter saltLength does not meet the requirements of \"alg\" ${algorithm}.`);\n                    }\n                }\n                break;\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/sign.js":
/*!*******************************************!*\
  !*** ./node_modules/jsonwebtoken/sign.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const timespan = __webpack_require__(/*! ./lib/timespan */ \"(rsc)/./node_modules/jsonwebtoken/lib/timespan.js\");\nconst PS_SUPPORTED = __webpack_require__(/*! ./lib/psSupported */ \"(rsc)/./node_modules/jsonwebtoken/lib/psSupported.js\");\nconst validateAsymmetricKey = __webpack_require__(/*! ./lib/validateAsymmetricKey */ \"(rsc)/./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js\");\nconst jws = __webpack_require__(/*! jws */ \"(rsc)/./node_modules/jws/index.js\");\nconst includes = __webpack_require__(/*! lodash.includes */ \"(rsc)/./node_modules/lodash.includes/index.js\");\nconst isBoolean = __webpack_require__(/*! lodash.isboolean */ \"(rsc)/./node_modules/lodash.isboolean/index.js\");\nconst isInteger = __webpack_require__(/*! lodash.isinteger */ \"(rsc)/./node_modules/lodash.isinteger/index.js\");\nconst isNumber = __webpack_require__(/*! lodash.isnumber */ \"(rsc)/./node_modules/lodash.isnumber/index.js\");\nconst isPlainObject = __webpack_require__(/*! lodash.isplainobject */ \"(rsc)/./node_modules/lodash.isplainobject/index.js\");\nconst isString = __webpack_require__(/*! lodash.isstring */ \"(rsc)/./node_modules/lodash.isstring/index.js\");\nconst once = __webpack_require__(/*! lodash.once */ \"(rsc)/./node_modules/lodash.once/index.js\");\nconst { KeyObject, createSecretKey, createPrivateKey } = __webpack_require__(/*! crypto */ \"crypto\");\nconst SUPPORTED_ALGS = [\n    \"RS256\",\n    \"RS384\",\n    \"RS512\",\n    \"ES256\",\n    \"ES384\",\n    \"ES512\",\n    \"HS256\",\n    \"HS384\",\n    \"HS512\",\n    \"none\"\n];\nif (PS_SUPPORTED) {\n    SUPPORTED_ALGS.splice(3, 0, \"PS256\", \"PS384\", \"PS512\");\n}\nconst sign_options_schema = {\n    expiresIn: {\n        isValid: function(value) {\n            return isInteger(value) || isString(value) && value;\n        },\n        message: '\"expiresIn\" should be a number of seconds or string representing a timespan'\n    },\n    notBefore: {\n        isValid: function(value) {\n            return isInteger(value) || isString(value) && value;\n        },\n        message: '\"notBefore\" should be a number of seconds or string representing a timespan'\n    },\n    audience: {\n        isValid: function(value) {\n            return isString(value) || Array.isArray(value);\n        },\n        message: '\"audience\" must be a string or array'\n    },\n    algorithm: {\n        isValid: includes.bind(null, SUPPORTED_ALGS),\n        message: '\"algorithm\" must be a valid string enum value'\n    },\n    header: {\n        isValid: isPlainObject,\n        message: '\"header\" must be an object'\n    },\n    encoding: {\n        isValid: isString,\n        message: '\"encoding\" must be a string'\n    },\n    issuer: {\n        isValid: isString,\n        message: '\"issuer\" must be a string'\n    },\n    subject: {\n        isValid: isString,\n        message: '\"subject\" must be a string'\n    },\n    jwtid: {\n        isValid: isString,\n        message: '\"jwtid\" must be a string'\n    },\n    noTimestamp: {\n        isValid: isBoolean,\n        message: '\"noTimestamp\" must be a boolean'\n    },\n    keyid: {\n        isValid: isString,\n        message: '\"keyid\" must be a string'\n    },\n    mutatePayload: {\n        isValid: isBoolean,\n        message: '\"mutatePayload\" must be a boolean'\n    },\n    allowInsecureKeySizes: {\n        isValid: isBoolean,\n        message: '\"allowInsecureKeySizes\" must be a boolean'\n    },\n    allowInvalidAsymmetricKeyTypes: {\n        isValid: isBoolean,\n        message: '\"allowInvalidAsymmetricKeyTypes\" must be a boolean'\n    }\n};\nconst registered_claims_schema = {\n    iat: {\n        isValid: isNumber,\n        message: '\"iat\" should be a number of seconds'\n    },\n    exp: {\n        isValid: isNumber,\n        message: '\"exp\" should be a number of seconds'\n    },\n    nbf: {\n        isValid: isNumber,\n        message: '\"nbf\" should be a number of seconds'\n    }\n};\nfunction validate(schema, allowUnknown, object, parameterName) {\n    if (!isPlainObject(object)) {\n        throw new Error('Expected \"' + parameterName + '\" to be a plain object.');\n    }\n    Object.keys(object).forEach(function(key) {\n        const validator = schema[key];\n        if (!validator) {\n            if (!allowUnknown) {\n                throw new Error('\"' + key + '\" is not allowed in \"' + parameterName + '\"');\n            }\n            return;\n        }\n        if (!validator.isValid(object[key])) {\n            throw new Error(validator.message);\n        }\n    });\n}\nfunction validateOptions(options) {\n    return validate(sign_options_schema, false, options, \"options\");\n}\nfunction validatePayload(payload) {\n    return validate(registered_claims_schema, true, payload, \"payload\");\n}\nconst options_to_payload = {\n    \"audience\": \"aud\",\n    \"issuer\": \"iss\",\n    \"subject\": \"sub\",\n    \"jwtid\": \"jti\"\n};\nconst options_for_objects = [\n    \"expiresIn\",\n    \"notBefore\",\n    \"noTimestamp\",\n    \"audience\",\n    \"issuer\",\n    \"subject\",\n    \"jwtid\"\n];\nmodule.exports = function(payload, secretOrPrivateKey, options, callback) {\n    if (typeof options === \"function\") {\n        callback = options;\n        options = {};\n    } else {\n        options = options || {};\n    }\n    const isObjectPayload = typeof payload === \"object\" && !Buffer.isBuffer(payload);\n    const header = Object.assign({\n        alg: options.algorithm || \"HS256\",\n        typ: isObjectPayload ? \"JWT\" : undefined,\n        kid: options.keyid\n    }, options.header);\n    function failure(err) {\n        if (callback) {\n            return callback(err);\n        }\n        throw err;\n    }\n    if (!secretOrPrivateKey && options.algorithm !== \"none\") {\n        return failure(new Error(\"secretOrPrivateKey must have a value\"));\n    }\n    if (secretOrPrivateKey != null && !(secretOrPrivateKey instanceof KeyObject)) {\n        try {\n            secretOrPrivateKey = createPrivateKey(secretOrPrivateKey);\n        } catch (_) {\n            try {\n                secretOrPrivateKey = createSecretKey(typeof secretOrPrivateKey === \"string\" ? Buffer.from(secretOrPrivateKey) : secretOrPrivateKey);\n            } catch (_) {\n                return failure(new Error(\"secretOrPrivateKey is not valid key material\"));\n            }\n        }\n    }\n    if (header.alg.startsWith(\"HS\") && secretOrPrivateKey.type !== \"secret\") {\n        return failure(new Error(`secretOrPrivateKey must be a symmetric key when using ${header.alg}`));\n    } else if (/^(?:RS|PS|ES)/.test(header.alg)) {\n        if (secretOrPrivateKey.type !== \"private\") {\n            return failure(new Error(`secretOrPrivateKey must be an asymmetric key when using ${header.alg}`));\n        }\n        if (!options.allowInsecureKeySizes && !header.alg.startsWith(\"ES\") && secretOrPrivateKey.asymmetricKeyDetails !== undefined && //KeyObject.asymmetricKeyDetails is supported in Node 15+\n        secretOrPrivateKey.asymmetricKeyDetails.modulusLength < 2048) {\n            return failure(new Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${header.alg}`));\n        }\n    }\n    if (typeof payload === \"undefined\") {\n        return failure(new Error(\"payload is required\"));\n    } else if (isObjectPayload) {\n        try {\n            validatePayload(payload);\n        } catch (error) {\n            return failure(error);\n        }\n        if (!options.mutatePayload) {\n            payload = Object.assign({}, payload);\n        }\n    } else {\n        const invalid_options = options_for_objects.filter(function(opt) {\n            return typeof options[opt] !== \"undefined\";\n        });\n        if (invalid_options.length > 0) {\n            return failure(new Error(\"invalid \" + invalid_options.join(\",\") + \" option for \" + typeof payload + \" payload\"));\n        }\n    }\n    if (typeof payload.exp !== \"undefined\" && typeof options.expiresIn !== \"undefined\") {\n        return failure(new Error('Bad \"options.expiresIn\" option the payload already has an \"exp\" property.'));\n    }\n    if (typeof payload.nbf !== \"undefined\" && typeof options.notBefore !== \"undefined\") {\n        return failure(new Error('Bad \"options.notBefore\" option the payload already has an \"nbf\" property.'));\n    }\n    try {\n        validateOptions(options);\n    } catch (error) {\n        return failure(error);\n    }\n    if (!options.allowInvalidAsymmetricKeyTypes) {\n        try {\n            validateAsymmetricKey(header.alg, secretOrPrivateKey);\n        } catch (error) {\n            return failure(error);\n        }\n    }\n    const timestamp = payload.iat || Math.floor(Date.now() / 1000);\n    if (options.noTimestamp) {\n        delete payload.iat;\n    } else if (isObjectPayload) {\n        payload.iat = timestamp;\n    }\n    if (typeof options.notBefore !== \"undefined\") {\n        try {\n            payload.nbf = timespan(options.notBefore, timestamp);\n        } catch (err) {\n            return failure(err);\n        }\n        if (typeof payload.nbf === \"undefined\") {\n            return failure(new Error('\"notBefore\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60'));\n        }\n    }\n    if (typeof options.expiresIn !== \"undefined\" && typeof payload === \"object\") {\n        try {\n            payload.exp = timespan(options.expiresIn, timestamp);\n        } catch (err) {\n            return failure(err);\n        }\n        if (typeof payload.exp === \"undefined\") {\n            return failure(new Error('\"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60'));\n        }\n    }\n    Object.keys(options_to_payload).forEach(function(key) {\n        const claim = options_to_payload[key];\n        if (typeof options[key] !== \"undefined\") {\n            if (typeof payload[claim] !== \"undefined\") {\n                return failure(new Error('Bad \"options.' + key + '\" option. The payload already has an \"' + claim + '\" property.'));\n            }\n            payload[claim] = options[key];\n        }\n    });\n    const encoding = options.encoding || \"utf8\";\n    if (typeof callback === \"function\") {\n        callback = callback && once(callback);\n        jws.createSign({\n            header: header,\n            privateKey: secretOrPrivateKey,\n            payload: payload,\n            encoding: encoding\n        }).once(\"error\", callback).once(\"done\", function(signature) {\n            // TODO: Remove in favor of the modulus length check before signing once node 15+ is the minimum supported version\n            if (!options.allowInsecureKeySizes && /^(?:RS|PS)/.test(header.alg) && signature.length < 256) {\n                return callback(new Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${header.alg}`));\n            }\n            callback(null, signature);\n        });\n    } else {\n        let signature = jws.sign({\n            header: header,\n            payload: payload,\n            secret: secretOrPrivateKey,\n            encoding: encoding\n        });\n        // TODO: Remove in favor of the modulus length check before signing once node 15+ is the minimum supported version\n        if (!options.allowInsecureKeySizes && /^(?:RS|PS)/.test(header.alg) && signature.length < 256) {\n            throw new Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${header.alg}`);\n        }\n        return signature;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/sign.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/verify.js":
/*!*********************************************!*\
  !*** ./node_modules/jsonwebtoken/verify.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const JsonWebTokenError = __webpack_require__(/*! ./lib/JsonWebTokenError */ \"(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\");\nconst NotBeforeError = __webpack_require__(/*! ./lib/NotBeforeError */ \"(rsc)/./node_modules/jsonwebtoken/lib/NotBeforeError.js\");\nconst TokenExpiredError = __webpack_require__(/*! ./lib/TokenExpiredError */ \"(rsc)/./node_modules/jsonwebtoken/lib/TokenExpiredError.js\");\nconst decode = __webpack_require__(/*! ./decode */ \"(rsc)/./node_modules/jsonwebtoken/decode.js\");\nconst timespan = __webpack_require__(/*! ./lib/timespan */ \"(rsc)/./node_modules/jsonwebtoken/lib/timespan.js\");\nconst validateAsymmetricKey = __webpack_require__(/*! ./lib/validateAsymmetricKey */ \"(rsc)/./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js\");\nconst PS_SUPPORTED = __webpack_require__(/*! ./lib/psSupported */ \"(rsc)/./node_modules/jsonwebtoken/lib/psSupported.js\");\nconst jws = __webpack_require__(/*! jws */ \"(rsc)/./node_modules/jws/index.js\");\nconst { KeyObject, createSecretKey, createPublicKey } = __webpack_require__(/*! crypto */ \"crypto\");\nconst PUB_KEY_ALGS = [\n    \"RS256\",\n    \"RS384\",\n    \"RS512\"\n];\nconst EC_KEY_ALGS = [\n    \"ES256\",\n    \"ES384\",\n    \"ES512\"\n];\nconst RSA_KEY_ALGS = [\n    \"RS256\",\n    \"RS384\",\n    \"RS512\"\n];\nconst HS_ALGS = [\n    \"HS256\",\n    \"HS384\",\n    \"HS512\"\n];\nif (PS_SUPPORTED) {\n    PUB_KEY_ALGS.splice(PUB_KEY_ALGS.length, 0, \"PS256\", \"PS384\", \"PS512\");\n    RSA_KEY_ALGS.splice(RSA_KEY_ALGS.length, 0, \"PS256\", \"PS384\", \"PS512\");\n}\nmodule.exports = function(jwtString, secretOrPublicKey, options, callback) {\n    if (typeof options === \"function\" && !callback) {\n        callback = options;\n        options = {};\n    }\n    if (!options) {\n        options = {};\n    }\n    //clone this object since we are going to mutate it.\n    options = Object.assign({}, options);\n    let done;\n    if (callback) {\n        done = callback;\n    } else {\n        done = function(err, data) {\n            if (err) throw err;\n            return data;\n        };\n    }\n    if (options.clockTimestamp && typeof options.clockTimestamp !== \"number\") {\n        return done(new JsonWebTokenError(\"clockTimestamp must be a number\"));\n    }\n    if (options.nonce !== undefined && (typeof options.nonce !== \"string\" || options.nonce.trim() === \"\")) {\n        return done(new JsonWebTokenError(\"nonce must be a non-empty string\"));\n    }\n    if (options.allowInvalidAsymmetricKeyTypes !== undefined && typeof options.allowInvalidAsymmetricKeyTypes !== \"boolean\") {\n        return done(new JsonWebTokenError(\"allowInvalidAsymmetricKeyTypes must be a boolean\"));\n    }\n    const clockTimestamp = options.clockTimestamp || Math.floor(Date.now() / 1000);\n    if (!jwtString) {\n        return done(new JsonWebTokenError(\"jwt must be provided\"));\n    }\n    if (typeof jwtString !== \"string\") {\n        return done(new JsonWebTokenError(\"jwt must be a string\"));\n    }\n    const parts = jwtString.split(\".\");\n    if (parts.length !== 3) {\n        return done(new JsonWebTokenError(\"jwt malformed\"));\n    }\n    let decodedToken;\n    try {\n        decodedToken = decode(jwtString, {\n            complete: true\n        });\n    } catch (err) {\n        return done(err);\n    }\n    if (!decodedToken) {\n        return done(new JsonWebTokenError(\"invalid token\"));\n    }\n    const header = decodedToken.header;\n    let getSecret;\n    if (typeof secretOrPublicKey === \"function\") {\n        if (!callback) {\n            return done(new JsonWebTokenError(\"verify must be called asynchronous if secret or public key is provided as a callback\"));\n        }\n        getSecret = secretOrPublicKey;\n    } else {\n        getSecret = function(header, secretCallback) {\n            return secretCallback(null, secretOrPublicKey);\n        };\n    }\n    return getSecret(header, function(err, secretOrPublicKey) {\n        if (err) {\n            return done(new JsonWebTokenError(\"error in secret or public key callback: \" + err.message));\n        }\n        const hasSignature = parts[2].trim() !== \"\";\n        if (!hasSignature && secretOrPublicKey) {\n            return done(new JsonWebTokenError(\"jwt signature is required\"));\n        }\n        if (hasSignature && !secretOrPublicKey) {\n            return done(new JsonWebTokenError(\"secret or public key must be provided\"));\n        }\n        if (!hasSignature && !options.algorithms) {\n            return done(new JsonWebTokenError('please specify \"none\" in \"algorithms\" to verify unsigned tokens'));\n        }\n        if (secretOrPublicKey != null && !(secretOrPublicKey instanceof KeyObject)) {\n            try {\n                secretOrPublicKey = createPublicKey(secretOrPublicKey);\n            } catch (_) {\n                try {\n                    secretOrPublicKey = createSecretKey(typeof secretOrPublicKey === \"string\" ? Buffer.from(secretOrPublicKey) : secretOrPublicKey);\n                } catch (_) {\n                    return done(new JsonWebTokenError(\"secretOrPublicKey is not valid key material\"));\n                }\n            }\n        }\n        if (!options.algorithms) {\n            if (secretOrPublicKey.type === \"secret\") {\n                options.algorithms = HS_ALGS;\n            } else if ([\n                \"rsa\",\n                \"rsa-pss\"\n            ].includes(secretOrPublicKey.asymmetricKeyType)) {\n                options.algorithms = RSA_KEY_ALGS;\n            } else if (secretOrPublicKey.asymmetricKeyType === \"ec\") {\n                options.algorithms = EC_KEY_ALGS;\n            } else {\n                options.algorithms = PUB_KEY_ALGS;\n            }\n        }\n        if (options.algorithms.indexOf(decodedToken.header.alg) === -1) {\n            return done(new JsonWebTokenError(\"invalid algorithm\"));\n        }\n        if (header.alg.startsWith(\"HS\") && secretOrPublicKey.type !== \"secret\") {\n            return done(new JsonWebTokenError(`secretOrPublicKey must be a symmetric key when using ${header.alg}`));\n        } else if (/^(?:RS|PS|ES)/.test(header.alg) && secretOrPublicKey.type !== \"public\") {\n            return done(new JsonWebTokenError(`secretOrPublicKey must be an asymmetric key when using ${header.alg}`));\n        }\n        if (!options.allowInvalidAsymmetricKeyTypes) {\n            try {\n                validateAsymmetricKey(header.alg, secretOrPublicKey);\n            } catch (e) {\n                return done(e);\n            }\n        }\n        let valid;\n        try {\n            valid = jws.verify(jwtString, decodedToken.header.alg, secretOrPublicKey);\n        } catch (e) {\n            return done(e);\n        }\n        if (!valid) {\n            return done(new JsonWebTokenError(\"invalid signature\"));\n        }\n        const payload = decodedToken.payload;\n        if (typeof payload.nbf !== \"undefined\" && !options.ignoreNotBefore) {\n            if (typeof payload.nbf !== \"number\") {\n                return done(new JsonWebTokenError(\"invalid nbf value\"));\n            }\n            if (payload.nbf > clockTimestamp + (options.clockTolerance || 0)) {\n                return done(new NotBeforeError(\"jwt not active\", new Date(payload.nbf * 1000)));\n            }\n        }\n        if (typeof payload.exp !== \"undefined\" && !options.ignoreExpiration) {\n            if (typeof payload.exp !== \"number\") {\n                return done(new JsonWebTokenError(\"invalid exp value\"));\n            }\n            if (clockTimestamp >= payload.exp + (options.clockTolerance || 0)) {\n                return done(new TokenExpiredError(\"jwt expired\", new Date(payload.exp * 1000)));\n            }\n        }\n        if (options.audience) {\n            const audiences = Array.isArray(options.audience) ? options.audience : [\n                options.audience\n            ];\n            const target = Array.isArray(payload.aud) ? payload.aud : [\n                payload.aud\n            ];\n            const match = target.some(function(targetAudience) {\n                return audiences.some(function(audience) {\n                    return audience instanceof RegExp ? audience.test(targetAudience) : audience === targetAudience;\n                });\n            });\n            if (!match) {\n                return done(new JsonWebTokenError(\"jwt audience invalid. expected: \" + audiences.join(\" or \")));\n            }\n        }\n        if (options.issuer) {\n            const invalid_issuer = typeof options.issuer === \"string\" && payload.iss !== options.issuer || Array.isArray(options.issuer) && options.issuer.indexOf(payload.iss) === -1;\n            if (invalid_issuer) {\n                return done(new JsonWebTokenError(\"jwt issuer invalid. expected: \" + options.issuer));\n            }\n        }\n        if (options.subject) {\n            if (payload.sub !== options.subject) {\n                return done(new JsonWebTokenError(\"jwt subject invalid. expected: \" + options.subject));\n            }\n        }\n        if (options.jwtid) {\n            if (payload.jti !== options.jwtid) {\n                return done(new JsonWebTokenError(\"jwt jwtid invalid. expected: \" + options.jwtid));\n            }\n        }\n        if (options.nonce) {\n            if (payload.nonce !== options.nonce) {\n                return done(new JsonWebTokenError(\"jwt nonce invalid. expected: \" + options.nonce));\n            }\n        }\n        if (options.maxAge) {\n            if (typeof payload.iat !== \"number\") {\n                return done(new JsonWebTokenError(\"iat required when maxAge is specified\"));\n            }\n            const maxAgeTimestamp = timespan(options.maxAge, payload.iat);\n            if (typeof maxAgeTimestamp === \"undefined\") {\n                return done(new JsonWebTokenError('\"maxAge\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60'));\n            }\n            if (clockTimestamp >= maxAgeTimestamp + (options.clockTolerance || 0)) {\n                return done(new TokenExpiredError(\"maxAge exceeded\", new Date(maxAgeTimestamp * 1000)));\n            }\n        }\n        if (options.complete === true) {\n            const signature = decodedToken.signature;\n            return done(null, {\n                header: header,\n                payload: payload,\n                signature: signature\n            });\n        }\n        return done(null, payload);\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/verify.js\n");

/***/ })

};
;