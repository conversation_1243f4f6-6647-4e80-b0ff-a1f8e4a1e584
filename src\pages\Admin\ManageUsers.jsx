import React, { useState, useEffect } from 'react';
import { <PERSON>, But<PERSON>, Alert, Badge, Modal, Form } from 'react-bootstrap';
import { adminAPI, supedAdminAPI } from '../../services/api';
import { Trash2, UserCog, Shield, AlertTriangle } from 'lucide-react';
import { useAuth } from '../../context/AuthContext';

const ManageUsers = () => {
  const { user: currentUser, isSuperAdmin } = useAuth();
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showRoleModal, setShowRoleModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [newRole, setNewRole] = useState('');

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
  try {
    const response = isSuperAdmin ? await supedAdminAPI.getUsers() : await adminAPI.getUsers();
    setUsers(Array.isArray(response.data) ? response.data : []); // الحل هنا
  } catch (err) {
    setError('فشل في تحميل المستخدمين');
    setUsers([]); // في حالة الخطأ، اجعلها مصفوفة فارغة
  } finally {
    setLoading(false);
  }
};

  const handleDeleteClick = (user) => {
    setSelectedUser(user);
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      await supedAdminAPI.deleteUser(selectedUser._id);
      setShowDeleteModal(false);
      setSelectedUser(null);
      fetchUsers(); // Refresh the list
    } catch (err) {
      setError('فشل في حذف المستخدم');
    }
  };

  const handleRoleClick = (user) => {
    setSelectedUser(user);
    setNewRole(user.role);
    setShowRoleModal(true);
  };

  const handleRoleUpdate = async () => {
    try {
      await supedAdminAPI.updateUserRole(selectedUser._id, newRole);
      setShowRoleModal(false);
      setSelectedUser(null);
      fetchUsers(); // Refresh the list
    } catch (err) {
      setError('فشل في تحديث دور المستخدم');
    }
  };

  const getRoleBadgeVariant = (role) => {
    switch (role) {
      case 'super-admin': return 'danger';
      case 'admin': return 'warning';
      case 'student': return 'primary';
      default: return 'secondary';
    }
  };

  const getRoleLabel = (role) => {
    switch (role) {
      case 'super-admin': return 'مدير عام';
      case 'admin': return 'مدير';
      case 'student': return 'طالب';
      default: return role;
    }
  };

  if (loading) {
    return (
      <div className="text-center py-5">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  // Only super admins can access this page
  if (!isSuperAdmin) {
    return (
      <div className="text-center py-5">
        <AlertTriangle size={64} className="text-warning mb-3" />
        <h3>غير مصرح لك بالوصول</h3>
        <p className="text-muted">هذه الصفحة متاحة للمديرين العامين فقط</p>
        <Button variant="primary" href="/admin">
          العودة للوحة التحكم
        </Button>
      </div>
    );
  }

  return (
    <div>
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h2 className="fw-bold">إدارة المستخدمين</h2>
          <div className="d-flex align-items-center gap-2">
            <Shield size={16} className="text-danger" />
            <span className="text-muted">صلاحيات المدير العام فقط</span>
          </div>
        </div>
      </div>

      {error && (
        <Alert variant="danger" dismissible onClose={() => setError('')}>
          {error}
        </Alert>
      )}

      <div className="bg-white rounded shadow-sm">
        <Table responsive hover className="mb-0">
          <thead className="bg-light">
            <tr>
              <th>الاسم</th>
              <th>البريد الإلكتروني</th>
              <th>الدور</th>
              <th>آخر نشاط</th>
              <th>تاريخ التسجيل</th>
              <th>الإجراءات</th>
            </tr>
          </thead>
          <tbody>
            {users.map((user) => {
              const isCurrentUser = user._id === currentUser?._id;
              const canModify = !isCurrentUser; // Can't modify own account

              return (
                <tr key={user._id} className={isCurrentUser ? 'table-warning' : ''}>
                  <td className="fw-semibold">
                    {user.name}
                    {isCurrentUser && <Badge bg="info" className="ms-2">أنت</Badge>}
                  </td>
                  <td>{user.email}</td>
                  <td>
                    <Badge bg={getRoleBadgeVariant(user.role)} className="d-flex align-items-center gap-1 w-fit">
                      {user.role === 'super-admin' && <Shield size={12} />}
                      {getRoleLabel(user.role)}
                    </Badge>
                  </td>
                  <td>{user.lastActive ? new Date(user.lastActive).toLocaleString('ar-SA') : 'غير متاح'}</td>
                  <td>{new Date(user.createdAt).toLocaleString('ar-SA')}</td>
                  <td>
                    <div className="d-flex gap-2">
                      <Button
                        variant="outline-primary"
                        size="sm"
                        onClick={() => handleRoleClick(user)}
                        disabled={!canModify}
                        title={canModify ? "تغيير الدور" : "لا يمكن تعديل حسابك الخاص"}
                      >
                        <UserCog size={16} />
                      </Button>
                      <Button
                        variant="outline-danger"
                        size="sm"
                        onClick={() => handleDeleteClick(user)}
                        disabled={!canModify}
                        title={canModify ? "حذف المستخدم" : "لا يمكن حذف حسابك الخاص"}
                      >
                        <Trash2 size={16} />
                      </Button>
                    </div>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </Table>
        
        {users.length === 0 && (
          <div className="text-center py-5 text-muted">
            <h5>لا يوجد مستخدمون</h5>
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title className="text-danger">حذف المستخدم</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="alert alert-danger">
            <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه!
          </div>
          هل أنت متأكد من حذف المستخدم "{selectedUser?.name}"؟
          <br />
          <small className="text-muted">
            سيتم حذف جميع البيانات المرتبطة بهذا المستخدم نهائياً.
          </small>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
            إلغاء
          </Button>
          <Button variant="danger" onClick={handleDeleteConfirm}>
            حذف المستخدم
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Role Update Modal */}
      <Modal show={showRoleModal} onHide={() => setShowRoleModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>تحديث دور المستخدم</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p>تحديث دور المستخدم "{selectedUser?.name}":</p>
          <Form.Select
            value={newRole}
            onChange={(e) => setNewRole(e.target.value)}
          >
            <option value="student">طالب</option>
            <option value="admin">مدير</option>
            <option value="super-admin">مدير عام</option>
          </Form.Select>
          <div className="mt-3">
            <small className="text-muted">
              <strong>ملاحظة:</strong> تغيير الدور سيؤثر على صلاحيات المستخدم في النظام.
            </small>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowRoleModal(false)}>
            إلغاء
          </Button>
          <Button variant="primary" onClick={handleRoleUpdate}>
            تحديث الدور
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default ManageUsers;