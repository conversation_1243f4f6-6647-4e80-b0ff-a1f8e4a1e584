'use client';

import { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Form, Button, Badge, Tab, Tabs, ProgressBar, Alert } from 'react-bootstrap';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import { useRouter } from 'next/navigation';
import { 
  User, 
  Mail, 
  Calendar, 
  Award, 
  BookOpen, 
  TrendingUp, 
  Edit3, 
  Save,
  Camera,
  Star,
  Clock,
  Target
} from 'lucide-react';
import { toast } from 'react-toastify';

export default function Profile() {
  const { user, updateProfile, isAuthenticated, loading: authLoading } = useAuth();
  const { isDark } = useTheme();
  const router = useRouter();
  
  const [activeTab, setActiveTab] = useState('overview');
  const [editing, setEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    preferences: {
      theme: 'light',
      language: 'ar',
      notifications: true
    }
  });
  const [stats, setStats] = useState({
    completedCourses: 0,
    totalPoints: 0,
    achievements: [],
    currentStreak: 0,
    totalHours: 0
  });

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/login');
      return;
    }

    if (user) {
      setFormData({
        name: user.name || '',
        email: user.email || '',
        preferences: {
          theme: user.preferences?.theme || 'light',
          language: user.preferences?.language || 'ar',
          notifications: user.preferences?.notifications ?? true
        }
      });

      // جلب إحصائيات المستخدم الحقيقية
      try {
        const enrollmentsResponse =  api.get('/enrollments');
        const enrollments = enrollmentsResponse.data.enrollments || [];
        const enrollmentStats = enrollmentsResponse.data.stats || {};

        setStats({
          completedCourses: enrollmentStats.completed || 0,
          totalPoints: user.totalPoints || 0,
          achievements: user.achievements || [],
          currentStreak: user.stats?.currentStreak || 0,
          totalHours: enrollmentStats.totalStudyHours || 0
        });
      } catch (error) {
        console.error('Error fetching user stats:', error);
        // استخدام القيم الافتراضية في حالة الخطأ
        setStats({
          completedCourses: 0,
          totalPoints: 0,
          achievements: [],
          currentStreak: 0,
          totalHours: 0
        });
      }
    }
  }, [user, isAuthenticated, authLoading, router]);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: type === 'checkbox' ? checked : value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : value
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const result = await updateProfile({
        name: formData.name,
        preferences: formData.preferences
      });

      if (result.success) {
        toast.success(result.message);
        setEditing(false);
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      toast.error('حدث خطأ في تحديث البيانات');
    } finally {
      setLoading(false);
    }
  };

  if (authLoading) {
    return (
      <div className="d-flex justify-content-center align-items-center min-vh-100">
        <div className="spinner-border text-primary" />
      </div>
    );
  }

  if (!user) return null;

  return (
    <div className={`min-vh-100 ${isDark ? 'bg-dark text-light' : 'bg-light'}`}>
      <Container className="py-5">
        {/* Header */}
        <Row className="mb-4">
          <Col>
            <Card className={`border-0 shadow-sm ${isDark ? 'bg-dark text-light' : ''}`}>
              <Card.Body>
                <Row className="align-items-center">
                  <Col md={2} className="text-center">
                    <div className="position-relative d-inline-block">
                      <div className="bg-primary rounded-circle d-flex align-items-center justify-content-center" 
                           style={{ width: '80px', height: '80px' }}>
                        <User size={40} className="text-white" />
                      </div>
                      <Button
                        variant="primary"
                        size="sm"
                        className="position-absolute bottom-0 end-0 rounded-circle p-1"
                        style={{ width: '30px', height: '30px' }}
                      >
                        <Camera size={14} />
                      </Button>
                    </div>
                  </Col>
                  <Col md={8}>
                    <h3 className="mb-1">{user.name}</h3>
                    <p className="text-muted mb-2">
                      <Mail size={16} className="me-1" />
                      {user.email}
                    </p>
                    <div className="d-flex gap-2 flex-wrap">
                      <Badge bg="primary">{user.role === 'student' ? 'طالب' : user.role === 'admin' ? 'مدير' : 'مدير عام'}</Badge>
                      <Badge bg="success">نشط</Badge>
                      <Badge bg="info">
                        <Calendar size={12} className="me-1" />
                        انضم في {new Date(user.createdAt).toLocaleDateString('ar-SA')}
                      </Badge>
                    </div>
                  </Col>
                  <Col md={2} className="text-end">
                    <Button
                      variant={editing ? "success" : "outline-primary"}
                      onClick={() => setEditing(!editing)}
                      disabled={loading}
                    >
                      {editing ? <Save size={16} /> : <Edit3 size={16} />}
                      <span className="ms-1">{editing ? 'حفظ' : 'تعديل'}</span>
                    </Button>
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          </Col>
        </Row>

        {/* Stats Cards */}
        <Row className="mb-4">
          <Col md={3}>
            <Card className={`text-center border-0 shadow-sm h-100 ${isDark ? 'bg-dark text-light' : ''}`}>
              <Card.Body>
                <BookOpen size={32} className="text-primary mb-2" />
                <h4 className="fw-bold">{stats.completedCourses}</h4>
                <p className="text-muted mb-0">دورة مكتملة</p>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3}>
            <Card className={`text-center border-0 shadow-sm h-100 ${isDark ? 'bg-dark text-light' : ''}`}>
              <Card.Body>
                <Star size={32} className="text-warning mb-2" />
                <h4 className="fw-bold">{stats.totalPoints}</h4>
                <p className="text-muted mb-0">نقطة</p>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3}>
            <Card className={`text-center border-0 shadow-sm h-100 ${isDark ? 'bg-dark text-light' : ''}`}>
              <Card.Body>
                <TrendingUp size={32} className="text-success mb-2" />
                <h4 className="fw-bold">{stats.currentStreak}</h4>
                <p className="text-muted mb-0">يوم متتالي</p>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3}>
            <Card className={`text-center border-0 shadow-sm h-100 ${isDark ? 'bg-dark text-light' : ''}`}>
              <Card.Body>
                <Clock size={32} className="text-info mb-2" />
                <h4 className="fw-bold">{stats.totalHours}</h4>
                <p className="text-muted mb-0">ساعة تعلم</p>
              </Card.Body>
            </Card>
          </Col>
        </Row>

        {/* Tabs */}
        <Row>
          <Col>
            <Card className={`border-0 shadow-sm ${isDark ? 'bg-dark text-light' : ''}`}>
              <Card.Body>
                <Tabs
                  activeKey={activeTab}
                  onSelect={setActiveTab}
                  className="mb-4"
                >
                  <Tab eventKey="overview" title="نظرة عامة">
                    <Row>
                      <Col md={6}>
                        <h5 className="mb-3">الإنجازات الأخيرة</h5>
                        {stats.achievements.length > 0 ? (
                          stats.achievements.map((achievement, index) => (
                            <div key={index} className="d-flex align-items-center mb-2">
                              <Award size={20} className="text-warning me-2" />
                              <span>{achievement}</span>
                            </div>
                          ))
                        ) : (
                          <Alert variant="info">
                            لم تحصل على أي إنجازات بعد. ابدأ بإكمال دورة!
                          </Alert>
                        )}
                      </Col>
                      <Col md={6}>
                        <h5 className="mb-3">التقدم الحالي</h5>
                        <div className="mb-3">
                          <div className="d-flex justify-content-between mb-1">
                            <small>التقدم الإجمالي</small>
                            <small>75%</small>
                          </div>
                          <ProgressBar now={75} variant="primary" />
                        </div>
                        <div className="mb-3">
                          <div className="d-flex justify-content-between mb-1">
                            <small>الهدف الشهري</small>
                            <small>3/5 دورات</small>
                          </div>
                          <ProgressBar now={60} variant="success" />
                        </div>
                      </Col>
                    </Row>
                  </Tab>

                  <Tab eventKey="settings" title="الإعدادات">
                    <Form onSubmit={handleSubmit}>
                      <Row>
                        <Col md={6}>
                          <h5 className="mb-3">المعلومات الشخصية</h5>
                          <Form.Group className="mb-3">
                            <Form.Label>الاسم الكامل</Form.Label>
                            <Form.Control
                              type="text"
                              name="name"
                              value={formData.name}
                              onChange={handleInputChange}
                              disabled={!editing}
                            />
                          </Form.Group>
                          <Form.Group className="mb-3">
                            <Form.Label>البريد الإلكتروني</Form.Label>
                            <Form.Control
                              type="email"
                              value={formData.email}
                              disabled
                              className="bg-light"
                            />
                            <Form.Text className="text-muted">
                              لا يمكن تغيير البريد الإلكتروني
                            </Form.Text>
                          </Form.Group>
                        </Col>
                        <Col md={6}>
                          <h5 className="mb-3">التفضيلات</h5>
                          <Form.Group className="mb-3">
                            <Form.Label>المظهر</Form.Label>
                            <Form.Select
                              name="preferences.theme"
                              value={formData.preferences.theme}
                              onChange={handleInputChange}
                              disabled={!editing}
                            >
                              <option value="light">فاتح</option>
                              <option value="dark">مظلم</option>
                            </Form.Select>
                          </Form.Group>
                          <Form.Group className="mb-3">
                            <Form.Label>اللغة</Form.Label>
                            <Form.Select
                              name="preferences.language"
                              value={formData.preferences.language}
                              onChange={handleInputChange}
                              disabled={!editing}
                            >
                              <option value="ar">العربية</option>
                              <option value="en">English</option>
                            </Form.Select>
                          </Form.Group>
                          <Form.Group className="mb-3">
                            <Form.Check
                              type="checkbox"
                              name="preferences.notifications"
                              label="تفعيل الإشعارات"
                              checked={formData.preferences.notifications}
                              onChange={handleInputChange}
                              disabled={!editing}
                            />
                          </Form.Group>
                        </Col>
                      </Row>
                      {editing && (
                        <div className="text-end">
                          <Button
                            variant="secondary"
                            className="me-2"
                            onClick={() => setEditing(false)}
                          >
                            إلغاء
                          </Button>
                          <Button
                            type="submit"
                            variant="primary"
                            disabled={loading}
                          >
                            {loading ? 'جاري الحفظ...' : 'حفظ التغييرات'}
                          </Button>
                        </div>
                      )}
                    </Form>
                  </Tab>
                </Tabs>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>
    </div>
  );
}
